
# UPSC SaaS Platform

A comprehensive Software-as-a-Service platform designed for UPSC (Union Public Service Commission) exam preparation, built with modern web technologies.

## 🚀 Features

- **User Management**: Complete user registration, authentication, and profile management
- **Study Materials**: Comprehensive study resources and materials management
- **Exam System**: Full-featured exam creation and management system
- **Daily Quiz**: Interactive daily quiz system with progress tracking
- **Progress Analytics**: Detailed progress tracking and performance analytics using Recharts
- **Admin Dashboard**: Complete administrative interface with statistics and user management
- **Responsive Design**: Mobile-first design built with Tailwind CSS
- **Real-time Notifications**: User notification system
- **Pricing Plans**: Flexible subscription and pricing management
- **Firebase Integration**: Secure authentication and data management

## 🛠️ Tech Stack

### Frontend
- **React 18** - Modern React with hooks and functional components
- **TypeScript** - Type-safe development
- **Vite** - Fast build tool and development server
- **React Router DOM** - Client-side routing
- **Tailwind CSS** - Utility-first CSS framework
- **Lucide React** - Beautiful icon library
- **Recharts** - Data visualization and charts
- **Date-fns** - Modern date utility library

### Backend
- **Node.js** - Runtime environment
- **Express.js** - Web application framework (via api-server)
- **JSON Server** - Mock REST API for development
- **Firebase** - Authentication and backend services

### Development Tools
- **ESLint** - Code linting with TypeScript support
- **Autoprefixer** - CSS vendor prefixing
- **Terser** - JavaScript minification
- **Concurrently** - Run multiple commands simultaneously

## 📦 Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd upsc-saas-platform
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Set up environment variables**
   Create environment files for different stages (development, staging, production) with your Firebase configuration and other necessary variables.

## 🚀 Development

### Available Scripts

- **Development server**
  ```bash
  npm run dev
  ```
  Starts the Vite development server

- **Full development environment**
  ```bash
  npm run dev:full
  ```
  Runs both the API server and frontend development server concurrently

- **API server only**
  ```bash
  npm run api-server
  ```
  Starts the backend API server on port 3001

- **JSON server (mock API)**
  ```bash
  npm run json-server
  ```
  Starts JSON server for development/testing

### Build Commands

- **Production build**
  ```bash
  npm run build
  ```

- **Staging build**
  ```bash
  npm run build:staging
  ```

- **Preview production build**
  ```bash
  npm run preview
  ```

### Code Quality

- **Lint code**
  ```bash
  npm run lint
  ```

## 🌐 Deployment

The project is configured for Google Cloud Platform deployment:

- **Deploy frontend only**
  ```bash
  npm run deploy:frontend
  ```

- **Deploy API only**
  ```bash
  npm run deploy:api
  ```

- **Deploy everything**
  ```bash
  npm run deploy:all
  ```

## 📁 Project Structure

```
upsc-saas-platform/
├── src/                    # Frontend source code
├── api-server/            # Backend API server
│   └── server.mjs        # Express server configuration
├── dist/                 # Production build output
├── public/               # Static assets
├── db.json              # Mock database for development
├── vite.config.ts       # Vite configuration
├── eslint.config.js     # ESLint configuration
├── tailwind.config.js   # Tailwind CSS configuration
├── app.yaml            # Google Cloud App Engine config (frontend)
├── api-service.yaml    # Google Cloud App Engine config (API)
└── package.json        # Project dependencies and scripts
```

## 🗄️ Data Models

The platform manages several key data entities:

- **Users & User Profiles** - User authentication and profile information
- **Study Materials** - Educational content and resources
- **Exams & Questions** - Exam system with question management
- **Daily Quiz** - Daily quiz questions and user progress
- **Testimonials** - User testimonials and reviews
- **Pricing Plans & Purchases** - Subscription management
- **Notifications** - User notification system
- **Admin Stats** - Administrative analytics and statistics

## 🔧 Configuration

### Vite Configuration
- Optimized build with code splitting
- Manual chunks for better caching (vendor, router, firebase, ui)
- Terser minification for production
- Source maps disabled for production builds

### ESLint Configuration
- TypeScript support with recommended rules
- React Hooks plugin for React-specific linting
- React Refresh plugin for development

## 🚀 Performance Optimizations

- **Code Splitting**: Automatic code splitting with manual chunks
- **Tree Shaking**: Dead code elimination
- **Minification**: Terser for JavaScript minification
- **Lazy Loading**: Optimized dependency loading
- **Caching**: Optimized chunk naming for better caching

## 🔐 Security

- Firebase Authentication integration
- Secure API endpoints
- Environment-based configuration
- Input validation and sanitization

## 📱 Responsive Design

Built with mobile-first approach using Tailwind CSS, ensuring optimal experience across all device sizes.

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add some amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📄 License

This project is private and proprietary.

## 🆘 Support

For support and questions, please contact the development team.

---

Built with ❤️ for UPSC aspirants

