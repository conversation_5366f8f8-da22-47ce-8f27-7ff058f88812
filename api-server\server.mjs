import express from 'express';
import cors from 'cors';
import compression from 'compression';
import bodyParser from 'body-parser';
import fs from 'fs';
import path from 'path';
import multer from 'multer';

const app = express();
const PORT = process.env.PORT || 8080;

// Security middleware

app.use(compression());
// CORS configuration
const allowedOrigins = process.env.ALLOWED_ORIGINS
  ? process.env.ALLOWED_ORIGINS.split(',')
  : ['http://localhost:5173'];

app.use(cors({
  origin: allowedOrigins,
  credentials: true
}));

app.use(bodyParser.json());

// Load data from db.json
let data = {};
try {
  const dataPath = path.join(process.cwd(), 'db.json');
  data = JSON.parse(fs.readFileSync(dataPath, 'utf8'));
} catch (err) {
  console.error('Error loading data:', err);
  // Initialize with empty data structure
  data = {
    users: [],
    userProfiles: [],
    studyMaterials: [],
    exams: [],
    questions: [],
    dailyQuizQuestions: [],
    testimonials: [],
    pricingPlans: [],
    purchases: [],
    notifications: [],
    adminStats: [],
    userGrowthData: [],
    progressData: [],
    upcomingExams: [],
    recentMaterials: [],
    userDailyQuizProgress: [],
    dailyQuizSubmissions: []
  };
}

// Generic CRUD routes
const createCRUDRoutes = (resourceName) => {
  const router = express.Router();

  // GET all
  router.get('/', (req, res) => {
    res.json(data[resourceName] || []);
  });

  // GET by ID
  router.get('/:id', (req, res) => {
    const item = data[resourceName]?.find(item => item.id == req.params.id);
    if (!item) {
      return res.status(404).json({ error: 'Item not found' });
    }
    res.json(item);
  });

  // POST create
  router.post('/', (req, res) => {
    if (!data[resourceName]) data[resourceName] = [];
    const newItem = {
      id: Date.now().toString(),
      ...req.body,
      createdAt: new Date().toISOString()
    };
    data[resourceName].push(newItem);
    res.status(201).json(newItem);
  });

  // PUT update
  router.put('/:id', (req, res) => {
    const index = data[resourceName]?.findIndex(item => item.id == req.params.id);
    if (index === -1) {
      return res.status(404).json({ error: 'Item not found' });
    }
    data[resourceName][index] = {
      ...data[resourceName][index],
      ...req.body,
      updatedAt: new Date().toISOString()
    };
    res.json(data[resourceName][index]);
  });

  // DELETE
  router.delete('/:id', (req, res) => {
    const index = data[resourceName]?.findIndex(item => item.id == req.params.id);
    if (index === -1) {
      return res.status(404).json({ error: 'Item not found' });
    }
    data[resourceName].splice(index, 1);
    res.status(204).send();
  });

  return router;
};

// Register routes
const resources = [
  'users', 'userProfiles', 'studyMaterials', 'exams', 'questions',
  'dailyQuizQuestions', 'testimonials', 'pricingPlans', 'purchases',
  'notifications', 'adminStats', 'userGrowthData', 'progressData',
  'upcomingExams', 'recentMaterials', 'userDailyQuizProgress',
  'dailyQuizSubmissions', 'landingStats', 'landingFeatures',
  'demoQuestions', 'newsItems', 'recentActivities', 'examResults'
];

resources.forEach(resource => {
  app.use(`/${resource}`, createCRUDRoutes(resource));
});

// Health check
app.get('/health', (req, res) => {
  res.json({
    status: 'OK',
    timestamp: new Date().toISOString(),
    environment: process.env.NODE_ENV
  });
});

// Add download management endpoints
app.get('/api/secure-download/:downloadId', async (req, res) => {
  try {
    const { downloadId } = req.params;
    const { token } = req.query;

    // Validate token
    const payload = JSON.parse(Buffer.from(token, 'base64').toString());

    // Check expiry (30 minutes)
    if (Date.now() - payload.timestamp > 30 * 60 * 1000) {
      return res.status(410).json({ error: 'Download link expired' });
    }

    // Find the actual material file
    const dbData = JSON.parse(fs.readFileSync('./db.json', 'utf8'));
    const material = dbData.studyMaterials.find(m => m.id === parseInt(payload.materialId));

    if (!material || !material.filePath) {
      return res.status(404).json({ error: 'Material file not found' });
    }

    // Normalize the file path for cross-platform compatibility
    const normalizedPath = path.resolve(material.filePath);

    // Check if file exists
    if (!fs.existsSync(normalizedPath)) {
      // Try alternative path resolution
      const alternativePath = path.join(__dirname, material.filePath);

      if (!fs.existsSync(alternativePath)) {
        return res.status(404).json({ error: 'Physical file not found' });
      }

      // Use alternative path if it exists
      const fileBuffer = fs.readFileSync(alternativePath);
      res.setHeader('Content-Type', 'application/pdf');
      res.setHeader('Content-Disposition', `attachment; filename="${material.originalName}"`);
      res.setHeader('Cache-Control', 'no-cache, no-store, must-revalidate');
      res.setHeader('Pragma', 'no-cache');
      res.setHeader('Expires', '0');
      return res.send(fileBuffer);
    }

    // Serve the file
    const fileBuffer = fs.readFileSync(normalizedPath);

    res.setHeader('Content-Type', 'application/pdf');
    res.setHeader('Content-Disposition', `attachment; filename="${material.originalName}"`);
    res.setHeader('Cache-Control', 'no-cache, no-store, must-revalidate');
    res.setHeader('Pragma', 'no-cache');
    res.setHeader('Expires', '0');

    res.send(fileBuffer);

  } catch (error) {
    console.error('Secure download error:', error);
    res.status(500).json({ error: 'Download failed' });
  }
});

async function generateWatermarkedPDF(payload) {
  // This function is no longer used - keeping for future watermarking implementation
  const watermarkText = `Licensed to: User ${payload.userId} | Download ID: ${payload.downloadId} | ${new Date().toISOString()}`;

  // TODO: Implement proper PDF watermarking using pdf-lib
  // For now, this is not used
  return Buffer.from(`PDF Content with watermark: ${watermarkText}`);
}

// Download limits endpoint
app.get('/api/download-limits/:userId/:materialId', (req, res) => {
  const { userId, materialId } = req.params;

  try {
    const dbData = JSON.parse(fs.readFileSync('./db.json', 'utf8'));

    console.log(`Fetching download limits for user ${userId}, material ${materialId}`);

    // Find existing limit record
    let limitRecord = dbData.downloadLimits?.find(
      limit => limit.userId === userId && limit.materialId === materialId
    );

    if (!limitRecord) {
      console.log('No limit record found, creating default');
      // Return default values without creating record yet
      return res.json({
        maxDownloads: 5,
        usedDownloads: 0,
        remainingDownloads: 5,
        resetDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString()
      });
    }

    console.log('Found limit record:', limitRecord);

    const remainingDownloads = Math.max(0, limitRecord.maxDownloads - limitRecord.usedDownloads);

    res.json({
      maxDownloads: limitRecord.maxDownloads,
      usedDownloads: limitRecord.usedDownloads,
      remainingDownloads,
      resetDate: limitRecord.resetDate,
      lastDownloadAt: limitRecord.lastDownloadAt
    });

  } catch (error) {
    console.error('Error fetching download limits:', error);
    res.status(500).json({ error: 'Failed to fetch download limits' });
  }
});

// Download attempts endpoint
app.post('/api/download-attempts', (req, res) => {
  try {
    const attempt = req.body;
    const dbData = JSON.parse(fs.readFileSync('./db.json', 'utf8'));

    if (!dbData.downloadAttempts) dbData.downloadAttempts = [];

    const existingAttempt = dbData.downloadAttempts.find(a => a.downloadId === attempt.downloadId);
    if (existingAttempt) {
      Object.assign(existingAttempt, attempt);
    } else {
      dbData.downloadAttempts.unshift(attempt);
    }

    fs.writeFileSync('./db.json', JSON.stringify(dbData, null, 2));
    res.json({ success: true });

  } catch (error) {
    res.status(500).json({ error: 'Failed to record download attempt' });
  }
});

// Configure multer for file uploads
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    const uploadDir = 'uploads/materials';
    if (!fs.existsSync(uploadDir)) {
      fs.mkdirSync(uploadDir, { recursive: true });
    }
    cb(null, uploadDir);
  },
  filename: (req, file, cb) => {
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    cb(null, `${uniqueSuffix}-${file.originalname}`);
  }
});

const upload = multer({
  storage,
  fileFilter: (req, file, cb) => {
    if (file.mimetype === 'application/pdf') {
      cb(null, true);
    } else {
      cb(new Error('Only PDF files are allowed!'), false);
    }
  },
  limits: {
    fileSize: 50 * 1024 * 1024 // 50MB limit
  }
});

// File upload endpoint
app.post('/api/materials/upload', upload.single('file'), (req, res) => {
  try {
    if (!req.file) {
      return res.status(400).json({ error: 'No file uploaded' });
    }

    // Parse tags safely - handle undefined/empty values
    let tags = [];
    if (req.body.tags) {
      try {
        // Try to parse as JSON first
        tags = JSON.parse(req.body.tags);
      } catch (e) {
        // If JSON parsing fails, treat as comma-separated string
        tags = req.body.tags.split(',').map(tag => tag.trim()).filter(Boolean);
      }
    }

    const materialData = {
      id: Date.now(),
      title: req.body.title || '',
      description: req.body.description || '',
      type: req.body.type || 'PDF',
      subject: req.body.subject || '',
      price: req.body.price ? parseInt(req.body.price) : 0,
      originalPrice: req.body.originalPrice ? parseInt(req.body.originalPrice) : 0,
      rating: 0,
      reviews: 0,
      pages: req.body.pages ? parseInt(req.body.pages) : 0,
      duration: req.body.duration || '',
      author: req.body.author || '',
      isPremium: req.body.isPremium === 'true',
      thumbnailUrl: req.body.thumbnailUrl || 'https://images.pexels.com/photos/159711/books-bookstore-book-reading-159711.jpeg',
      status: req.body.status || 'published',
      downloads: 0,
      createdAt: new Date().toISOString().split('T')[0],
      fileSize: req.file.size,
      fileName: req.file.filename,
      originalName: req.file.originalname,
      filePath: req.file.path.replace(/\\/g, '/'), // Normalize path separators
      uploadedAt: new Date().toISOString(),
      downloadCount: 0,
      category: req.body.category || 'notes',
      tags: tags
    };

    // Read current data
    let dbData;
    try {
      const dbPath = path.join(process.cwd(), 'db.json');
      dbData = JSON.parse(fs.readFileSync(dbPath, 'utf8'));
    } catch (error) {
      dbData = { studyMaterials: [] };
    }

    // Add new material to studyMaterials collection
    if (!dbData.studyMaterials) {
      dbData.studyMaterials = [];
    }
    dbData.studyMaterials.push(materialData);

    // Save to database
    const dbPath = path.join(process.cwd(), 'db.json');
    fs.writeFileSync(dbPath, JSON.stringify(dbData, null, 2));

    res.json({
      success: true,
      material: materialData,
      message: 'Material uploaded successfully'
    });

  } catch (error) {
    console.error('Upload error:', error);
    res.status(500).json({
      error: 'Upload failed',
      details: error.message
    });
  }
});

// File download endpoint
app.get('/api/materials/:id/download', (req, res) => {
  try {
    const materialId = parseInt(req.params.id);
    const materialsData = JSON.parse(fs.readFileSync('./db.json', 'utf8'));
    const material = materialsData.studyMaterials.find(m => m.id === materialId);

    if (!material) {
      return res.status(404).json({ error: 'Material not found' });
    }

    const filePath = path.resolve(material.filePath);

    if (!fs.existsSync(filePath)) {
      return res.status(404).json({ error: 'File not found' });
    }

    // Increment download count
    material.downloadCount = (material.downloadCount || 0) + 1;
    fs.writeFileSync('./db.json', JSON.stringify(materialsData, null, 2));

    // Send file
    res.download(filePath, material.originalName);
  } catch (error) {
    console.error('Download error:', error);
    res.status(500).json({ error: 'Download failed' });
  }
});

// Add download count increment endpoint
app.post('/studyMaterials/:id/download', (req, res) => {
  try {
    const materialId = parseInt(req.params.id);
    const material = data.studyMaterials?.find(m => m.id === materialId);

    if (!material) {
      return res.status(404).json({ error: 'Material not found' });
    }

    // Increment download count
    material.downloads = (material.downloads || 0) + 1;
    material.downloadCount = (material.downloadCount || 0) + 1;

    // Save updated data
    fs.writeFileSync('./db.json', JSON.stringify(data, null, 2));

    res.json({
      success: true,
      downloads: material.downloads
    });

  } catch (error) {
    console.error('Download count update error:', error);
    res.status(500).json({ error: 'Failed to update download count' });
  }
});

// Download progress update endpoint
app.post('/api/download-progress', (req, res) => {
  try {
    const { downloadId, progress, status } = req.body;
    console.log('Updating download progress:', { downloadId, progress, status });

    const dbData = JSON.parse(fs.readFileSync('./db.json', 'utf8'));

    // Find and update the download attempt
    if (dbData.downloadAttempts) {
      const attemptIndex = dbData.downloadAttempts.findIndex(a => a.downloadId === downloadId);
      if (attemptIndex !== -1) {
        dbData.downloadAttempts[attemptIndex].progress = progress;
        dbData.downloadAttempts[attemptIndex].status = status;

        fs.writeFileSync('./db.json', JSON.stringify(dbData, null, 2));
        console.log('Updated download attempt in database');
      }
    }

    res.json({ success: true });
  } catch (error) {
    console.error('Error updating download progress:', error);
    res.status(500).json({ error: 'Failed to update progress' });
  }
});

// Download completion endpoint
app.post('/api/download-complete', (req, res) => {
  try {
    const { downloadId } = req.body;

    if (!downloadId) {
      return res.status(400).json({ error: 'downloadId is required' });
    }

    const dbData = JSON.parse(fs.readFileSync('./db.json', 'utf8'));

    const attempt = dbData.downloadAttempts?.find(a => a.downloadId === downloadId);
    if (!attempt) {
      return res.status(404).json({ error: 'Download attempt not found' });
    }

    attempt.status = 'completed';
    attempt.progress = 100;

    if (!dbData.downloadLimits) dbData.downloadLimits = [];

    let limitRecord = dbData.downloadLimits.find(
      limit => limit.userId === attempt.userId && limit.materialId === attempt.materialId
    );

    if (!limitRecord) {
      limitRecord = {
        userId: attempt.userId,
        materialId: attempt.materialId,
        maxDownloads: 5,
        usedDownloads: 0,
        lastDownloadAt: null,
        resetDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString()
      };
      dbData.downloadLimits.push(limitRecord);
    }

    limitRecord.usedDownloads += 1;
    limitRecord.lastDownloadAt = new Date().toISOString();

    fs.writeFileSync('./db.json', JSON.stringify(dbData, null, 2));
    res.json({ success: true, limitRecord });

  } catch (error) {
    res.status(500).json({ error: 'Failed to mark download complete' });
  }
});

// Get exam details
app.get('/api/exams/:id', (req, res) => {
  try {
    const examId = req.params.id;
    const exam = data.exams?.find(e => e.id === examId);

    if (!exam) {
      return res.status(404).json({ error: 'Exam not found' });
    }

    res.json(exam);
  } catch (error) {
    console.error('Error fetching exam:', error);
    res.status(500).json({ error: 'Failed to fetch exam details' });
  }
});

// Get questions for an exam
app.get('/api/questions', (req, res) => {
  try {
    const { examId } = req.query;

    if (!examId) {
      return res.status(400).json({ error: 'examId is required' });
    }

    const questions = data.questions?.filter(q => q.examId === examId) || [];
    console.log(`Found ${questions.length} questions for examId: ${examId}`);

    if (questions.length === 0) {
      return res.status(404).json({ error: 'No questions found for this exam' });
    }

    res.json(questions);
  } catch (error) {
    console.error('Error fetching questions:', error);
    res.status(500).json({ error: 'Failed to fetch questions' });
  }
});

// Get exam results
app.get('/api/exam-results/:examId', (req, res) => {
  try {
    const { examId } = req.params;

    // Try to find existing result
    let result = data.examResults?.find(r => r.examId === examId);

    if (!result) {
      // Generate mock result if not found
      const exam = data.exams?.find(e => e.id === examId);
      const examTitle = exam ? exam.title : "Mock Test";

      result = {
        id: Date.now().toString(),
        examId: examId,
        examTitle: examTitle,
        score: Math.floor(Math.random() * 40) + 120, // Random score between 120-160
        totalQuestions: exam ? exam.questions : 100,
        totalMarks: exam ? exam.totalMarks : 200,
        correctAnswers: Math.floor(Math.random() * 30) + 60,
        incorrectAnswers: Math.floor(Math.random() * 25) + 15,
        unanswered: Math.floor(Math.random() * 10) + 5,
        timeTaken: "1h 45m",
        rank: Math.floor(Math.random() * 500) + 100,
        totalParticipants: exam ? exam.participants : 1250,
        percentile: Math.floor(Math.random() * 30) + 70,
        subjects: [
          { name: "History", score: Math.floor(Math.random() * 10) + 15, total: 25, marks: Math.floor(Math.random() * 20) + 30, totalMarks: 50 },
          { name: "Geography", score: Math.floor(Math.random() * 10) + 15, total: 25, marks: Math.floor(Math.random() * 20) + 30, totalMarks: 50 },
          { name: "Polity", score: Math.floor(Math.random() * 10) + 15, total: 25, marks: Math.floor(Math.random() * 20) + 30, totalMarks: 50 },
          { name: "Current Affairs", score: Math.floor(Math.random() * 10) + 15, total: 25, marks: Math.floor(Math.random() * 20) + 30, totalMarks: 50 }
        ],
        completedAt: new Date().toISOString(),
        submittedAt: new Date().toISOString()
      };
    }

    res.json(result);
  } catch (error) {
    console.error('Error fetching exam results:', error);
    res.status(500).json({ error: 'Failed to fetch exam results' });
  }
});

// Get user daily quiz progress with auto-creation
app.get('/userDailyQuizProgress/:userId', (req, res) => {
  try {
    const { userId } = req.params;
    const dbData = JSON.parse(fs.readFileSync('./db.json', 'utf8'));

    let userProgress = dbData.userDailyQuizProgress?.find(
      progress => progress.userId === userId || progress.userId === parseInt(userId)
    );

    if (!userProgress) {
      // Create default progress for new user
      userProgress = {
        id: Date.now().toString(),
        userId: userId,
        totalQuizzesTaken: 0,
        averageScore: 0,
        currentStreak: 0,
        bestStreak: 0,
        weeklyProgress: []
      };

      if (!dbData.userDailyQuizProgress) {
        dbData.userDailyQuizProgress = [];
      }

      dbData.userDailyQuizProgress.push(userProgress);
      fs.writeFileSync('./db.json', JSON.stringify(dbData, null, 2));
    }

    res.json(userProgress);
  } catch (error) {
    console.error('Error fetching user daily quiz progress:', error);
    res.status(500).json({ error: 'Failed to fetch user progress' });
  }
});

app.listen(PORT, () => {
  console.log(`🚀 API Server running on port ${PORT}`);
});

