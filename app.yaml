runtime: nodejs22
service: default

env_variables:
  NODE_ENV: production
  VITE_API_BASE_URL: https://api-dot-brainstorm-upsc-466216.el.r.appspot.com
  VITE_JSON_SERVER_URL: https://api-dot-brainstorm-upsc-466216.el.r.appspot.com
  VITE_APP_URL: https://brainstorm-upsc-466216.el.r.appspot.com
  VITE_APP_NAME: Brainstorm
  VITE_RAZORPAY_KEY_ID: rzp_test_USZOM02NmjnObZ
  VITE_CASHFREE_APP_ID: your_cashfree_test_app_id
  VITE_UPI_ID: yourbusiness@upi
  VITE_FIREBASE_API_KEY: AIzaSyDgHZQm2OoU8BpD1r9Zj8Nijz3my3dRv-E
  VITE_FIREBASE_AUTH_DOMAIN: brainstorm-upsc.firebaseapp.com
  VITE_FIREBASE_PROJECT_ID: brainstorm-upsc
  VITE_FIREBASE_STORAGE_BUCKET: brainstorm-upsc.firebasestorage.app
  VITE_FIREBASE_MESSAGING_SENDER_ID: 677811681568
  VITE_FIREBASE_APP_ID: 1:677811681568:web:363994c7a855f88636ff45
  VITE_FIREBASE_MEASUREMENT_ID: G-MFC0XV9JF0

handlers:
  # JavaScript files with correct MIME type
  - url: /(.*\.js)$
    static_files: dist/\1
    upload: dist/.*\.js$
    mime_type: application/javascript
    secure: always

  # CSS files
  - url: /(.*\.css)$
    static_files: dist/\1
    upload: dist/.*\.css$
    mime_type: text/css
    secure: always

  # Source maps
  - url: /(.*\.map)$
    static_files: dist/\1
    upload: dist/.*\.map$
    mime_type: application/json
    secure: always

  # Images and other assets
  - url: /(.*\.(png|jpg|jpeg|gif|svg|ico))$
    static_files: dist/\1
    upload: dist/.*\.(png|jpg|jpeg|gif|svg|ico)$
    secure: always

  # JSON files
  - url: /(.*\.json)$
    static_files: dist/\1
    upload: dist/.*\.json$
    mime_type: application/json
    secure: always

  # Static folder
  - url: /static
    static_dir: dist/static
    secure: always

  # Fallback for SPA routes
  - url: /.*
    static_files: dist/index.html
    upload: dist/index.html
    secure: always

automatic_scaling:
  target_cpu_utilization: 0.65
  min_instances: 1
  max_instances: 3

