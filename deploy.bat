@echo off
setlocal enabledelayedexpansion

REM Deployment script for UPSC SaaS Platform
echo [INFO] Starting deployment to Google App Engine...

REM Check if gcloud is installed
where gcloud >nul 2>nul
if %errorlevel% neq 0 (
    echo [ERROR] gcloud CLI is not installed. Please install it first.
    pause
    exit /b 1
)

REM Check if user is authenticated
for /f "tokens=*" %%i in ('gcloud auth list --filter=status:ACTIVE --format="value(account)"') do set ACCOUNT=%%i
if "!ACCOUNT!"=="" (
    echo [ERROR] Not authenticated with gcloud. Please run 'gcloud auth login'
    pause
    exit /b 1
)

REM Set project ID
set PROJECT_ID=brainstorm-upsc-466216
echo [INFO] Setting project to %PROJECT_ID%
call gcloud config set project %PROJECT_ID%

echo [INFO] Building frontend with production environment...
call npm run build
if %errorlevel% neq 0 (
    echo [ERROR] Frontend build failed
    pause
    exit /b 1
)

REM echo [INFO] Deploying API service...REM 
REM cd api-server
REM call gcloud app deploy api-service.yaml --quiet
REM if %errorlevel% neq 0 (
REM     echo [ERROR] API service deployment failed
REM     cd ..
REM     pause
REM     exit /b 1
REM )
REM cd ..

echo [INFO] Deploying frontend (default service)...
call gcloud app deploy app.yaml --quiet
if %errorlevel% neq 0 (
    echo [ERROR] Frontend deployment failed
    pause
    exit /b 1
)

echo [SUCCESS] Deployment completed successfully!
echo.
echo Your application URLs:
echo Frontend: https://brainstorm-upsc-466216.el.r.appspot.com
echo API service: https://api-dot-brainstorm-upsc-466216.el.r.appspot.com

pause



