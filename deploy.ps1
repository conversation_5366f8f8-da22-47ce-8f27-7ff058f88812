# Deployment script for UPSC SaaS Platform
$ErrorActionPreference = "Stop"

Write-Host "🚀 Starting deployment to Google App Engine..." -ForegroundColor Green

# Check if gcloud is installed
if (!(Get-Command gcloud -ErrorAction SilentlyContinue)) {
    Write-Host "❌ gcloud CLI is not installed. Please install it first." -ForegroundColor Red
    Read-Host "Press Enter to exit"
    exit 1
}

# Check if user is authenticated
$account = gcloud auth list --filter=status:ACTIVE --format="value(account)" 2>$null
if ([string]::IsNullOrEmpty($account)) {
    Write-Host "❌ Not authenticated with gcloud. Please run 'gcloud auth login'" -ForegroundColor Red
    Read-Host "Press Enter to exit"
    exit 1
}

# Set project ID
$PROJECT_ID = "brainstorm-upsc-466216"
gcloud config set project $PROJECT_ID

Write-Host "📦 Building frontend..." -ForegroundColor Yellow
try {
    npm run build
    if ($LASTEXITCODE -ne 0) { throw "Frontend build failed" }
} catch {
    Write-Host "❌ Frontend build failed: $_" -ForegroundColor Red
    Read-Host "Press Enter to exit"
    exit 1
}

Write-Host "🔧 Setting up secrets..." -ForegroundColor Yellow
# Set secrets using Secret Manager (only if they don't exist)
#try {
#    "rzp_test_USZOM02NmjnObZ" | gcloud secrets create razorpay-key-id --data-file=- 2>$null
#} catch {
#    Write-Host "Secret razorpay-key-id already exists" -ForegroundColor Gray
#}

#try {
#    "KNPqBUHxyAw49KOGc7hfLRLP" | gcloud secrets create razorpay-key-secret --data-file=- 2>$null
#} catch {
#    Write-Host "Secret razorpay-key-secret already exists" -ForegroundColor Gray
#}

# Add your Cashfree credentials here
# try {
#     "your_cashfree_client_id" | gcloud secrets create cashfree-client-id --data-file=- 2>$null
# } catch {
#     Write-Host "Secret cashfree-client-id already exists" -ForegroundColor Gray
# }

Write-Host "🚀 Deploying API service..." -ForegroundColor Yellow
try {
    Set-Location api-server
    gcloud app deploy ../api-service.yaml --quiet
    if ($LASTEXITCODE -ne 0) { throw "API service deployment failed" }
    Set-Location ..
} catch {
    Write-Host "❌ API service deployment failed: $_" -ForegroundColor Red
    Set-Location ..
    Read-Host "Press Enter to exit"
    exit 1
}

Write-Host "🚀 Deploying Razorpay service..." -ForegroundColor Yellow
try {
    Set-Location razorpay-backend
    gcloud app deploy ../razorpay-service.yaml --quiet
    if ($LASTEXITCODE -ne 0) { throw "Razorpay service deployment failed" }
    Set-Location ..
} catch {
    Write-Host "❌ Razorpay service deployment failed: $_" -ForegroundColor Red
    Set-Location ..
    Read-Host "Press Enter to exit"
    exit 1
}

Write-Host "🚀 Deploying Cashfree service..." -ForegroundColor Yellow
try {
    Set-Location cashfree-backend
    gcloud app deploy ../cashfree-service.yaml --quiet
    if ($LASTEXITCODE -ne 0) { throw "Cashfree service deployment failed" }
    Set-Location ..
} catch {
    Write-Host "❌ Cashfree service deployment failed: $_" -ForegroundColor Red
    Set-Location ..
    Read-Host "Press Enter to exit"
    exit 1
}

Write-Host "🚀 Deploying frontend..." -ForegroundColor Yellow
try {
    gcloud app deploy app.yaml --quiet
    if ($LASTEXITCODE -ne 0) { throw "Frontend deployment failed" }
} catch {
    Write-Host "❌ Frontend deployment failed: $_" -ForegroundColor Red
    Read-Host "Press Enter to exit"
    exit 1
}

Write-Host "✅ Deployment completed successfully!" -ForegroundColor Green
Write-Host "🌐 Your app is available at: https://brainstorm-upsc-466216.el.r.appspot.com" -ForegroundColor Cyan
Write-Host "🔧 API service: https://api-dot-brainstorm-upsc-466216.el.r.appspot.com" -ForegroundColor Cyan
Write-Host "💳 Razorpay service: https://razorpay-dot-brainstorm-upsc-466216.el.r.appspot.com" -ForegroundColor Cyan
Write-Host "💰 Cashfree service: https://cashfree-dot-brainstorm-upsc-466216.el.r.appspot.com" -ForegroundColor Cyan

Read-Host "Press Enter to exit"