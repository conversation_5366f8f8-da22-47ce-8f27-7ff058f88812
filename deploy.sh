#!/bin/bash

# Deployment script for UPSC SaaS Platform
set -e

echo "🚀 Starting deployment to Google App Engine..."

# Check if gcloud is installed
if ! command -v gcloud &> /dev/null; then
    echo "❌ gcloud CLI is not installed. Please install it first."
    exit 1
fi

# Check if user is authenticated
if ! gcloud auth list --filter=status:ACTIVE --format="value(account)" | grep -q .; then
    echo "❌ Not authenticated with gcloud. Please run 'gcloud auth login'"
    exit 1
fi

# Set project ID
PROJECT_ID="brainstorm-upsc-466216"
gcloud config set project $PROJECT_ID

echo "📦 Building frontend with production environment..."
npm run build

echo "🚀 Deploying API service..."
cd api-server && gcloud app deploy ../api-service.yaml --quiet && cd ..

echo "🚀 Deploying frontend..."
gcloud app deploy app.yaml --quiet

echo "✅ Deployment completed successfully!"
echo "🌐 Your app is available at: https://brainstorm-upsc-466216.el.r.appspot.com"
echo "🔧 API service: https://api-dot-brainstorm-upsc-466216.el.r.appspot.com"

