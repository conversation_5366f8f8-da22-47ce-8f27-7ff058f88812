import{a as e,A as o}from"./index-IEicAP2f.js";import"./vendor-CAdiN7ib.js";import"./router-BfC3tt3M.js";import"./firebase-HhoYzh8x.js";import"./ui-CMwmWbW1.js";class t{async downloadMaterial(t,r){try{const r=await e.getAuthHeaders(),a=await e.get(`${o.STUDY_MATERIALS}/${t}`);if(!a.ok)throw new Error(`Material not found: ${a.statusText}`);const n=await a.json(),i=await fetch(`${o.STUDY_MATERIALS}/${t}/download`,{headers:r});if(i.ok){const e=await i.blob(),o=window.URL.createObjectURL(e),t=document.createElement("a");return t.href=o,t.download=n.originalName||`${n.title}.pdf`,document.body.appendChild(t),t.click(),document.body.removeChild(t),window.URL.revokeObjectURL(o),{success:!0,downloadUrl:o,fileName:n.originalName||`${n.title}.pdf`}}{const e=await i.text();throw new Error(`Download failed: ${e}`)}}catch(a){return console.error("Download failed:",a),{success:!1,error:a.message||"Unknown error occurred"}}}}const r=new t;export{t as MaterialDownloadService,r as materialDownloadService};
