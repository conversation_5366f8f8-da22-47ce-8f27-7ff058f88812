import{r as t,c as e,g as r,a as n}from"./vendor-CAdiN7ib.js";
/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */var o={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};
/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const i=(e,r)=>{const n=t.forwardRef(({color:n="currentColor",size:i=24,strokeWidth:a=2,absoluteStrokeWidth:c,className:u="",children:l,...s},f)=>{return t.createElement("svg",{ref:f,...o,width:i,height:i,stroke:n,strokeWidth:c?24*Number(a)/Number(i):a,className:["lucide",`lucide-${p=e,p.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase().trim()}`,u].join(" "),...s},[...r.map(([e,r])=>t.createElement(e,r)),...Array.isArray(l)?l:[l]]);var p});return n.displayName=`${e}`,n},a=i("AlertCircle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]]),c=i("AlertTriangle",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3Z",key:"c3ski4"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]]),u=i("ArrowLeft",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]]),l=i("ArrowRight",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"m12 5 7 7-7 7",key:"xquz4c"}]]),s=i("Award",[["circle",{cx:"12",cy:"8",r:"6",key:"1vp47v"}],["path",{d:"M15.477 12.89 17 22l-5-3-5 3 1.523-9.11",key:"em7aur"}]]),f=i("BarChart3",[["path",{d:"M3 3v18h18",key:"1s2lah"}],["path",{d:"M18 17V9",key:"2bz60n"}],["path",{d:"M13 17V5",key:"1frdt8"}],["path",{d:"M8 17v-3",key:"17ska0"}]]),p=i("Bell",[["path",{d:"M6 8a6 6 0 0 1 12 0c0 7 3 9 3 9H3s3-2 3-9",key:"1qo2s2"}],["path",{d:"M10.3 21a1.94 1.94 0 0 0 3.4 0",key:"qgo35s"}]]),h=i("BookOpen",[["path",{d:"M2 3h6a4 4 0 0 1 4 4v14a3 3 0 0 0-3-3H2z",key:"vv98re"}],["path",{d:"M22 3h-6a4 4 0 0 0-4 4v14a3 3 0 0 1 3-3h7z",key:"1cyq3y"}]]),y=i("Calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]]),d=i("CheckCircle2",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m9 12 2 2 4-4",key:"dzmm74"}]]),v=i("CheckCircle",[["path",{d:"M22 11.08V12a10 10 0 1 1-5.93-9.14",key:"g774vq"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]]),m=i("Check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]]),b=i("ChevronDown",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]]),g=i("ChevronRight",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]]),w=i("Clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]]),x=i("Cookie",[["path",{d:"M12 2a10 10 0 1 0 10 10 4 4 0 0 1-5-5 4 4 0 0 1-5-5",key:"laymnq"}],["path",{d:"M8.5 8.5v.01",key:"ue8clq"}],["path",{d:"M16 15.5v.01",key:"14dtrp"}],["path",{d:"M12 12v.01",key:"u5ubse"}],["path",{d:"M11 17v.01",key:"1hyl5a"}],["path",{d:"M7 14v.01",key:"uct60s"}]]),O=i("Copyright",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M14.83 14.83a4 4 0 1 1 0-5.66",key:"1i56pz"}]]),j=i("CreditCard",[["rect",{width:"20",height:"14",x:"2",y:"5",rx:"2",key:"ynyp8z"}],["line",{x1:"2",x2:"22",y1:"10",y2:"10",key:"1b3vmo"}]]),S=i("Crown",[["path",{d:"m2 4 3 12h14l3-12-6 7-4-7-4 7-6-7zm3 16h14",key:"zkxr6b"}]]),k=i("Download",[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"7 10 12 15 17 10",key:"2ggqvy"}],["line",{x1:"12",x2:"12",y1:"15",y2:"3",key:"1vk2je"}]]),P=i("ExternalLink",[["path",{d:"M15 3h6v6",key:"1q9fwt"}],["path",{d:"M10 14 21 3",key:"gplh6r"}],["path",{d:"M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6",key:"a6xqqp"}]]),A=i("EyeOff",[["path",{d:"M9.88 9.88a3 3 0 1 0 4.24 4.24",key:"1jxqfv"}],["path",{d:"M10.73 5.08A10.43 10.43 0 0 1 12 5c7 0 10 7 10 7a13.16 13.16 0 0 1-1.67 2.68",key:"9wicm4"}],["path",{d:"M6.61 6.61A13.526 13.526 0 0 0 2 12s3 7 10 7a9.74 9.74 0 0 0 5.39-1.61",key:"1jreej"}],["line",{x1:"2",x2:"22",y1:"2",y2:"22",key:"a6p6uj"}]]),E=i("Eye",[["path",{d:"M2 12s3-7 10-7 10 7 10 7-3 7-10 7-10-7-10-7Z",key:"rwhkz3"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]]),M=i("FileText",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]]),_=i("Filter",[["polygon",{points:"22 3 2 3 10 12.46 10 19 14 21 14 12.46 22 3",key:"1yg77f"}]]),T=i("Flag",[["path",{d:"M4 15s1-1 4-1 5 2 8 2 4-1 4-1V3s-1 1-4 1-5-2-8-2-4 1-4 1z",key:"i9b6wo"}],["line",{x1:"4",x2:"4",y1:"22",y2:"15",key:"1cm3nv"}]]),C=i("Gavel",[["path",{d:"m14.5 12.5-8 8a2.119 2.119 0 1 1-3-3l8-8",key:"15492f"}],["path",{d:"m16 16 6-6",key:"vzrcl6"}],["path",{d:"m8 8 6-6",key:"18bi4p"}],["path",{d:"m9 7 8 8",key:"5jnvq1"}],["path",{d:"m21 11-8-8",key:"z4y7zo"}]]),D=i("Globe",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 2a14.5 14.5 0 0 0 0 20 14.5 14.5 0 0 0 0-20",key:"13o1zl"}],["path",{d:"M2 12h20",key:"9i4pu4"}]]),I=i("Headphones",[["path",{d:"M3 14h3a2 2 0 0 1 2 2v3a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-7a9 9 0 0 1 18 0v7a2 2 0 0 1-2 2h-1a2 2 0 0 1-2-2v-3a2 2 0 0 1 2-2h3",key:"1xhozi"}]]),N=i("HelpCircle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M9.09 9a3 3 0 0 1 5.83 1c0 2-3 3-3 3",key:"1u773s"}],["path",{d:"M12 17h.01",key:"p32p05"}]]),B=i("Home",[["path",{d:"m3 9 9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z",key:"y5dka4"}],["polyline",{points:"9 22 9 12 15 12 15 22",key:"e2us08"}]]),L=i("IndianRupee",[["path",{d:"M6 3h12",key:"ggurg9"}],["path",{d:"M6 8h12",key:"6g4wlu"}],["path",{d:"m6 13 8.5 8",key:"u1kupk"}],["path",{d:"M6 13h3",key:"wdp6ag"}],["path",{d:"M9 13c6.667 0 6.667-10 0-10",key:"1nkvk2"}]]),R=i("Info",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 16v-4",key:"1dtifu"}],["path",{d:"M12 8h.01",key:"e9boi3"}]]),z=i("Leaf",[["path",{d:"M11 20A7 7 0 0 1 9.8 6.1C15.5 5 17 4.48 19 2c1 2 2 4.18 2 8 0 5.5-4.78 10-10 10Z",key:"nnexq3"}],["path",{d:"M2 21c0-3 1.85-5.36 5.08-6C9.5 14.52 12 13 13 12",key:"mt58a7"}]]),U=i("Loader2",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]]),$=i("Lock",[["rect",{width:"18",height:"11",x:"3",y:"11",rx:"2",ry:"2",key:"1w4ew1"}],["path",{d:"M7 11V7a5 5 0 0 1 10 0v4",key:"fwvmzm"}]]),q=i("LogIn",[["path",{d:"M15 3h4a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2h-4",key:"u53s6r"}],["polyline",{points:"10 17 15 12 10 7",key:"1ail0h"}],["line",{x1:"15",x2:"3",y1:"12",y2:"12",key:"v6grx8"}]]),F=i("LogOut",[["path",{d:"M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4",key:"1uf3rs"}],["polyline",{points:"16 17 21 12 16 7",key:"1gabdz"}],["line",{x1:"21",x2:"9",y1:"12",y2:"12",key:"1uyos4"}]]),W=i("Mail",[["rect",{width:"20",height:"16",x:"2",y:"4",rx:"2",key:"18n3k1"}],["path",{d:"m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7",key:"1ocrg3"}]]),H=i("MapPin",[["path",{d:"M20 10c0 6-8 12-8 12s-8-6-8-12a8 8 0 0 1 16 0Z",key:"2oe9fu"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]]),V=i("Maximize",[["path",{d:"M8 3H5a2 2 0 0 0-2 2v3",key:"1dcmit"}],["path",{d:"M21 8V5a2 2 0 0 0-2-2h-3",key:"1e4gt3"}],["path",{d:"M3 16v3a2 2 0 0 0 2 2h3",key:"wsl5sc"}],["path",{d:"M16 21h3a2 2 0 0 0 2-2v-3",key:"18trek"}]]),X=i("Medal",[["path",{d:"M7.21 15 2.66 7.14a2 2 0 0 1 .13-2.2L4.4 2.8A2 2 0 0 1 6 2h12a2 2 0 0 1 1.6.8l1.6 2.14a2 2 0 0 1 .14 2.2L16.79 15",key:"143lza"}],["path",{d:"M11 12 5.12 2.2",key:"qhuxz6"}],["path",{d:"m13 12 5.88-9.8",key:"hbye0f"}],["path",{d:"M8 7h8",key:"i86dvs"}],["circle",{cx:"12",cy:"17",r:"5",key:"qbz8iq"}],["path",{d:"M12 18v-2h-.5",key:"fawc4q"}]]),G=i("Menu",[["line",{x1:"4",x2:"20",y1:"12",y2:"12",key:"1e0a9i"}],["line",{x1:"4",x2:"20",y1:"6",y2:"6",key:"1owob3"}],["line",{x1:"4",x2:"20",y1:"18",y2:"18",key:"yk5zj1"}]]),Y=i("MessageCircle",[["path",{d:"M7.9 20A9 9 0 1 0 4 16.1L2 22Z",key:"vv11sd"}]]),K=i("MinusCircle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M8 12h8",key:"1wcyev"}]]),Z=i("Pen",[["path",{d:"M17 3a2.85 2.83 0 1 1 4 4L7.5 20.5 2 22l1.5-5.5Z",key:"5qss01"}]]),J=i("Phone",[["path",{d:"M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z",key:"foiqr5"}]]),Q=i("Play",[["polygon",{points:"5 3 19 12 5 21 5 3",key:"191637"}]]),tt=i("Plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]]),et=i("RotateCcw",[["path",{d:"M3 12a9 9 0 1 0 9-9 9.75 9.75 0 0 0-6.74 2.74L3 8",key:"1357e3"}],["path",{d:"M3 3v5h5",key:"1xhq8a"}]]),rt=i("Save",[["path",{d:"M19 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h11l5 5v11a2 2 0 0 1-2 2z",key:"1owoqh"}],["polyline",{points:"17 21 17 13 7 13 7 21",key:"1md35c"}],["polyline",{points:"7 3 7 8 15 8",key:"8nz8an"}]]),nt=i("Search",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["path",{d:"m21 21-4.3-4.3",key:"1qie3q"}]]),ot=i("Send",[["path",{d:"m22 2-7 20-4-9-9-4Z",key:"1q3vgg"}],["path",{d:"M22 2 11 13",key:"nzbqef"}]]),it=i("Settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]]),at=i("ShieldCheck",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}],["path",{d:"m9 12 2 2 4-4",key:"dzmm74"}]]),ct=i("Shield",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}]]),ut=i("ShoppingCart",[["circle",{cx:"8",cy:"21",r:"1",key:"jimo8o"}],["circle",{cx:"19",cy:"21",r:"1",key:"13723u"}],["path",{d:"M2.05 2.05h2l2.66 12.42a2 2 0 0 0 2 1.58h9.78a2 2 0 0 0 1.95-1.57l1.65-7.43H5.12",key:"9zh506"}]]),lt=i("SquarePen",[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a2.121 2.121 0 1 1 3 3L12 15l-4 1 1-4Z",key:"1lpok0"}]]),st=i("Star",[["polygon",{points:"12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2",key:"8f66p6"}]]),ft=i("Tag",[["path",{d:"M12.586 2.586A2 2 0 0 0 11.172 2H4a2 2 0 0 0-2 2v7.172a2 2 0 0 0 .586 1.414l8.704 8.704a2.426 2.426 0 0 0 3.42 0l6.58-6.58a2.426 2.426 0 0 0 0-3.42z",key:"vktsd0"}],["circle",{cx:"7.5",cy:"7.5",r:".5",fill:"currentColor",key:"kqv944"}]]),pt=i("Target",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["circle",{cx:"12",cy:"12",r:"6",key:"1vlfrh"}],["circle",{cx:"12",cy:"12",r:"2",key:"1c9p78"}]]),ht=i("Trash2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]]),yt=i("TrendingUp",[["polyline",{points:"22 7 13.5 15.5 8.5 10.5 2 17",key:"126l90"}],["polyline",{points:"16 7 22 7 22 13",key:"kwv8wd"}]]),dt=i("Trophy",[["path",{d:"M6 9H4.5a2.5 2.5 0 0 1 0-5H6",key:"17hqa7"}],["path",{d:"M18 9h1.5a2.5 2.5 0 0 0 0-5H18",key:"lmptdp"}],["path",{d:"M4 22h16",key:"57wxv0"}],["path",{d:"M10 14.66V17c0 .55-.47.98-.97 1.21C7.85 18.75 7 20.24 7 22",key:"1nw9bq"}],["path",{d:"M14 14.66V17c0 .55.47.98.97 1.21C16.15 18.75 17 20.24 17 22",key:"1np0yb"}],["path",{d:"M18 2H6v7a6 6 0 0 0 12 0V2Z",key:"u46fv3"}]]),vt=i("Upload",[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"17 8 12 3 7 8",key:"t8dd8p"}],["line",{x1:"12",x2:"12",y1:"3",y2:"15",key:"widbto"}]]),mt=i("User",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]]),bt=i("Users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["path",{d:"M16 3.13a4 4 0 0 1 0 7.75",key:"1da9ce"}]]),gt=i("Video",[["path",{d:"m22 8-6 4 6 4V8Z",key:"50v9me"}],["rect",{width:"14",height:"12",x:"2",y:"6",rx:"2",ry:"2",key:"1rqjg6"}]]),wt=i("XCircle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]]),xt=i("X",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]]),Ot=i("Zap",[["polygon",{points:"13 2 3 14 12 14 11 22 21 10 12 10 13 2",key:"45s27k"}]]);function jt(t){var e,r,n="";if("string"==typeof t||"number"==typeof t)n+=t;else if("object"==typeof t)if(Array.isArray(t)){var o=t.length;for(e=0;e<o;e++)t[e]&&(r=jt(t[e]))&&(n&&(n+=" "),n+=r)}else for(r in t)t[r]&&(n&&(n+=" "),n+=r);return n}function St(){for(var t,e,r=0,n="",o=arguments.length;r<o;r++)(t=arguments[r])&&(e=jt(t))&&(n&&(n+=" "),n+=e);return n}var kt=Array.isArray,Pt="object"==typeof e&&e&&e.Object===Object&&e,At=Pt,Et="object"==typeof self&&self&&self.Object===Object&&self,Mt=At||Et||Function("return this")(),_t=Mt.Symbol,Tt=_t,Ct=Object.prototype,Dt=Ct.hasOwnProperty,It=Ct.toString,Nt=Tt?Tt.toStringTag:void 0;var Bt=function(t){var e=Dt.call(t,Nt),r=t[Nt];try{t[Nt]=void 0;var n=!0}catch(i){}var o=It.call(t);return n&&(e?t[Nt]=r:delete t[Nt]),o},Lt=Object.prototype.toString;var Rt=Bt,zt=function(t){return Lt.call(t)},Ut=_t?_t.toStringTag:void 0;var $t=function(t){return null==t?void 0===t?"[object Undefined]":"[object Null]":Ut&&Ut in Object(t)?Rt(t):zt(t)};var qt=function(t){return null!=t&&"object"==typeof t},Ft=$t,Wt=qt;var Ht=function(t){return"symbol"==typeof t||Wt(t)&&"[object Symbol]"==Ft(t)},Vt=kt,Xt=Ht,Gt=/\.|\[(?:[^[\]]*|(["'])(?:(?!\1)[^\\]|\\.)*?\1)\]/,Yt=/^\w*$/;var Kt=function(t,e){if(Vt(t))return!1;var r=typeof t;return!("number"!=r&&"symbol"!=r&&"boolean"!=r&&null!=t&&!Xt(t))||(Yt.test(t)||!Gt.test(t)||null!=e&&t in Object(e))};var Zt=function(t){var e=typeof t;return null!=t&&("object"==e||"function"==e)};const Jt=r(Zt);var Qt=$t,te=Zt;var ee=function(t){if(!te(t))return!1;var e=Qt(t);return"[object Function]"==e||"[object GeneratorFunction]"==e||"[object AsyncFunction]"==e||"[object Proxy]"==e};const re=r(ee);var ne,oe=Mt["__core-js_shared__"],ie=(ne=/[^.]+$/.exec(oe&&oe.keys&&oe.keys.IE_PROTO||""))?"Symbol(src)_1."+ne:"";var ae=function(t){return!!ie&&ie in t},ce=Function.prototype.toString;var ue=function(t){if(null!=t){try{return ce.call(t)}catch(e){}try{return t+""}catch(e){}}return""},le=ee,se=ae,fe=Zt,pe=ue,he=/^\[object .+?Constructor\]$/,ye=Function.prototype,de=Object.prototype,ve=ye.toString,me=de.hasOwnProperty,be=RegExp("^"+ve.call(me).replace(/[\\^$.*+?()[\]{}|]/g,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$");var ge=function(t){return!(!fe(t)||se(t))&&(le(t)?be:he).test(pe(t))},we=function(t,e){return null==t?void 0:t[e]};var xe=function(t,e){var r=we(t,e);return ge(r)?r:void 0},Oe=xe(Object,"create"),je=Oe;var Se=function(){this.__data__=je?je(null):{},this.size=0};var ke=function(t){var e=this.has(t)&&delete this.__data__[t];return this.size-=e?1:0,e},Pe=Oe,Ae=Object.prototype.hasOwnProperty;var Ee=function(t){var e=this.__data__;if(Pe){var r=e[t];return"__lodash_hash_undefined__"===r?void 0:r}return Ae.call(e,t)?e[t]:void 0},Me=Oe,_e=Object.prototype.hasOwnProperty;var Te=Oe;var Ce=Se,De=ke,Ie=Ee,Ne=function(t){var e=this.__data__;return Me?void 0!==e[t]:_e.call(e,t)},Be=function(t,e){var r=this.__data__;return this.size+=this.has(t)?0:1,r[t]=Te&&void 0===e?"__lodash_hash_undefined__":e,this};function Le(t){var e=-1,r=null==t?0:t.length;for(this.clear();++e<r;){var n=t[e];this.set(n[0],n[1])}}Le.prototype.clear=Ce,Le.prototype.delete=De,Le.prototype.get=Ie,Le.prototype.has=Ne,Le.prototype.set=Be;var Re=Le;var ze=function(){this.__data__=[],this.size=0};var Ue=function(t,e){return t===e||t!=t&&e!=e},$e=Ue;var qe=function(t,e){for(var r=t.length;r--;)if($e(t[r][0],e))return r;return-1},Fe=qe,We=Array.prototype.splice;var He=qe;var Ve=qe;var Xe=qe;var Ge=ze,Ye=function(t){var e=this.__data__,r=Fe(e,t);return!(r<0)&&(r==e.length-1?e.pop():We.call(e,r,1),--this.size,!0)},Ke=function(t){var e=this.__data__,r=He(e,t);return r<0?void 0:e[r][1]},Ze=function(t){return Ve(this.__data__,t)>-1},Je=function(t,e){var r=this.__data__,n=Xe(r,t);return n<0?(++this.size,r.push([t,e])):r[n][1]=e,this};function Qe(t){var e=-1,r=null==t?0:t.length;for(this.clear();++e<r;){var n=t[e];this.set(n[0],n[1])}}Qe.prototype.clear=Ge,Qe.prototype.delete=Ye,Qe.prototype.get=Ke,Qe.prototype.has=Ze,Qe.prototype.set=Je;var tr=Qe,er=xe(Mt,"Map"),rr=Re,nr=tr,or=er;var ir=function(t){var e=typeof t;return"string"==e||"number"==e||"symbol"==e||"boolean"==e?"__proto__"!==t:null===t};var ar=function(t,e){var r=t.__data__;return ir(e)?r["string"==typeof e?"string":"hash"]:r.map},cr=ar;var ur=ar;var lr=ar;var sr=ar;var fr=function(){this.size=0,this.__data__={hash:new rr,map:new(or||nr),string:new rr}},pr=function(t){var e=cr(this,t).delete(t);return this.size-=e?1:0,e},hr=function(t){return ur(this,t).get(t)},yr=function(t){return lr(this,t).has(t)},dr=function(t,e){var r=sr(this,t),n=r.size;return r.set(t,e),this.size+=r.size==n?0:1,this};function vr(t){var e=-1,r=null==t?0:t.length;for(this.clear();++e<r;){var n=t[e];this.set(n[0],n[1])}}vr.prototype.clear=fr,vr.prototype.delete=pr,vr.prototype.get=hr,vr.prototype.has=yr,vr.prototype.set=dr;var mr=vr,br=mr;function gr(t,e){if("function"!=typeof t||null!=e&&"function"!=typeof e)throw new TypeError("Expected a function");var r=function(){var n=arguments,o=e?e.apply(this,n):n[0],i=r.cache;if(i.has(o))return i.get(o);var a=t.apply(this,n);return r.cache=i.set(o,a)||i,a};return r.cache=new(gr.Cache||br),r}gr.Cache=br;var wr=gr;const xr=r(wr);var Or=wr;var jr=/[^.[\]]+|\[(?:(-?\d+(?:\.\d+)?)|(["'])((?:(?!\2)[^\\]|\\.)*?)\2)\]|(?=(?:\.|\[\])(?:\.|\[\]|$))/g,Sr=/\\(\\)?/g,kr=function(t){var e=Or(t,function(t){return 500===r.size&&r.clear(),t}),r=e.cache;return e}(function(t){var e=[];return 46===t.charCodeAt(0)&&e.push(""),t.replace(jr,function(t,r,n,o){e.push(n?o.replace(Sr,"$1"):r||t)}),e});var Pr=function(t,e){for(var r=-1,n=null==t?0:t.length,o=Array(n);++r<n;)o[r]=e(t[r],r,t);return o},Ar=Pr,Er=kt,Mr=Ht,_r=_t?_t.prototype:void 0,Tr=_r?_r.toString:void 0;var Cr=function t(e){if("string"==typeof e)return e;if(Er(e))return Ar(e,t)+"";if(Mr(e))return Tr?Tr.call(e):"";var r=e+"";return"0"==r&&1/e==-1/0?"-0":r},Dr=Cr;var Ir=function(t){return null==t?"":Dr(t)},Nr=kt,Br=Kt,Lr=kr,Rr=Ir;var zr=function(t,e){return Nr(t)?t:Br(t,e)?[t]:Lr(Rr(t))},Ur=Ht;var $r=function(t){if("string"==typeof t||Ur(t))return t;var e=t+"";return"0"==e&&1/t==-1/0?"-0":e},qr=zr,Fr=$r;var Wr=function(t,e){for(var r=0,n=(e=qr(e,t)).length;null!=t&&r<n;)t=t[Fr(e[r++])];return r&&r==n?t:void 0},Hr=Wr;var Vr=function(t,e,r){var n=null==t?void 0:Hr(t,e);return void 0===n?r:n};const Xr=r(Vr);const Gr=r(function(t){return null==t});var Yr=$t,Kr=kt,Zr=qt;const Jr=r(function(t){return"string"==typeof t||!Kr(t)&&Zr(t)&&"[object String]"==Yr(t)});var Qr,tn={exports:{}},en={},rn=Symbol.for("react.element"),nn=Symbol.for("react.portal"),on=Symbol.for("react.fragment"),an=Symbol.for("react.strict_mode"),cn=Symbol.for("react.profiler"),un=Symbol.for("react.provider"),ln=Symbol.for("react.context"),sn=Symbol.for("react.server_context"),fn=Symbol.for("react.forward_ref"),pn=Symbol.for("react.suspense"),hn=Symbol.for("react.suspense_list"),yn=Symbol.for("react.memo"),dn=Symbol.for("react.lazy"),vn=Symbol.for("react.offscreen");function mn(t){if("object"==typeof t&&null!==t){var e=t.$$typeof;switch(e){case rn:switch(t=t.type){case on:case cn:case an:case pn:case hn:return t;default:switch(t=t&&t.$$typeof){case sn:case ln:case fn:case dn:case yn:case un:return t;default:return e}}case nn:return e}}}Qr=Symbol.for("react.module.reference"),en.ContextConsumer=ln,en.ContextProvider=un,en.Element=rn,en.ForwardRef=fn,en.Fragment=on,en.Lazy=dn,en.Memo=yn,en.Portal=nn,en.Profiler=cn,en.StrictMode=an,en.Suspense=pn,en.SuspenseList=hn,en.isAsyncMode=function(){return!1},en.isConcurrentMode=function(){return!1},en.isContextConsumer=function(t){return mn(t)===ln},en.isContextProvider=function(t){return mn(t)===un},en.isElement=function(t){return"object"==typeof t&&null!==t&&t.$$typeof===rn},en.isForwardRef=function(t){return mn(t)===fn},en.isFragment=function(t){return mn(t)===on},en.isLazy=function(t){return mn(t)===dn},en.isMemo=function(t){return mn(t)===yn},en.isPortal=function(t){return mn(t)===nn},en.isProfiler=function(t){return mn(t)===cn},en.isStrictMode=function(t){return mn(t)===an},en.isSuspense=function(t){return mn(t)===pn},en.isSuspenseList=function(t){return mn(t)===hn},en.isValidElementType=function(t){return"string"==typeof t||"function"==typeof t||t===on||t===cn||t===an||t===pn||t===hn||t===vn||"object"==typeof t&&null!==t&&(t.$$typeof===dn||t.$$typeof===yn||t.$$typeof===un||t.$$typeof===ln||t.$$typeof===fn||t.$$typeof===Qr||void 0!==t.getModuleId)},en.typeOf=mn,tn.exports=en;var bn=tn.exports,gn=$t,wn=qt;var xn=function(t){return"number"==typeof t||wn(t)&&"[object Number]"==gn(t)};const On=r(xn);var jn=xn;const Sn=r(function(t){return jn(t)&&t!=+t});var kn=function(t){return 0===t?0:t>0?1:-1},Pn=function(t){return Jr(t)&&t.indexOf("%")===t.length-1},An=function(t){return On(t)&&!Sn(t)},En=function(t){return An(t)||Jr(t)},Mn=0,_n=function(t){var e=++Mn;return"".concat(t||"").concat(e)},Tn=function(t,e){var r,n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0,o=arguments.length>3&&void 0!==arguments[3]&&arguments[3];if(!An(t)&&!Jr(t))return n;if(Pn(t)){var i=t.indexOf("%");r=e*parseFloat(t.slice(0,i))/100}else r=+t;return Sn(r)&&(r=n),o&&r>e&&(r=e),r},Cn=function(t){if(!t)return null;var e=Object.keys(t);return e&&e.length?t[e[0]]:null},Dn=function(t,e){return An(t)&&An(e)?function(r){return t+r*(e-t)}:function(){return e}};function In(t,e,r){return t&&t.length?t.find(function(t){return t&&("function"==typeof e?e(t):Xr(t,e))===r}):null}var Nn=function(t,e){return An(t)&&An(e)?t-e:Jr(t)&&Jr(e)?t.localeCompare(e):t instanceof Date&&e instanceof Date?t.getTime()-e.getTime():String(t).localeCompare(String(e))};function Bn(t,e){for(var r in t)if({}.hasOwnProperty.call(t,r)&&(!{}.hasOwnProperty.call(e,r)||t[r]!==e[r]))return!1;for(var n in e)if({}.hasOwnProperty.call(e,n)&&!{}.hasOwnProperty.call(t,n))return!1;return!0}function Ln(t){return(Ln="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}var Rn=["aria-activedescendant","aria-atomic","aria-autocomplete","aria-busy","aria-checked","aria-colcount","aria-colindex","aria-colspan","aria-controls","aria-current","aria-describedby","aria-details","aria-disabled","aria-errormessage","aria-expanded","aria-flowto","aria-haspopup","aria-hidden","aria-invalid","aria-keyshortcuts","aria-label","aria-labelledby","aria-level","aria-live","aria-modal","aria-multiline","aria-multiselectable","aria-orientation","aria-owns","aria-placeholder","aria-posinset","aria-pressed","aria-readonly","aria-relevant","aria-required","aria-roledescription","aria-rowcount","aria-rowindex","aria-rowspan","aria-selected","aria-setsize","aria-sort","aria-valuemax","aria-valuemin","aria-valuenow","aria-valuetext","className","color","height","id","lang","max","media","method","min","name","style","target","width","role","tabIndex","accentHeight","accumulate","additive","alignmentBaseline","allowReorder","alphabetic","amplitude","arabicForm","ascent","attributeName","attributeType","autoReverse","azimuth","baseFrequency","baselineShift","baseProfile","bbox","begin","bias","by","calcMode","capHeight","clip","clipPath","clipPathUnits","clipRule","colorInterpolation","colorInterpolationFilters","colorProfile","colorRendering","contentScriptType","contentStyleType","cursor","cx","cy","d","decelerate","descent","diffuseConstant","direction","display","divisor","dominantBaseline","dur","dx","dy","edgeMode","elevation","enableBackground","end","exponent","externalResourcesRequired","fill","fillOpacity","fillRule","filter","filterRes","filterUnits","floodColor","floodOpacity","focusable","fontFamily","fontSize","fontSizeAdjust","fontStretch","fontStyle","fontVariant","fontWeight","format","from","fx","fy","g1","g2","glyphName","glyphOrientationHorizontal","glyphOrientationVertical","glyphRef","gradientTransform","gradientUnits","hanging","horizAdvX","horizOriginX","href","ideographic","imageRendering","in2","in","intercept","k1","k2","k3","k4","k","kernelMatrix","kernelUnitLength","kerning","keyPoints","keySplines","keyTimes","lengthAdjust","letterSpacing","lightingColor","limitingConeAngle","local","markerEnd","markerHeight","markerMid","markerStart","markerUnits","markerWidth","mask","maskContentUnits","maskUnits","mathematical","mode","numOctaves","offset","opacity","operator","order","orient","orientation","origin","overflow","overlinePosition","overlineThickness","paintOrder","panose1","pathLength","patternContentUnits","patternTransform","patternUnits","pointerEvents","pointsAtX","pointsAtY","pointsAtZ","preserveAlpha","preserveAspectRatio","primitiveUnits","r","radius","refX","refY","renderingIntent","repeatCount","repeatDur","requiredExtensions","requiredFeatures","restart","result","rotate","rx","ry","seed","shapeRendering","slope","spacing","specularConstant","specularExponent","speed","spreadMethod","startOffset","stdDeviation","stemh","stemv","stitchTiles","stopColor","stopOpacity","strikethroughPosition","strikethroughThickness","string","stroke","strokeDasharray","strokeDashoffset","strokeLinecap","strokeLinejoin","strokeMiterlimit","strokeOpacity","strokeWidth","surfaceScale","systemLanguage","tableValues","targetX","targetY","textAnchor","textDecoration","textLength","textRendering","to","transform","u1","u2","underlinePosition","underlineThickness","unicode","unicodeBidi","unicodeRange","unitsPerEm","vAlphabetic","values","vectorEffect","version","vertAdvY","vertOriginX","vertOriginY","vHanging","vIdeographic","viewTarget","visibility","vMathematical","widths","wordSpacing","writingMode","x1","x2","x","xChannelSelector","xHeight","xlinkActuate","xlinkArcrole","xlinkHref","xlinkRole","xlinkShow","xlinkTitle","xlinkType","xmlBase","xmlLang","xmlns","xmlnsXlink","xmlSpace","y1","y2","y","yChannelSelector","z","zoomAndPan","ref","key","angle"],zn=["points","pathLength"],Un={svg:["viewBox","children"],polygon:zn,polyline:zn},$n=["dangerouslySetInnerHTML","onCopy","onCopyCapture","onCut","onCutCapture","onPaste","onPasteCapture","onCompositionEnd","onCompositionEndCapture","onCompositionStart","onCompositionStartCapture","onCompositionUpdate","onCompositionUpdateCapture","onFocus","onFocusCapture","onBlur","onBlurCapture","onChange","onChangeCapture","onBeforeInput","onBeforeInputCapture","onInput","onInputCapture","onReset","onResetCapture","onSubmit","onSubmitCapture","onInvalid","onInvalidCapture","onLoad","onLoadCapture","onError","onErrorCapture","onKeyDown","onKeyDownCapture","onKeyPress","onKeyPressCapture","onKeyUp","onKeyUpCapture","onAbort","onAbortCapture","onCanPlay","onCanPlayCapture","onCanPlayThrough","onCanPlayThroughCapture","onDurationChange","onDurationChangeCapture","onEmptied","onEmptiedCapture","onEncrypted","onEncryptedCapture","onEnded","onEndedCapture","onLoadedData","onLoadedDataCapture","onLoadedMetadata","onLoadedMetadataCapture","onLoadStart","onLoadStartCapture","onPause","onPauseCapture","onPlay","onPlayCapture","onPlaying","onPlayingCapture","onProgress","onProgressCapture","onRateChange","onRateChangeCapture","onSeeked","onSeekedCapture","onSeeking","onSeekingCapture","onStalled","onStalledCapture","onSuspend","onSuspendCapture","onTimeUpdate","onTimeUpdateCapture","onVolumeChange","onVolumeChangeCapture","onWaiting","onWaitingCapture","onAuxClick","onAuxClickCapture","onClick","onClickCapture","onContextMenu","onContextMenuCapture","onDoubleClick","onDoubleClickCapture","onDrag","onDragCapture","onDragEnd","onDragEndCapture","onDragEnter","onDragEnterCapture","onDragExit","onDragExitCapture","onDragLeave","onDragLeaveCapture","onDragOver","onDragOverCapture","onDragStart","onDragStartCapture","onDrop","onDropCapture","onMouseDown","onMouseDownCapture","onMouseEnter","onMouseLeave","onMouseMove","onMouseMoveCapture","onMouseOut","onMouseOutCapture","onMouseOver","onMouseOverCapture","onMouseUp","onMouseUpCapture","onSelect","onSelectCapture","onTouchCancel","onTouchCancelCapture","onTouchEnd","onTouchEndCapture","onTouchMove","onTouchMoveCapture","onTouchStart","onTouchStartCapture","onPointerDown","onPointerDownCapture","onPointerMove","onPointerMoveCapture","onPointerUp","onPointerUpCapture","onPointerCancel","onPointerCancelCapture","onPointerEnter","onPointerEnterCapture","onPointerLeave","onPointerLeaveCapture","onPointerOver","onPointerOverCapture","onPointerOut","onPointerOutCapture","onGotPointerCapture","onGotPointerCaptureCapture","onLostPointerCapture","onLostPointerCaptureCapture","onScroll","onScrollCapture","onWheel","onWheelCapture","onAnimationStart","onAnimationStartCapture","onAnimationEnd","onAnimationEndCapture","onAnimationIteration","onAnimationIterationCapture","onTransitionEnd","onTransitionEndCapture"],qn=function(e,r){if(!e||"function"==typeof e||"boolean"==typeof e)return null;var n=e;if(t.isValidElement(e)&&(n=e.props),!Jt(n))return null;var o={};return Object.keys(n).forEach(function(t){$n.includes(t)&&(o[t]=r||function(e){return n[t](n,e)})}),o},Fn=function(t,e,r){if(!Jt(t)||"object"!==Ln(t))return null;var n=null;return Object.keys(t).forEach(function(o){var i=t[o];$n.includes(o)&&"function"==typeof i&&(n||(n={}),n[o]=function(t,e,r){return function(n){return t(e,r,n),null}}(i,e,r))}),n},Wn=["children"],Hn=["children"];function Vn(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],e.indexOf(r)>=0||Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}function Xn(t){return(Xn="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}var Gn={click:"onClick",mousedown:"onMouseDown",mouseup:"onMouseUp",mouseover:"onMouseOver",mousemove:"onMouseMove",mouseout:"onMouseOut",mouseenter:"onMouseEnter",mouseleave:"onMouseLeave",touchcancel:"onTouchCancel",touchend:"onTouchEnd",touchmove:"onTouchMove",touchstart:"onTouchStart",contextmenu:"onContextMenu",dblclick:"onDoubleClick"},Yn=function(t){return"string"==typeof t?t:t?t.displayName||t.name||"Component":""},Kn=null,Zn=null,Jn=function e(r){if(r===Kn&&Array.isArray(Zn))return Zn;var n=[];return t.Children.forEach(r,function(t){Gr(t)||(bn.isFragment(t)?n=n.concat(e(t.props.children)):n.push(t))}),Zn=n,Kn=r,n};function Qn(t,e){var r=[],n=[];return n=Array.isArray(e)?e.map(function(t){return Yn(t)}):[Yn(e)],Jn(t).forEach(function(t){var e=Xr(t,"type.displayName")||Xr(t,"type.name");-1!==n.indexOf(e)&&r.push(t)}),r}function to(t,e){var r=Qn(t,e);return r&&r[0]}var eo=function(t){if(!t||!t.props)return!1;var e=t.props,r=e.width,n=e.height;return!(!An(r)||r<=0||!An(n)||n<=0)},ro=["a","altGlyph","altGlyphDef","altGlyphItem","animate","animateColor","animateMotion","animateTransform","circle","clipPath","color-profile","cursor","defs","desc","ellipse","feBlend","feColormatrix","feComponentTransfer","feComposite","feConvolveMatrix","feDiffuseLighting","feDisplacementMap","feDistantLight","feFlood","feFuncA","feFuncB","feFuncG","feFuncR","feGaussianBlur","feImage","feMerge","feMergeNode","feMorphology","feOffset","fePointLight","feSpecularLighting","feSpotLight","feTile","feTurbulence","filter","font","font-face","font-face-format","font-face-name","font-face-url","foreignObject","g","glyph","glyphRef","hkern","image","line","lineGradient","marker","mask","metadata","missing-glyph","mpath","path","pattern","polygon","polyline","radialGradient","rect","script","set","stop","style","svg","switch","symbol","text","textPath","title","tref","tspan","use","view","vkern"],no=function(e,r,n){if(!e||"function"==typeof e||"boolean"==typeof e)return null;var o=e;if(t.isValidElement(e)&&(o=e.props),!Jt(o))return null;var i={};return Object.keys(o).forEach(function(t){var e;(function(t,e,r,n){var o,i=null!==(o=null==Un?void 0:Un[n])&&void 0!==o?o:[];return e.startsWith("data-")||!re(t)&&(n&&i.includes(e)||Rn.includes(e))||r&&$n.includes(e)})(null===(e=o)||void 0===e?void 0:e[t],t,r,n)&&(i[t]=o[t])}),i},oo=function e(r,n){if(r===n)return!0;var o=t.Children.count(r);if(o!==t.Children.count(n))return!1;if(0===o)return!0;if(1===o)return io(Array.isArray(r)?r[0]:r,Array.isArray(n)?n[0]:n);for(var i=0;i<o;i++){var a=r[i],c=n[i];if(Array.isArray(a)||Array.isArray(c)){if(!e(a,c))return!1}else if(!io(a,c))return!1}return!0},io=function(t,e){if(Gr(t)&&Gr(e))return!0;if(!Gr(t)&&!Gr(e)){var r=t.props||{},n=r.children,o=Vn(r,Wn),i=e.props||{},a=i.children,c=Vn(i,Hn);return n&&a?Bn(o,c)&&oo(n,a):!n&&!a&&Bn(o,c)}return!1},ao=function(t,e){var r=[],n={};return Jn(t).forEach(function(t,o){if(function(t){return t&&t.type&&Jr(t.type)&&ro.indexOf(t.type)>=0}(t))r.push(t);else if(t){var i=Yn(t.type),a=e[i]||{},c=a.handler,u=a.once;if(c&&(!u||!n[i])){var l=c(t,i,o);r.push(l),n[i]=!0}}}),r},co=["children","width","height","viewBox","className","style","title","desc"];function uo(){return uo=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},uo.apply(this,arguments)}function lo(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],e.indexOf(r)>=0||Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}function so(t){var e=t.children,r=t.width,o=t.height,i=t.viewBox,a=t.className,c=t.style,u=t.title,l=t.desc,s=lo(t,co),f=i||{width:r,height:o,x:0,y:0},p=St("recharts-surface",a);return n.createElement("svg",uo({},no(s,!0,"svg"),{className:p,width:r,height:o,style:c,viewBox:"".concat(f.x," ").concat(f.y," ").concat(f.width," ").concat(f.height)}),n.createElement("title",null,u),n.createElement("desc",null,l),e)}var fo=["children","className"];function po(){return po=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},po.apply(this,arguments)}function ho(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],e.indexOf(r)>=0||Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}var yo=n.forwardRef(function(t,e){var r=t.children,o=t.className,i=ho(t,fo),a=St("recharts-layer",o);return n.createElement("g",po({className:a},no(i,!0),{ref:e}),r)}),vo=function(t,e){for(var r=arguments.length,n=new Array(r>2?r-2:0),o=2;o<r;o++)n[o-2]=arguments[o]};var mo=function(t,e,r){var n=-1,o=t.length;e<0&&(e=-e>o?0:o+e),(r=r>o?o:r)<0&&(r+=o),o=e>r?0:r-e>>>0,e>>>=0;for(var i=Array(o);++n<o;)i[n]=t[n+e];return i};var bo=function(t,e,r){var n=t.length;return r=void 0===r?n:r,!e&&r>=n?t:mo(t,e,r)},go=RegExp("[\\u200d\\ud800-\\udfff\\u0300-\\u036f\\ufe20-\\ufe2f\\u20d0-\\u20ff\\ufe0e\\ufe0f]");var wo=function(t){return go.test(t)};var xo=function(t){return t.split("")},Oo="\\ud800-\\udfff",jo="["+Oo+"]",So="[\\u0300-\\u036f\\ufe20-\\ufe2f\\u20d0-\\u20ff]",ko="\\ud83c[\\udffb-\\udfff]",Po="[^"+Oo+"]",Ao="(?:\\ud83c[\\udde6-\\uddff]){2}",Eo="[\\ud800-\\udbff][\\udc00-\\udfff]",Mo="(?:"+So+"|"+ko+")"+"?",_o="[\\ufe0e\\ufe0f]?",To=_o+Mo+("(?:\\u200d(?:"+[Po,Ao,Eo].join("|")+")"+_o+Mo+")*"),Co="(?:"+[Po+So+"?",So,Ao,Eo,jo].join("|")+")",Do=RegExp(ko+"(?="+ko+")|"+Co+To,"g");var Io=xo,No=wo,Bo=function(t){return t.match(Do)||[]};var Lo=bo,Ro=wo,zo=function(t){return No(t)?Bo(t):Io(t)},Uo=Ir;const $o=r(function(t){return function(e){e=Uo(e);var r=Ro(e)?zo(e):void 0,n=r?r[0]:e.charAt(0),o=r?Lo(r,1).join(""):e.slice(1);return n[t]()+o}}("toUpperCase"));function qo(t){return function(){return t}}const Fo=Math.cos,Wo=Math.sin,Ho=Math.sqrt,Vo=Math.PI,Xo=2*Vo,Go=Math.PI,Yo=2*Go,Ko=1e-6,Zo=Yo-Ko;function Jo(t){this._+=t[0];for(let e=1,r=t.length;e<r;++e)this._+=arguments[e]+t[e]}class Qo{constructor(t){this._x0=this._y0=this._x1=this._y1=null,this._="",this._append=null==t?Jo:function(t){let e=Math.floor(t);if(!(e>=0))throw new Error(`invalid digits: ${t}`);if(e>15)return Jo;const r=10**e;return function(t){this._+=t[0];for(let e=1,n=t.length;e<n;++e)this._+=Math.round(arguments[e]*r)/r+t[e]}}(t)}moveTo(t,e){this._append`M${this._x0=this._x1=+t},${this._y0=this._y1=+e}`}closePath(){null!==this._x1&&(this._x1=this._x0,this._y1=this._y0,this._append`Z`)}lineTo(t,e){this._append`L${this._x1=+t},${this._y1=+e}`}quadraticCurveTo(t,e,r,n){this._append`Q${+t},${+e},${this._x1=+r},${this._y1=+n}`}bezierCurveTo(t,e,r,n,o,i){this._append`C${+t},${+e},${+r},${+n},${this._x1=+o},${this._y1=+i}`}arcTo(t,e,r,n,o){if(t=+t,e=+e,r=+r,n=+n,(o=+o)<0)throw new Error(`negative radius: ${o}`);let i=this._x1,a=this._y1,c=r-t,u=n-e,l=i-t,s=a-e,f=l*l+s*s;if(null===this._x1)this._append`M${this._x1=t},${this._y1=e}`;else if(f>Ko)if(Math.abs(s*c-u*l)>Ko&&o){let p=r-i,h=n-a,y=c*c+u*u,d=p*p+h*h,v=Math.sqrt(y),m=Math.sqrt(f),b=o*Math.tan((Go-Math.acos((y+f-d)/(2*v*m)))/2),g=b/m,w=b/v;Math.abs(g-1)>Ko&&this._append`L${t+g*l},${e+g*s}`,this._append`A${o},${o},0,0,${+(s*p>l*h)},${this._x1=t+w*c},${this._y1=e+w*u}`}else this._append`L${this._x1=t},${this._y1=e}`;else;}arc(t,e,r,n,o,i){if(t=+t,e=+e,i=!!i,(r=+r)<0)throw new Error(`negative radius: ${r}`);let a=r*Math.cos(n),c=r*Math.sin(n),u=t+a,l=e+c,s=1^i,f=i?n-o:o-n;null===this._x1?this._append`M${u},${l}`:(Math.abs(this._x1-u)>Ko||Math.abs(this._y1-l)>Ko)&&this._append`L${u},${l}`,r&&(f<0&&(f=f%Yo+Yo),f>Zo?this._append`A${r},${r},0,1,${s},${t-a},${e-c}A${r},${r},0,1,${s},${this._x1=u},${this._y1=l}`:f>Ko&&this._append`A${r},${r},0,${+(f>=Go)},${s},${this._x1=t+r*Math.cos(o)},${this._y1=e+r*Math.sin(o)}`)}rect(t,e,r,n){this._append`M${this._x0=this._x1=+t},${this._y0=this._y1=+e}h${r=+r}v${+n}h${-r}Z`}toString(){return this._}}function ti(t){let e=3;return t.digits=function(r){if(!arguments.length)return e;if(null==r)e=null;else{const t=Math.floor(r);if(!(t>=0))throw new RangeError(`invalid digits: ${r}`);e=t}return t},()=>new Qo(e)}function ei(t){return"object"==typeof t&&"length"in t?t:Array.from(t)}function ri(t){this._context=t}function ni(t){return new ri(t)}function oi(t){return t[0]}function ii(t){return t[1]}function ai(t,e){var r=qo(!0),n=null,o=ni,i=null,a=ti(c);function c(c){var u,l,s,f=(c=ei(c)).length,p=!1;for(null==n&&(i=o(s=a())),u=0;u<=f;++u)!(u<f&&r(l=c[u],u,c))===p&&((p=!p)?i.lineStart():i.lineEnd()),p&&i.point(+t(l,u,c),+e(l,u,c));if(s)return i=null,s+""||null}return t="function"==typeof t?t:void 0===t?oi:qo(t),e="function"==typeof e?e:void 0===e?ii:qo(e),c.x=function(e){return arguments.length?(t="function"==typeof e?e:qo(+e),c):t},c.y=function(t){return arguments.length?(e="function"==typeof t?t:qo(+t),c):e},c.defined=function(t){return arguments.length?(r="function"==typeof t?t:qo(!!t),c):r},c.curve=function(t){return arguments.length?(o=t,null!=n&&(i=o(n)),c):o},c.context=function(t){return arguments.length?(null==t?n=i=null:i=o(n=t),c):n},c}function ci(t,e,r){var n=null,o=qo(!0),i=null,a=ni,c=null,u=ti(l);function l(l){var s,f,p,h,y,d=(l=ei(l)).length,v=!1,m=new Array(d),b=new Array(d);for(null==i&&(c=a(y=u())),s=0;s<=d;++s){if(!(s<d&&o(h=l[s],s,l))===v)if(v=!v)f=s,c.areaStart(),c.lineStart();else{for(c.lineEnd(),c.lineStart(),p=s-1;p>=f;--p)c.point(m[p],b[p]);c.lineEnd(),c.areaEnd()}v&&(m[s]=+t(h,s,l),b[s]=+e(h,s,l),c.point(n?+n(h,s,l):m[s],r?+r(h,s,l):b[s]))}if(y)return c=null,y+""||null}function s(){return ai().defined(o).curve(a).context(i)}return t="function"==typeof t?t:void 0===t?oi:qo(+t),e="function"==typeof e?e:qo(void 0===e?0:+e),r="function"==typeof r?r:void 0===r?ii:qo(+r),l.x=function(e){return arguments.length?(t="function"==typeof e?e:qo(+e),n=null,l):t},l.x0=function(e){return arguments.length?(t="function"==typeof e?e:qo(+e),l):t},l.x1=function(t){return arguments.length?(n=null==t?null:"function"==typeof t?t:qo(+t),l):n},l.y=function(t){return arguments.length?(e="function"==typeof t?t:qo(+t),r=null,l):e},l.y0=function(t){return arguments.length?(e="function"==typeof t?t:qo(+t),l):e},l.y1=function(t){return arguments.length?(r=null==t?null:"function"==typeof t?t:qo(+t),l):r},l.lineX0=l.lineY0=function(){return s().x(t).y(e)},l.lineY1=function(){return s().x(t).y(r)},l.lineX1=function(){return s().x(n).y(e)},l.defined=function(t){return arguments.length?(o="function"==typeof t?t:qo(!!t),l):o},l.curve=function(t){return arguments.length?(a=t,null!=i&&(c=a(i)),l):a},l.context=function(t){return arguments.length?(null==t?i=c=null:c=a(i=t),l):i},l}ri.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._point=0},lineEnd:function(){(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(t,e){switch(t=+t,e=+e,this._point){case 0:this._point=1,this._line?this._context.lineTo(t,e):this._context.moveTo(t,e);break;case 1:this._point=2;default:this._context.lineTo(t,e)}}};class ui{constructor(t,e){this._context=t,this._x=e}areaStart(){this._line=0}areaEnd(){this._line=NaN}lineStart(){this._point=0}lineEnd(){(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line}point(t,e){switch(t=+t,e=+e,this._point){case 0:this._point=1,this._line?this._context.lineTo(t,e):this._context.moveTo(t,e);break;case 1:this._point=2;default:this._x?this._context.bezierCurveTo(this._x0=(this._x0+t)/2,this._y0,this._x0,e,t,e):this._context.bezierCurveTo(this._x0,this._y0=(this._y0+e)/2,t,this._y0,t,e)}this._x0=t,this._y0=e}}const li={draw(t,e){const r=Ho(e/Vo);t.moveTo(r,0),t.arc(0,0,r,0,Xo)}},si={draw(t,e){const r=Ho(e/5)/2;t.moveTo(-3*r,-r),t.lineTo(-r,-r),t.lineTo(-r,-3*r),t.lineTo(r,-3*r),t.lineTo(r,-r),t.lineTo(3*r,-r),t.lineTo(3*r,r),t.lineTo(r,r),t.lineTo(r,3*r),t.lineTo(-r,3*r),t.lineTo(-r,r),t.lineTo(-3*r,r),t.closePath()}},fi=Ho(1/3),pi=2*fi,hi={draw(t,e){const r=Ho(e/pi),n=r*fi;t.moveTo(0,-r),t.lineTo(n,0),t.lineTo(0,r),t.lineTo(-n,0),t.closePath()}},yi={draw(t,e){const r=Ho(e),n=-r/2;t.rect(n,n,r,r)}},di=Wo(Vo/10)/Wo(7*Vo/10),vi=Wo(Xo/10)*di,mi=-Fo(Xo/10)*di,bi={draw(t,e){const r=Ho(.8908130915292852*e),n=vi*r,o=mi*r;t.moveTo(0,-r),t.lineTo(n,o);for(let i=1;i<5;++i){const e=Xo*i/5,a=Fo(e),c=Wo(e);t.lineTo(c*r,-a*r),t.lineTo(a*n-c*o,c*n+a*o)}t.closePath()}},gi=Ho(3),wi={draw(t,e){const r=-Ho(e/(3*gi));t.moveTo(0,2*r),t.lineTo(-gi*r,-r),t.lineTo(gi*r,-r),t.closePath()}},xi=-.5,Oi=Ho(3)/2,ji=1/Ho(12),Si=3*(ji/2+1),ki={draw(t,e){const r=Ho(e/Si),n=r/2,o=r*ji,i=n,a=r*ji+r,c=-i,u=a;t.moveTo(n,o),t.lineTo(i,a),t.lineTo(c,u),t.lineTo(xi*n-Oi*o,Oi*n+xi*o),t.lineTo(xi*i-Oi*a,Oi*i+xi*a),t.lineTo(xi*c-Oi*u,Oi*c+xi*u),t.lineTo(xi*n+Oi*o,xi*o-Oi*n),t.lineTo(xi*i+Oi*a,xi*a-Oi*i),t.lineTo(xi*c+Oi*u,xi*u-Oi*c),t.closePath()}};function Pi(){}function Ai(t,e,r){t._context.bezierCurveTo((2*t._x0+t._x1)/3,(2*t._y0+t._y1)/3,(t._x0+2*t._x1)/3,(t._y0+2*t._y1)/3,(t._x0+4*t._x1+e)/6,(t._y0+4*t._y1+r)/6)}function Ei(t){this._context=t}function Mi(t){this._context=t}function _i(t){this._context=t}function Ti(t){this._context=t}function Ci(t){return t<0?-1:1}function Di(t,e,r){var n=t._x1-t._x0,o=e-t._x1,i=(t._y1-t._y0)/(n||o<0&&-0),a=(r-t._y1)/(o||n<0&&-0),c=(i*o+a*n)/(n+o);return(Ci(i)+Ci(a))*Math.min(Math.abs(i),Math.abs(a),.5*Math.abs(c))||0}function Ii(t,e){var r=t._x1-t._x0;return r?(3*(t._y1-t._y0)/r-e)/2:e}function Ni(t,e,r){var n=t._x0,o=t._y0,i=t._x1,a=t._y1,c=(i-n)/3;t._context.bezierCurveTo(n+c,o+c*e,i-c,a-c*r,i,a)}function Bi(t){this._context=t}function Li(t){this._context=new Ri(t)}function Ri(t){this._context=t}function zi(t){this._context=t}function Ui(t){var e,r,n=t.length-1,o=new Array(n),i=new Array(n),a=new Array(n);for(o[0]=0,i[0]=2,a[0]=t[0]+2*t[1],e=1;e<n-1;++e)o[e]=1,i[e]=4,a[e]=4*t[e]+2*t[e+1];for(o[n-1]=2,i[n-1]=7,a[n-1]=8*t[n-1]+t[n],e=1;e<n;++e)r=o[e]/i[e-1],i[e]-=r,a[e]-=r*a[e-1];for(o[n-1]=a[n-1]/i[n-1],e=n-2;e>=0;--e)o[e]=(a[e]-o[e+1])/i[e];for(i[n-1]=(t[n]+o[n-1])/2,e=0;e<n-1;++e)i[e]=2*t[e+1]-o[e+1];return[o,i]}function $i(t,e){this._context=t,this._t=e}function qi(t,e){if((o=t.length)>1)for(var r,n,o,i=1,a=t[e[0]],c=a.length;i<o;++i)for(n=a,a=t[e[i]],r=0;r<c;++r)a[r][1]+=a[r][0]=isNaN(n[r][1])?n[r][0]:n[r][1]}function Fi(t){for(var e=t.length,r=new Array(e);--e>=0;)r[e]=e;return r}function Wi(t,e){return t[e]}function Hi(t){const e=[];return e.key=t,e}function Vi(t){return(Vi="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}Ei.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._y0=this._y1=NaN,this._point=0},lineEnd:function(){switch(this._point){case 3:Ai(this,this._x1,this._y1);case 2:this._context.lineTo(this._x1,this._y1)}(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(t,e){switch(t=+t,e=+e,this._point){case 0:this._point=1,this._line?this._context.lineTo(t,e):this._context.moveTo(t,e);break;case 1:this._point=2;break;case 2:this._point=3,this._context.lineTo((5*this._x0+this._x1)/6,(5*this._y0+this._y1)/6);default:Ai(this,t,e)}this._x0=this._x1,this._x1=t,this._y0=this._y1,this._y1=e}},Mi.prototype={areaStart:Pi,areaEnd:Pi,lineStart:function(){this._x0=this._x1=this._x2=this._x3=this._x4=this._y0=this._y1=this._y2=this._y3=this._y4=NaN,this._point=0},lineEnd:function(){switch(this._point){case 1:this._context.moveTo(this._x2,this._y2),this._context.closePath();break;case 2:this._context.moveTo((this._x2+2*this._x3)/3,(this._y2+2*this._y3)/3),this._context.lineTo((this._x3+2*this._x2)/3,(this._y3+2*this._y2)/3),this._context.closePath();break;case 3:this.point(this._x2,this._y2),this.point(this._x3,this._y3),this.point(this._x4,this._y4)}},point:function(t,e){switch(t=+t,e=+e,this._point){case 0:this._point=1,this._x2=t,this._y2=e;break;case 1:this._point=2,this._x3=t,this._y3=e;break;case 2:this._point=3,this._x4=t,this._y4=e,this._context.moveTo((this._x0+4*this._x1+t)/6,(this._y0+4*this._y1+e)/6);break;default:Ai(this,t,e)}this._x0=this._x1,this._x1=t,this._y0=this._y1,this._y1=e}},_i.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._y0=this._y1=NaN,this._point=0},lineEnd:function(){(this._line||0!==this._line&&3===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(t,e){switch(t=+t,e=+e,this._point){case 0:this._point=1;break;case 1:this._point=2;break;case 2:this._point=3;var r=(this._x0+4*this._x1+t)/6,n=(this._y0+4*this._y1+e)/6;this._line?this._context.lineTo(r,n):this._context.moveTo(r,n);break;case 3:this._point=4;default:Ai(this,t,e)}this._x0=this._x1,this._x1=t,this._y0=this._y1,this._y1=e}},Ti.prototype={areaStart:Pi,areaEnd:Pi,lineStart:function(){this._point=0},lineEnd:function(){this._point&&this._context.closePath()},point:function(t,e){t=+t,e=+e,this._point?this._context.lineTo(t,e):(this._point=1,this._context.moveTo(t,e))}},Bi.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._y0=this._y1=this._t0=NaN,this._point=0},lineEnd:function(){switch(this._point){case 2:this._context.lineTo(this._x1,this._y1);break;case 3:Ni(this,this._t0,Ii(this,this._t0))}(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(t,e){var r=NaN;if(e=+e,(t=+t)!==this._x1||e!==this._y1){switch(this._point){case 0:this._point=1,this._line?this._context.lineTo(t,e):this._context.moveTo(t,e);break;case 1:this._point=2;break;case 2:this._point=3,Ni(this,Ii(this,r=Di(this,t,e)),r);break;default:Ni(this,this._t0,r=Di(this,t,e))}this._x0=this._x1,this._x1=t,this._y0=this._y1,this._y1=e,this._t0=r}}},(Li.prototype=Object.create(Bi.prototype)).point=function(t,e){Bi.prototype.point.call(this,e,t)},Ri.prototype={moveTo:function(t,e){this._context.moveTo(e,t)},closePath:function(){this._context.closePath()},lineTo:function(t,e){this._context.lineTo(e,t)},bezierCurveTo:function(t,e,r,n,o,i){this._context.bezierCurveTo(e,t,n,r,i,o)}},zi.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x=[],this._y=[]},lineEnd:function(){var t=this._x,e=this._y,r=t.length;if(r)if(this._line?this._context.lineTo(t[0],e[0]):this._context.moveTo(t[0],e[0]),2===r)this._context.lineTo(t[1],e[1]);else for(var n=Ui(t),o=Ui(e),i=0,a=1;a<r;++i,++a)this._context.bezierCurveTo(n[0][i],o[0][i],n[1][i],o[1][i],t[a],e[a]);(this._line||0!==this._line&&1===r)&&this._context.closePath(),this._line=1-this._line,this._x=this._y=null},point:function(t,e){this._x.push(+t),this._y.push(+e)}},$i.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x=this._y=NaN,this._point=0},lineEnd:function(){0<this._t&&this._t<1&&2===this._point&&this._context.lineTo(this._x,this._y),(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line>=0&&(this._t=1-this._t,this._line=1-this._line)},point:function(t,e){switch(t=+t,e=+e,this._point){case 0:this._point=1,this._line?this._context.lineTo(t,e):this._context.moveTo(t,e);break;case 1:this._point=2;default:if(this._t<=0)this._context.lineTo(this._x,e),this._context.lineTo(t,e);else{var r=this._x*(1-this._t)+t*this._t;this._context.lineTo(r,this._y),this._context.lineTo(r,e)}}this._x=t,this._y=e}};var Xi=["type","size","sizeType"];function Gi(){return Gi=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},Gi.apply(this,arguments)}function Yi(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function Ki(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?Yi(Object(r),!0).forEach(function(e){Zi(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):Yi(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function Zi(t,e,r){var n;return n=function(t,e){if("object"!=Vi(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=Vi(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(e,"string"),(e="symbol"==Vi(n)?n:n+"")in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function Ji(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],e.indexOf(r)>=0||Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}var Qi={symbolCircle:li,symbolCross:si,symbolDiamond:hi,symbolSquare:yi,symbolStar:bi,symbolTriangle:wi,symbolWye:ki},ta=Math.PI/180,ea=function(t){var e,r,o=t.type,i=void 0===o?"circle":o,a=t.size,c=void 0===a?64:a,u=t.sizeType,l=void 0===u?"area":u,s=Ki(Ki({},Ji(t,Xi)),{},{type:i,size:c,sizeType:l}),f=s.className,p=s.cx,h=s.cy,y=no(s,!0);return p===+p&&h===+h&&c===+c?n.createElement("path",Gi({},y,{className:St("recharts-symbols",f),transform:"translate(".concat(p,", ").concat(h,")"),d:(e=function(t){var e="symbol".concat($o(t));return Qi[e]||li}(i),r=function(t,e){let r=null,n=ti(o);function o(){let o;if(r||(r=o=n()),t.apply(this,arguments).draw(r,+e.apply(this,arguments)),o)return r=null,o+""||null}return t="function"==typeof t?t:qo(t||li),e="function"==typeof e?e:qo(void 0===e?64:+e),o.type=function(e){return arguments.length?(t="function"==typeof e?e:qo(e),o):t},o.size=function(t){return arguments.length?(e="function"==typeof t?t:qo(+t),o):e},o.context=function(t){return arguments.length?(r=null==t?null:t,o):r},o}().type(e).size(function(t,e,r){if("area"===e)return t;switch(r){case"cross":return 5*t*t/9;case"diamond":return.5*t*t/Math.sqrt(3);case"square":return t*t;case"star":var n=18*ta;return 1.25*t*t*(Math.tan(n)-Math.tan(2*n)*Math.pow(Math.tan(n),2));case"triangle":return Math.sqrt(3)*t*t/4;case"wye":return(21-10*Math.sqrt(3))*t*t/8;default:return Math.PI*t*t/4}}(c,l,i)),r())})):null};function ra(t){return(ra="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function na(){return na=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},na.apply(this,arguments)}function oa(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function ia(t,e,r){return e&&function(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,fa(n.key),n)}}(t.prototype,e),Object.defineProperty(t,"prototype",{writable:!1}),t}function aa(t,e,r){return e=ua(e),function(t,e){if(e&&("object"===ra(e)||"function"==typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(t,ca()?Reflect.construct(e,r||[],ua(t).constructor):e.apply(t,r))}function ca(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(e){}return(ca=function(){return!!t})()}function ua(t){return(ua=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function la(t,e){return(la=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function sa(t,e,r){return(e=fa(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function fa(t){var e=function(t,e){if("object"!=ra(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=ra(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==ra(e)?e:e+""}ea.registerSymbol=function(t,e){Qi["symbol".concat($o(t))]=e};var pa=32,ha=function(){function e(){return function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,e),aa(this,e,arguments)}return function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&la(t,e)}(e,t.PureComponent),ia(e,[{key:"renderIcon",value:function(t){var e=this.props.inactiveColor,r=16,o=pa/6,i=pa/3,a=t.inactive?e:t.color;if("plainline"===t.type)return n.createElement("line",{strokeWidth:4,fill:"none",stroke:a,strokeDasharray:t.payload.strokeDasharray,x1:0,y1:r,x2:pa,y2:r,className:"recharts-legend-icon"});if("line"===t.type)return n.createElement("path",{strokeWidth:4,fill:"none",stroke:a,d:"M0,".concat(r,"h").concat(i,"\n            A").concat(o,",").concat(o,",0,1,1,").concat(2*i,",").concat(r,"\n            H").concat(pa,"M").concat(2*i,",").concat(r,"\n            A").concat(o,",").concat(o,",0,1,1,").concat(i,",").concat(r),className:"recharts-legend-icon"});if("rect"===t.type)return n.createElement("path",{stroke:"none",fill:a,d:"M0,".concat(4,"h").concat(pa,"v").concat(24,"h").concat(-32,"z"),className:"recharts-legend-icon"});if(n.isValidElement(t.legendIcon)){var c=function(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?oa(Object(r),!0).forEach(function(e){sa(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):oa(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}({},t);return delete c.legendIcon,n.cloneElement(t.legendIcon,c)}return n.createElement(ea,{fill:a,cx:r,cy:r,size:pa,sizeType:"diameter",type:t.type})}},{key:"renderItems",value:function(){var t=this,e=this.props,r=e.payload,o=e.iconSize,i=e.layout,a=e.formatter,c=e.inactiveColor,u={x:0,y:0,width:pa,height:pa},l={display:"horizontal"===i?"inline-block":"block",marginRight:10},s={display:"inline-block",verticalAlign:"middle",marginRight:4};return r.map(function(e,r){var i=e.formatter||a,f=St(sa(sa({"recharts-legend-item":!0},"legend-item-".concat(r),!0),"inactive",e.inactive));if("none"===e.type)return null;var p=re(e.value)?null:e.value;vo(!re(e.value),'The name property is also required when using a function for the dataKey of a chart\'s cartesian components. Ex: <Bar name="Name of my Data"/>');var h=e.inactive?c:e.color;return n.createElement("li",na({className:f,style:l,key:"legend-item-".concat(r)},Fn(t.props,e,r)),n.createElement(so,{width:o,height:o,viewBox:u,style:s},t.renderIcon(e)),n.createElement("span",{className:"recharts-legend-item-text",style:{color:h}},i?i(p,e,r):p))})}},{key:"render",value:function(){var t=this.props,e=t.payload,r=t.layout,o=t.align;if(!e||!e.length)return null;var i={padding:0,margin:0,textAlign:"horizontal"===r?o:"left"};return n.createElement("ul",{className:"recharts-default-legend",style:i},this.renderItems())}}])}();sa(ha,"displayName","Legend"),sa(ha,"defaultProps",{iconSize:14,layout:"horizontal",align:"center",verticalAlign:"middle",inactiveColor:"#ccc"});var ya=tr;var da=tr,va=er,ma=mr;var ba=tr,ga=function(){this.__data__=new ya,this.size=0},wa=function(t){var e=this.__data__,r=e.delete(t);return this.size=e.size,r},xa=function(t){return this.__data__.get(t)},Oa=function(t){return this.__data__.has(t)},ja=function(t,e){var r=this.__data__;if(r instanceof da){var n=r.__data__;if(!va||n.length<199)return n.push([t,e]),this.size=++r.size,this;r=this.__data__=new ma(n)}return r.set(t,e),this.size=r.size,this};function Sa(t){var e=this.__data__=new ba(t);this.size=e.size}Sa.prototype.clear=ga,Sa.prototype.delete=wa,Sa.prototype.get=xa,Sa.prototype.has=Oa,Sa.prototype.set=ja;var ka=Sa;var Pa=mr,Aa=function(t){return this.__data__.set(t,"__lodash_hash_undefined__"),this},Ea=function(t){return this.__data__.has(t)};function Ma(t){var e=-1,r=null==t?0:t.length;for(this.__data__=new Pa;++e<r;)this.add(t[e])}Ma.prototype.add=Ma.prototype.push=Aa,Ma.prototype.has=Ea;var _a=Ma;var Ta=function(t,e){for(var r=-1,n=null==t?0:t.length;++r<n;)if(e(t[r],r,t))return!0;return!1};var Ca=function(t,e){return t.has(e)},Da=_a,Ia=Ta,Na=Ca;var Ba=function(t,e,r,n,o,i){var a=1&r,c=t.length,u=e.length;if(c!=u&&!(a&&u>c))return!1;var l=i.get(t),s=i.get(e);if(l&&s)return l==e&&s==t;var f=-1,p=!0,h=2&r?new Da:void 0;for(i.set(t,e),i.set(e,t);++f<c;){var y=t[f],d=e[f];if(n)var v=a?n(d,y,f,e,t,i):n(y,d,f,t,e,i);if(void 0!==v){if(v)continue;p=!1;break}if(h){if(!Ia(e,function(t,e){if(!Na(h,e)&&(y===t||o(y,t,r,n,i)))return h.push(e)})){p=!1;break}}else if(y!==d&&!o(y,d,r,n,i)){p=!1;break}}return i.delete(t),i.delete(e),p};var La=function(t){var e=-1,r=Array(t.size);return t.forEach(function(t){r[++e]=t}),r},Ra=Mt.Uint8Array,za=Ue,Ua=Ba,$a=function(t){var e=-1,r=Array(t.size);return t.forEach(function(t,n){r[++e]=[n,t]}),r},qa=La,Fa=_t?_t.prototype:void 0,Wa=Fa?Fa.valueOf:void 0;var Ha=function(t,e,r,n,o,i,a){switch(r){case"[object DataView]":if(t.byteLength!=e.byteLength||t.byteOffset!=e.byteOffset)return!1;t=t.buffer,e=e.buffer;case"[object ArrayBuffer]":return!(t.byteLength!=e.byteLength||!i(new Ra(t),new Ra(e)));case"[object Boolean]":case"[object Date]":case"[object Number]":return za(+t,+e);case"[object Error]":return t.name==e.name&&t.message==e.message;case"[object RegExp]":case"[object String]":return t==e+"";case"[object Map]":var c=$a;case"[object Set]":var u=1&n;if(c||(c=qa),t.size!=e.size&&!u)return!1;var l=a.get(t);if(l)return l==e;n|=2,a.set(t,e);var s=Ua(c(t),c(e),n,o,i,a);return a.delete(t),s;case"[object Symbol]":if(Wa)return Wa.call(t)==Wa.call(e)}return!1};var Va=function(t,e){for(var r=-1,n=e.length,o=t.length;++r<n;)t[o+r]=e[r];return t},Xa=Va,Ga=kt;var Ya=function(t,e,r){var n=e(t);return Ga(t)?n:Xa(n,r(t))};var Ka=function(t,e){for(var r=-1,n=null==t?0:t.length,o=0,i=[];++r<n;){var a=t[r];e(a,r,t)&&(i[o++]=a)}return i},Za=function(){return[]},Ja=Object.prototype.propertyIsEnumerable,Qa=Object.getOwnPropertySymbols,tc=Qa?function(t){return null==t?[]:(t=Object(t),Ka(Qa(t),function(e){return Ja.call(t,e)}))}:Za;var ec=function(t,e){for(var r=-1,n=Array(t);++r<t;)n[r]=e(r);return n},rc=$t,nc=qt;var oc,ic,ac,cc,uc,lc,sc,fc,pc=function(t){return nc(t)&&"[object Arguments]"==rc(t)},hc=qt,yc=Object.prototype,dc=yc.hasOwnProperty,vc=yc.propertyIsEnumerable,mc=pc(function(){return arguments}())?pc:function(t){return hc(t)&&dc.call(t,"callee")&&!vc.call(t,"callee")},bc={exports:{}};oc=bc,ac=Mt,cc=function(){return!1},uc=(ic=bc.exports)&&!ic.nodeType&&ic,lc=uc&&oc&&!oc.nodeType&&oc,sc=lc&&lc.exports===uc?ac.Buffer:void 0,fc=(sc?sc.isBuffer:void 0)||cc,oc.exports=fc;var gc=bc.exports,wc=/^(?:0|[1-9]\d*)$/;var xc=function(t,e){var r=typeof t;return!!(e=null==e?9007199254740991:e)&&("number"==r||"symbol"!=r&&wc.test(t))&&t>-1&&t%1==0&&t<e};var Oc=function(t){return"number"==typeof t&&t>-1&&t%1==0&&t<=9007199254740991},jc=$t,Sc=Oc,kc=qt,Pc={};Pc["[object Float32Array]"]=Pc["[object Float64Array]"]=Pc["[object Int8Array]"]=Pc["[object Int16Array]"]=Pc["[object Int32Array]"]=Pc["[object Uint8Array]"]=Pc["[object Uint8ClampedArray]"]=Pc["[object Uint16Array]"]=Pc["[object Uint32Array]"]=!0,Pc["[object Arguments]"]=Pc["[object Array]"]=Pc["[object ArrayBuffer]"]=Pc["[object Boolean]"]=Pc["[object DataView]"]=Pc["[object Date]"]=Pc["[object Error]"]=Pc["[object Function]"]=Pc["[object Map]"]=Pc["[object Number]"]=Pc["[object Object]"]=Pc["[object RegExp]"]=Pc["[object Set]"]=Pc["[object String]"]=Pc["[object WeakMap]"]=!1;var Ac=function(t){return kc(t)&&Sc(t.length)&&!!Pc[jc(t)]};var Ec=function(t){return function(e){return t(e)}},Mc={exports:{}};!function(t,e){var r=Pt,n=e&&!e.nodeType&&e,o=n&&t&&!t.nodeType&&t,i=o&&o.exports===n&&r.process,a=function(){try{var t=o&&o.require&&o.require("util").types;return t||i&&i.binding&&i.binding("util")}catch(e){}}();t.exports=a}(Mc,Mc.exports);var _c=Mc.exports,Tc=Ac,Cc=Ec,Dc=_c&&_c.isTypedArray,Ic=Dc?Cc(Dc):Tc,Nc=ec,Bc=mc,Lc=kt,Rc=gc,zc=xc,Uc=Ic,$c=Object.prototype.hasOwnProperty;var qc=function(t,e){var r=Lc(t),n=!r&&Bc(t),o=!r&&!n&&Rc(t),i=!r&&!n&&!o&&Uc(t),a=r||n||o||i,c=a?Nc(t.length,String):[],u=c.length;for(var l in t)!e&&!$c.call(t,l)||a&&("length"==l||o&&("offset"==l||"parent"==l)||i&&("buffer"==l||"byteLength"==l||"byteOffset"==l)||zc(l,u))||c.push(l);return c},Fc=Object.prototype;var Wc=function(t){var e=t&&t.constructor;return t===("function"==typeof e&&e.prototype||Fc)};var Hc=function(t,e){return function(r){return t(e(r))}},Vc=Hc(Object.keys,Object),Xc=Wc,Gc=Vc,Yc=Object.prototype.hasOwnProperty;var Kc=ee,Zc=Oc;var Jc=function(t){return null!=t&&Zc(t.length)&&!Kc(t)},Qc=qc,tu=function(t){if(!Xc(t))return Gc(t);var e=[];for(var r in Object(t))Yc.call(t,r)&&"constructor"!=r&&e.push(r);return e},eu=Jc;var ru=function(t){return eu(t)?Qc(t):tu(t)},nu=Ya,ou=tc,iu=ru;var au=function(t){return nu(t,iu,ou)},cu=Object.prototype.hasOwnProperty;var uu=function(t,e,r,n,o,i){var a=1&r,c=au(t),u=c.length;if(u!=au(e).length&&!a)return!1;for(var l=u;l--;){var s=c[l];if(!(a?s in e:cu.call(e,s)))return!1}var f=i.get(t),p=i.get(e);if(f&&p)return f==e&&p==t;var h=!0;i.set(t,e),i.set(e,t);for(var y=a;++l<u;){var d=t[s=c[l]],v=e[s];if(n)var m=a?n(v,d,s,e,t,i):n(d,v,s,t,e,i);if(!(void 0===m?d===v||o(d,v,r,n,i):m)){h=!1;break}y||(y="constructor"==s)}if(h&&!y){var b=t.constructor,g=e.constructor;b==g||!("constructor"in t)||!("constructor"in e)||"function"==typeof b&&b instanceof b&&"function"==typeof g&&g instanceof g||(h=!1)}return i.delete(t),i.delete(e),h},lu=xe(Mt,"DataView"),su=xe(Mt,"Promise"),fu=xe(Mt,"Set"),pu=lu,hu=er,yu=su,du=fu,vu=xe(Mt,"WeakMap"),mu=$t,bu=ue,gu="[object Map]",wu="[object Promise]",xu="[object Set]",Ou="[object WeakMap]",ju="[object DataView]",Su=bu(pu),ku=bu(hu),Pu=bu(yu),Au=bu(du),Eu=bu(vu),Mu=mu;(pu&&Mu(new pu(new ArrayBuffer(1)))!=ju||hu&&Mu(new hu)!=gu||yu&&Mu(yu.resolve())!=wu||du&&Mu(new du)!=xu||vu&&Mu(new vu)!=Ou)&&(Mu=function(t){var e=mu(t),r="[object Object]"==e?t.constructor:void 0,n=r?bu(r):"";if(n)switch(n){case Su:return ju;case ku:return gu;case Pu:return wu;case Au:return xu;case Eu:return Ou}return e});var _u=ka,Tu=Ba,Cu=Ha,Du=uu,Iu=Mu,Nu=kt,Bu=gc,Lu=Ic,Ru="[object Arguments]",zu="[object Array]",Uu="[object Object]",$u=Object.prototype.hasOwnProperty;var qu=function(t,e,r,n,o,i){var a=Nu(t),c=Nu(e),u=a?zu:Iu(t),l=c?zu:Iu(e),s=(u=u==Ru?Uu:u)==Uu,f=(l=l==Ru?Uu:l)==Uu,p=u==l;if(p&&Bu(t)){if(!Bu(e))return!1;a=!0,s=!1}if(p&&!s)return i||(i=new _u),a||Lu(t)?Tu(t,e,r,n,o,i):Cu(t,e,u,r,n,o,i);if(!(1&r)){var h=s&&$u.call(t,"__wrapped__"),y=f&&$u.call(e,"__wrapped__");if(h||y){var d=h?t.value():t,v=y?e.value():e;return i||(i=new _u),o(d,v,r,n,i)}}return!!p&&(i||(i=new _u),Du(t,e,r,n,o,i))},Fu=qt;var Wu=function t(e,r,n,o,i){return e===r||(null==e||null==r||!Fu(e)&&!Fu(r)?e!=e&&r!=r:qu(e,r,n,o,t,i))},Hu=ka,Vu=Wu;var Xu=Zt;var Gu=function(t){return t==t&&!Xu(t)},Yu=Gu,Ku=ru;var Zu=function(t,e){return function(r){return null!=r&&(r[t]===e&&(void 0!==e||t in Object(r)))}},Ju=function(t,e,r,n){var o=r.length,i=o,a=!n;if(null==t)return!i;for(t=Object(t);o--;){var c=r[o];if(a&&c[2]?c[1]!==t[c[0]]:!(c[0]in t))return!1}for(;++o<i;){var u=(c=r[o])[0],l=t[u],s=c[1];if(a&&c[2]){if(void 0===l&&!(u in t))return!1}else{var f=new Hu;if(n)var p=n(l,s,u,t,e,f);if(!(void 0===p?Vu(s,l,3,n,f):p))return!1}}return!0},Qu=function(t){for(var e=Ku(t),r=e.length;r--;){var n=e[r],o=t[n];e[r]=[n,o,Yu(o)]}return e},tl=Zu;var el=zr,rl=mc,nl=kt,ol=xc,il=Oc,al=$r;var cl=function(t,e){return null!=t&&e in Object(t)},ul=function(t,e,r){for(var n=-1,o=(e=el(e,t)).length,i=!1;++n<o;){var a=al(e[n]);if(!(i=null!=t&&r(t,a)))break;t=t[a]}return i||++n!=o?i:!!(o=null==t?0:t.length)&&il(o)&&ol(a,o)&&(nl(t)||rl(t))};var ll=Wu,sl=Vr,fl=function(t,e){return null!=t&&ul(t,e,cl)},pl=Kt,hl=Gu,yl=Zu,dl=$r;var vl=function(t){return t};var ml=Wr;var bl=function(t){return function(e){return null==e?void 0:e[t]}},gl=function(t){return function(e){return ml(e,t)}},wl=Kt,xl=$r;var Ol=function(t){var e=Qu(t);return 1==e.length&&e[0][2]?tl(e[0][0],e[0][1]):function(r){return r===t||Ju(r,t,e)}},jl=function(t,e){return pl(t)&&hl(e)?yl(dl(t),e):function(r){var n=sl(r,t);return void 0===n&&n===e?fl(r,t):ll(e,n,3)}},Sl=vl,kl=kt,Pl=function(t){return wl(t)?bl(xl(t)):gl(t)};var Al=function(t){return"function"==typeof t?t:null==t?Sl:"object"==typeof t?kl(t)?jl(t[0],t[1]):Ol(t):Pl(t)};var El=function(t,e,r,n){for(var o=t.length,i=r+(n?1:-1);n?i--:++i<o;)if(e(t[i],i,t))return i;return-1};var Ml=El,_l=function(t){return t!=t},Tl=function(t,e,r){for(var n=r-1,o=t.length;++n<o;)if(t[n]===e)return n;return-1};var Cl=function(t,e,r){return e==e?Tl(t,e,r):Ml(t,_l,r)};var Dl=function(t,e){return!!(null==t?0:t.length)&&Cl(t,e,0)>-1};var Il=function(t,e,r){for(var n=-1,o=null==t?0:t.length;++n<o;)if(r(e,t[n]))return!0;return!1};var Nl=fu,Bl=function(){},Ll=Nl&&1/La(new Nl([,-0]))[1]==1/0?function(t){return new Nl(t)}:Bl,Rl=_a,zl=Dl,Ul=Il,$l=Ca,ql=Ll,Fl=La;var Wl=Al,Hl=function(t,e,r){var n=-1,o=zl,i=t.length,a=!0,c=[],u=c;if(r)a=!1,o=Ul;else if(i>=200){var l=e?null:ql(t);if(l)return Fl(l);a=!1,o=$l,u=new Rl}else u=e?[]:c;t:for(;++n<i;){var s=t[n],f=e?e(s):s;if(s=r||0!==s?s:0,a&&f==f){for(var p=u.length;p--;)if(u[p]===f)continue t;e&&u.push(f),c.push(s)}else o(u,f,r)||(u!==c&&u.push(f),c.push(s))}return c};const Vl=r(function(t,e){return t&&t.length?Hl(t,Wl(e)):[]});function Xl(t,e,r){return!0===e?Vl(t,r):re(e)?Vl(t,e):t}function Gl(t){return(Gl="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}var Yl=["ref"];function Kl(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function Zl(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?Kl(Object(r),!0).forEach(function(e){ns(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):Kl(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function Jl(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,os(n.key),n)}}function Ql(t,e,r){return e=es(e),function(t,e){if(e&&("object"===Gl(e)||"function"==typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(t,ts()?Reflect.construct(e,r||[],es(t).constructor):e.apply(t,r))}function ts(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(e){}return(ts=function(){return!!t})()}function es(t){return(es=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function rs(t,e){return(rs=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function ns(t,e,r){return(e=os(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function os(t){var e=function(t,e){if("object"!=Gl(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=Gl(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==Gl(e)?e:e+""}function is(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],e.indexOf(r)>=0||Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}function as(t){return t.value}var cs=function(){function e(){var t;!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,e);for(var r=arguments.length,n=new Array(r),o=0;o<r;o++)n[o]=arguments[o];return ns(t=Ql(this,e,[].concat(n)),"lastBoundingBox",{width:-1,height:-1}),t}return function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&rs(t,e)}(e,t.PureComponent),r=e,i=[{key:"getWithHeight",value:function(t,e){var r=Zl(Zl({},this.defaultProps),t.props).layout;return"vertical"===r&&An(t.props.height)?{height:t.props.height}:"horizontal"===r?{width:t.props.width||e}:null}}],(o=[{key:"componentDidMount",value:function(){this.updateBBox()}},{key:"componentDidUpdate",value:function(){this.updateBBox()}},{key:"getBBox",value:function(){if(this.wrapperNode&&this.wrapperNode.getBoundingClientRect){var t=this.wrapperNode.getBoundingClientRect();return t.height=this.wrapperNode.offsetHeight,t.width=this.wrapperNode.offsetWidth,t}return null}},{key:"updateBBox",value:function(){var t=this.props.onBBoxUpdate,e=this.getBBox();e?(Math.abs(e.width-this.lastBoundingBox.width)>1||Math.abs(e.height-this.lastBoundingBox.height)>1)&&(this.lastBoundingBox.width=e.width,this.lastBoundingBox.height=e.height,t&&t(e)):-1===this.lastBoundingBox.width&&-1===this.lastBoundingBox.height||(this.lastBoundingBox.width=-1,this.lastBoundingBox.height=-1,t&&t(null))}},{key:"getBBoxSnapshot",value:function(){return this.lastBoundingBox.width>=0&&this.lastBoundingBox.height>=0?Zl({},this.lastBoundingBox):{width:0,height:0}}},{key:"getDefaultPosition",value:function(t){var e,r,n=this.props,o=n.layout,i=n.align,a=n.verticalAlign,c=n.margin,u=n.chartWidth,l=n.chartHeight;return t&&(void 0!==t.left&&null!==t.left||void 0!==t.right&&null!==t.right)||(e="center"===i&&"vertical"===o?{left:((u||0)-this.getBBoxSnapshot().width)/2}:"right"===i?{right:c&&c.right||0}:{left:c&&c.left||0}),t&&(void 0!==t.top&&null!==t.top||void 0!==t.bottom&&null!==t.bottom)||(r="middle"===a?{top:((l||0)-this.getBBoxSnapshot().height)/2}:"bottom"===a?{bottom:c&&c.bottom||0}:{top:c&&c.top||0}),Zl(Zl({},e),r)}},{key:"render",value:function(){var t=this,e=this.props,r=e.content,o=e.width,i=e.height,a=e.wrapperStyle,c=e.payloadUniqBy,u=e.payload,l=Zl(Zl({position:"absolute",width:o||"auto",height:i||"auto"},this.getDefaultPosition(a)),a);return n.createElement("div",{className:"recharts-legend-wrapper",style:l,ref:function(e){t.wrapperNode=e}},function(t,e){if(n.isValidElement(t))return n.cloneElement(t,e);if("function"==typeof t)return n.createElement(t,e);e.ref;var r=is(e,Yl);return n.createElement(ha,r)}(r,Zl(Zl({},this.props),{},{payload:Xl(u,c,as)})))}}])&&Jl(r.prototype,o),i&&Jl(r,i),Object.defineProperty(r,"prototype",{writable:!1}),r;var r,o,i}();ns(cs,"displayName","Legend"),ns(cs,"defaultProps",{iconSize:14,layout:"horizontal",align:"center",verticalAlign:"bottom"});var us=mc,ls=kt,ss=_t?_t.isConcatSpreadable:void 0;var fs=Va,ps=function(t){return ls(t)||us(t)||!!(ss&&t&&t[ss])};var hs=function t(e,r,n,o,i){var a=-1,c=e.length;for(n||(n=ps),i||(i=[]);++a<c;){var u=e[a];r>0&&n(u)?r>1?t(u,r-1,n,o,i):fs(i,u):o||(i[i.length]=u)}return i};var ys=function(t){return function(e,r,n){for(var o=-1,i=Object(e),a=n(e),c=a.length;c--;){var u=a[t?c:++o];if(!1===r(i[u],u,i))break}return e}}(),ds=ru;var vs=function(t,e){return t&&ys(t,e,ds)},ms=Jc;var bs=function(t,e){return function(r,n){if(null==r)return r;if(!ms(r))return t(r,n);for(var o=r.length,i=e?o:-1,a=Object(r);(e?i--:++i<o)&&!1!==n(a[i],i,a););return r}}(vs),gs=bs,ws=Jc;var xs=function(t,e){var r=-1,n=ws(t)?Array(t.length):[];return gs(t,function(t,o,i){n[++r]=e(t,o,i)}),n};var Os=Ht;var js=function(t,e){if(t!==e){var r=void 0!==t,n=null===t,o=t==t,i=Os(t),a=void 0!==e,c=null===e,u=e==e,l=Os(e);if(!c&&!l&&!i&&t>e||i&&a&&u&&!c&&!l||n&&a&&u||!r&&u||!o)return 1;if(!n&&!i&&!l&&t<e||l&&r&&o&&!n&&!i||c&&r&&o||!a&&o||!u)return-1}return 0};var Ss=Pr,ks=Wr,Ps=Al,As=xs,Es=function(t,e){var r=t.length;for(t.sort(e);r--;)t[r]=t[r].value;return t},Ms=Ec,_s=function(t,e,r){for(var n=-1,o=t.criteria,i=e.criteria,a=o.length,c=r.length;++n<a;){var u=js(o[n],i[n]);if(u)return n>=c?u:u*("desc"==r[n]?-1:1)}return t.index-e.index},Ts=vl,Cs=kt;var Ds=function(t,e,r){e=e.length?Ss(e,function(t){return Cs(t)?function(e){return ks(e,1===t.length?t[0]:t)}:t}):[Ts];var n=-1;e=Ss(e,Ms(Ps));var o=As(t,function(t,r,o){return{criteria:Ss(e,function(e){return e(t)}),index:++n,value:t}});return Es(o,function(t,e){return _s(t,e,r)})};var Is=function(t,e,r){switch(r.length){case 0:return t.call(e);case 1:return t.call(e,r[0]);case 2:return t.call(e,r[0],r[1]);case 3:return t.call(e,r[0],r[1],r[2])}return t.apply(e,r)},Ns=Math.max;var Bs=function(t,e,r){return e=Ns(void 0===e?t.length-1:e,0),function(){for(var n=arguments,o=-1,i=Ns(n.length-e,0),a=Array(i);++o<i;)a[o]=n[e+o];o=-1;for(var c=Array(e+1);++o<e;)c[o]=n[o];return c[e]=r(a),Is(t,this,c)}};var Ls=function(t){return function(){return t}},Rs=xe,zs=function(){try{var t=Rs(Object,"defineProperty");return t({},"",{}),t}catch(e){}}(),Us=Ls,$s=zs,qs=$s?function(t,e){return $s(t,"toString",{configurable:!0,enumerable:!1,value:Us(e),writable:!0})}:vl,Fs=Date.now;var Ws=function(t){var e=0,r=0;return function(){var n=Fs(),o=16-(n-r);if(r=n,o>0){if(++e>=800)return arguments[0]}else e=0;return t.apply(void 0,arguments)}}(qs),Hs=vl,Vs=Bs,Xs=Ws;var Gs=Ue,Ys=Jc,Ks=xc,Zs=Zt;var Js=function(t,e,r){if(!Zs(r))return!1;var n=typeof e;return!!("number"==n?Ys(r)&&Ks(e,r.length):"string"==n&&e in r)&&Gs(r[e],t)},Qs=hs,tf=Ds,ef=Js;const rf=r(function(t,e){return Xs(Vs(t,e,Hs),t+"")}(function(t,e){if(null==t)return[];var r=e.length;return r>1&&ef(t,e[0],e[1])?e=[]:r>2&&ef(e[0],e[1],e[2])&&(e=[e[0]]),tf(t,Qs(e,1),[])}));function nf(t){return(nf="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function of(){return of=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},of.apply(this,arguments)}function af(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,c=[],u=!0,l=!1;try{if(i=(r=r.call(t)).next,0===e);else for(;!(u=(n=i.call(r)).done)&&(c.push(n.value),c.length!==e);u=!0);}catch(s){l=!0,o=s}finally{try{if(!u&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(l)throw o}}return c}}(t,e)||function(t,e){if(!t)return;if("string"==typeof t)return cf(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);"Object"===r&&t.constructor&&(r=t.constructor.name);if("Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return cf(t,e)}(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function cf(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}function uf(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function lf(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?uf(Object(r),!0).forEach(function(e){sf(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):uf(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function sf(t,e,r){var n;return n=function(t,e){if("object"!=nf(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=nf(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(e,"string"),(e="symbol"==nf(n)?n:n+"")in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function ff(t){return Array.isArray(t)&&En(t[0])&&En(t[1])?t.join(" ~ "):t}var pf=function(t){var e=t.separator,r=void 0===e?" : ":e,o=t.contentStyle,i=void 0===o?{}:o,a=t.itemStyle,c=void 0===a?{}:a,u=t.labelStyle,l=void 0===u?{}:u,s=t.payload,f=t.formatter,p=t.itemSorter,h=t.wrapperClassName,y=t.labelClassName,d=t.label,v=t.labelFormatter,m=t.accessibilityLayer,b=void 0!==m&&m,g=lf({margin:0,padding:10,backgroundColor:"#fff",border:"1px solid #ccc",whiteSpace:"nowrap"},i),w=lf({margin:0},l),x=!Gr(d),O=x?d:"",j=St("recharts-default-tooltip",h),S=St("recharts-tooltip-label",y);x&&v&&null!=s&&(O=v(d,s));var k=b?{role:"status","aria-live":"assertive"}:{};return n.createElement("div",of({className:j,style:g},k),n.createElement("p",{className:S,style:w},n.isValidElement(O)?O:"".concat(O)),function(){if(s&&s.length){var t=(p?rf(s,p):s).map(function(t,e){if("none"===t.type)return null;var o=lf({display:"block",paddingTop:4,paddingBottom:4,color:t.color||"#000"},c),i=t.formatter||f||ff,a=t.value,u=t.name,l=a,p=u;if(i&&null!=l&&null!=p){var h=i(a,u,t,e,s);if(Array.isArray(h)){var y=af(h,2);l=y[0],p=y[1]}else l=h}return n.createElement("li",{className:"recharts-tooltip-item",key:"tooltip-item-".concat(e),style:o},En(p)?n.createElement("span",{className:"recharts-tooltip-item-name"},p):null,En(p)?n.createElement("span",{className:"recharts-tooltip-item-separator"},r):null,n.createElement("span",{className:"recharts-tooltip-item-value"},l),n.createElement("span",{className:"recharts-tooltip-item-unit"},t.unit||""))});return n.createElement("ul",{className:"recharts-tooltip-item-list",style:{padding:0,margin:0}},t)}return null}())};function hf(t){return(hf="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function yf(t,e,r){var n;return n=function(t,e){if("object"!=hf(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=hf(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(e,"string"),(e="symbol"==hf(n)?n:n+"")in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}var df="recharts-tooltip-wrapper",vf={visibility:"hidden"};function mf(t){var e=t.coordinate,r=t.translateX,n=t.translateY;return St(df,yf(yf(yf(yf({},"".concat(df,"-right"),An(r)&&e&&An(e.x)&&r>=e.x),"".concat(df,"-left"),An(r)&&e&&An(e.x)&&r<e.x),"".concat(df,"-bottom"),An(n)&&e&&An(e.y)&&n>=e.y),"".concat(df,"-top"),An(n)&&e&&An(e.y)&&n<e.y))}function bf(t){var e=t.allowEscapeViewBox,r=t.coordinate,n=t.key,o=t.offsetTopLeft,i=t.position,a=t.reverseDirection,c=t.tooltipDimension,u=t.viewBox,l=t.viewBoxDimension;if(i&&An(i[n]))return i[n];var s=r[n]-c-o,f=r[n]+o;return e[n]?a[n]?s:f:a[n]?s<u[n]?Math.max(f,u[n]):Math.max(s,u[n]):f+c>u[n]+l?Math.max(s,u[n]):Math.max(f,u[n])}function gf(t){return(gf="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function wf(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function xf(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?wf(Object(r),!0).forEach(function(e){Af(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):wf(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function Of(t,e,r){return e&&function(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,Ef(n.key),n)}}(t.prototype,e),Object.defineProperty(t,"prototype",{writable:!1}),t}function jf(t,e,r){return e=kf(e),function(t,e){if(e&&("object"===gf(e)||"function"==typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(t,Sf()?Reflect.construct(e,r||[],kf(t).constructor):e.apply(t,r))}function Sf(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(e){}return(Sf=function(){return!!t})()}function kf(t){return(kf=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function Pf(t,e){return(Pf=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function Af(t,e,r){return(e=Ef(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function Ef(t){var e=function(t,e){if("object"!=gf(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=gf(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==gf(e)?e:e+""}var Mf=function(){function e(){var t;!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,e);for(var r=arguments.length,n=new Array(r),o=0;o<r;o++)n[o]=arguments[o];return Af(t=jf(this,e,[].concat(n)),"state",{dismissed:!1,dismissedAtCoordinate:{x:0,y:0},lastBoundingBox:{width:-1,height:-1}}),Af(t,"handleKeyDown",function(e){var r,n,o,i;"Escape"===e.key&&t.setState({dismissed:!0,dismissedAtCoordinate:{x:null!==(r=null===(n=t.props.coordinate)||void 0===n?void 0:n.x)&&void 0!==r?r:0,y:null!==(o=null===(i=t.props.coordinate)||void 0===i?void 0:i.y)&&void 0!==o?o:0}})}),t}return function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&Pf(t,e)}(e,t.PureComponent),Of(e,[{key:"updateBBox",value:function(){if(this.wrapperNode&&this.wrapperNode.getBoundingClientRect){var t=this.wrapperNode.getBoundingClientRect();(Math.abs(t.width-this.state.lastBoundingBox.width)>1||Math.abs(t.height-this.state.lastBoundingBox.height)>1)&&this.setState({lastBoundingBox:{width:t.width,height:t.height}})}else-1===this.state.lastBoundingBox.width&&-1===this.state.lastBoundingBox.height||this.setState({lastBoundingBox:{width:-1,height:-1}})}},{key:"componentDidMount",value:function(){document.addEventListener("keydown",this.handleKeyDown),this.updateBBox()}},{key:"componentWillUnmount",value:function(){document.removeEventListener("keydown",this.handleKeyDown)}},{key:"componentDidUpdate",value:function(){var t,e;this.props.active&&this.updateBBox(),this.state.dismissed&&((null===(t=this.props.coordinate)||void 0===t?void 0:t.x)===this.state.dismissedAtCoordinate.x&&(null===(e=this.props.coordinate)||void 0===e?void 0:e.y)===this.state.dismissedAtCoordinate.y||(this.state.dismissed=!1))}},{key:"render",value:function(){var t=this,e=this.props,r=e.active,o=e.allowEscapeViewBox,i=e.animationDuration,a=e.animationEasing,c=e.children,u=e.coordinate,l=e.hasPayload,s=e.isAnimationActive,f=e.offset,p=e.position,h=e.reverseDirection,y=e.useTranslate3d,d=e.viewBox,v=e.wrapperStyle,m=function(t){var e,r,n=t.allowEscapeViewBox,o=t.coordinate,i=t.offsetTopLeft,a=t.position,c=t.reverseDirection,u=t.tooltipBox,l=t.useTranslate3d,s=t.viewBox;return{cssProperties:u.height>0&&u.width>0&&o?function(t){var e=t.translateX,r=t.translateY;return{transform:t.useTranslate3d?"translate3d(".concat(e,"px, ").concat(r,"px, 0)"):"translate(".concat(e,"px, ").concat(r,"px)")}}({translateX:e=bf({allowEscapeViewBox:n,coordinate:o,key:"x",offsetTopLeft:i,position:a,reverseDirection:c,tooltipDimension:u.width,viewBox:s,viewBoxDimension:s.width}),translateY:r=bf({allowEscapeViewBox:n,coordinate:o,key:"y",offsetTopLeft:i,position:a,reverseDirection:c,tooltipDimension:u.height,viewBox:s,viewBoxDimension:s.height}),useTranslate3d:l}):vf,cssClasses:mf({translateX:e,translateY:r,coordinate:o})}}({allowEscapeViewBox:o,coordinate:u,offsetTopLeft:f,position:p,reverseDirection:h,tooltipBox:this.state.lastBoundingBox,useTranslate3d:y,viewBox:d}),b=m.cssClasses,g=m.cssProperties,w=xf(xf({transition:s&&r?"transform ".concat(i,"ms ").concat(a):void 0},g),{},{pointerEvents:"none",visibility:!this.state.dismissed&&r&&l?"visible":"hidden",position:"absolute",top:0,left:0},v);return n.createElement("div",{tabIndex:-1,className:b,style:w,ref:function(e){t.wrapperNode=e}},c)}}])}(),_f={isSsr:!("undefined"!=typeof window&&window.document&&window.document.createElement&&window.setTimeout),get:function(t){return _f[t]},set:function(t,e){if("string"==typeof t)_f[t]=e;else{var r=Object.keys(t);r&&r.length&&r.forEach(function(e){_f[e]=t[e]})}}};function Tf(t){return(Tf="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function Cf(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function Df(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?Cf(Object(r),!0).forEach(function(e){zf(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):Cf(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function If(t,e,r){return e&&function(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,Uf(n.key),n)}}(t.prototype,e),Object.defineProperty(t,"prototype",{writable:!1}),t}function Nf(t,e,r){return e=Lf(e),function(t,e){if(e&&("object"===Tf(e)||"function"==typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(t,Bf()?Reflect.construct(e,r||[],Lf(t).constructor):e.apply(t,r))}function Bf(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(e){}return(Bf=function(){return!!t})()}function Lf(t){return(Lf=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function Rf(t,e){return(Rf=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function zf(t,e,r){return(e=Uf(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function Uf(t){var e=function(t,e){if("object"!=Tf(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=Tf(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==Tf(e)?e:e+""}function $f(t){return t.dataKey}var qf=function(){function e(){return function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,e),Nf(this,e,arguments)}return function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&Rf(t,e)}(e,t.PureComponent),If(e,[{key:"render",value:function(){var t=this,e=this.props,r=e.active,o=e.allowEscapeViewBox,i=e.animationDuration,a=e.animationEasing,c=e.content,u=e.coordinate,l=e.filterNull,s=e.isAnimationActive,f=e.offset,p=e.payload,h=e.payloadUniqBy,y=e.position,d=e.reverseDirection,v=e.useTranslate3d,m=e.viewBox,b=e.wrapperStyle,g=null!=p?p:[];l&&g.length&&(g=Xl(p.filter(function(e){return null!=e.value&&(!0!==e.hide||t.props.includeHidden)}),h,$f));var w=g.length>0;return n.createElement(Mf,{allowEscapeViewBox:o,animationDuration:i,animationEasing:a,isAnimationActive:s,active:r,coordinate:u,hasPayload:w,offset:f,position:y,reverseDirection:d,useTranslate3d:v,viewBox:m,wrapperStyle:b},function(t,e){return n.isValidElement(t)?n.cloneElement(t,e):"function"==typeof t?n.createElement(t,e):n.createElement(pf,e)}(c,Df(Df({},this.props),{},{payload:g})))}}])}();zf(qf,"displayName","Tooltip"),zf(qf,"defaultProps",{accessibilityLayer:!1,allowEscapeViewBox:{x:!1,y:!1},animationDuration:400,animationEasing:"ease",contentStyle:{},coordinate:{x:0,y:0},cursor:!0,cursorStyle:{},filterNull:!0,isAnimationActive:!_f.isSsr,itemStyle:{},labelStyle:{},offset:10,reverseDirection:{x:!1,y:!1},separator:" : ",trigger:"hover",useTranslate3d:!1,viewBox:{x:0,y:0,height:0,width:0},wrapperStyle:{}});var Ff=Mt,Wf=/\s/;var Hf=function(t){for(var e=t.length;e--&&Wf.test(t.charAt(e)););return e},Vf=/^\s+/;var Xf=function(t){return t?t.slice(0,Hf(t)+1).replace(Vf,""):t},Gf=Zt,Yf=Ht,Kf=/^[-+]0x[0-9a-f]+$/i,Zf=/^0b[01]+$/i,Jf=/^0o[0-7]+$/i,Qf=parseInt;var tp=function(t){if("number"==typeof t)return t;if(Yf(t))return NaN;if(Gf(t)){var e="function"==typeof t.valueOf?t.valueOf():t;t=Gf(e)?e+"":e}if("string"!=typeof t)return 0===t?t:+t;t=Xf(t);var r=Zf.test(t);return r||Jf.test(t)?Qf(t.slice(2),r?2:8):Kf.test(t)?NaN:+t},ep=Zt,rp=function(){return Ff.Date.now()},np=tp,op=Math.max,ip=Math.min;var ap=function(t,e,r){var n,o,i,a,c,u,l=0,s=!1,f=!1,p=!0;if("function"!=typeof t)throw new TypeError("Expected a function");function h(e){var r=n,i=o;return n=o=void 0,l=e,a=t.apply(i,r)}function y(t){var r=t-u;return void 0===u||r>=e||r<0||f&&t-l>=i}function d(){var t=rp();if(y(t))return v(t);c=setTimeout(d,function(t){var r=e-(t-u);return f?ip(r,i-(t-l)):r}(t))}function v(t){return c=void 0,p&&n?h(t):(n=o=void 0,a)}function m(){var t=rp(),r=y(t);if(n=arguments,o=this,u=t,r){if(void 0===c)return function(t){return l=t,c=setTimeout(d,e),s?h(t):a}(u);if(f)return clearTimeout(c),c=setTimeout(d,e),h(u)}return void 0===c&&(c=setTimeout(d,e)),a}return e=np(e)||0,ep(r)&&(s=!!r.leading,i=(f="maxWait"in r)?op(np(r.maxWait)||0,e):i,p="trailing"in r?!!r.trailing:p),m.cancel=function(){void 0!==c&&clearTimeout(c),l=0,n=u=o=c=void 0},m.flush=function(){return void 0===c?a:v(rp())},m},cp=Zt;const up=r(function(t,e,r){var n=!0,o=!0;if("function"!=typeof t)throw new TypeError("Expected a function");return cp(r)&&(n="leading"in r?!!r.leading:n,o="trailing"in r?!!r.trailing:o),ap(t,e,{leading:n,maxWait:e,trailing:o})});function lp(t){return(lp="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function sp(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function fp(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?sp(Object(r),!0).forEach(function(e){pp(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):sp(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function pp(t,e,r){var n;return n=function(t,e){if("object"!=lp(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=lp(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(e,"string"),(e="symbol"==lp(n)?n:n+"")in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function hp(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,c=[],u=!0,l=!1;try{if(i=(r=r.call(t)).next,0===e);else for(;!(u=(n=i.call(r)).done)&&(c.push(n.value),c.length!==e);u=!0);}catch(s){l=!0,o=s}finally{try{if(!u&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(l)throw o}}return c}}(t,e)||function(t,e){if(!t)return;if("string"==typeof t)return yp(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);"Object"===r&&t.constructor&&(r=t.constructor.name);if("Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return yp(t,e)}(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function yp(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}var dp=t.forwardRef(function(e,r){var o=e.aspect,i=e.initialDimension,a=void 0===i?{width:-1,height:-1}:i,c=e.width,u=void 0===c?"100%":c,l=e.height,s=void 0===l?"100%":l,f=e.minWidth,p=void 0===f?0:f,h=e.minHeight,y=e.maxHeight,d=e.children,v=e.debounce,m=void 0===v?0:v,b=e.id,g=e.className,w=e.onResize,x=e.style,O=void 0===x?{}:x,j=t.useRef(null),S=t.useRef();S.current=w,t.useImperativeHandle(r,function(){return Object.defineProperty(j.current,"current",{get:function(){return console.warn("The usage of ref.current.current is deprecated and will no longer be supported."),j.current},configurable:!0})});var k=hp(t.useState({containerWidth:a.width,containerHeight:a.height}),2),P=k[0],A=k[1],E=t.useCallback(function(t,e){A(function(r){var n=Math.round(t),o=Math.round(e);return r.containerWidth===n&&r.containerHeight===o?r:{containerWidth:n,containerHeight:o}})},[]);t.useEffect(function(){var t=function(t){var e,r=t[0].contentRect,n=r.width,o=r.height;E(n,o),null===(e=S.current)||void 0===e||e.call(S,n,o)};m>0&&(t=up(t,m,{trailing:!0,leading:!1}));var e=new ResizeObserver(t),r=j.current.getBoundingClientRect(),n=r.width,o=r.height;return E(n,o),e.observe(j.current),function(){e.disconnect()}},[E,m]);var M=t.useMemo(function(){var e=P.containerWidth,r=P.containerHeight;if(e<0||r<0)return null;vo(Pn(u)||Pn(s),"The width(%s) and height(%s) are both fixed numbers,\n       maybe you don't need to use a ResponsiveContainer.",u,s),vo(!o||o>0,"The aspect(%s) must be greater than zero.",o);var i=Pn(u)?e:u,a=Pn(s)?r:s;o&&o>0&&(i?a=i/o:a&&(i=a*o),y&&a>y&&(a=y)),vo(i>0||a>0,"The width(%s) and height(%s) of chart should be greater than 0,\n       please check the style of container, or the props width(%s) and height(%s),\n       or add a minWidth(%s) or minHeight(%s) or use aspect(%s) to control the\n       height and width.",i,a,u,s,p,h,o);var c=!Array.isArray(d)&&Yn(d.type).endsWith("Chart");return n.Children.map(d,function(e){return n.isValidElement(e)?t.cloneElement(e,fp({width:i,height:a},c?{style:fp({height:"100%",width:"100%",maxHeight:a,maxWidth:i},e.props.style)}:{})):e})},[o,d,s,y,h,p,P,u]);return n.createElement("div",{id:b?"".concat(b):void 0,className:St("recharts-responsive-container",g),style:fp(fp({},O),{},{width:u,height:s,minWidth:p,minHeight:h,maxHeight:y}),ref:j},M)}),vp=function(t){return null};function mp(t){return(mp="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function bp(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function gp(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?bp(Object(r),!0).forEach(function(e){wp(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):bp(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function wp(t,e,r){var n;return n=function(t,e){if("object"!=mp(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=mp(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(e,"string"),(e="symbol"==mp(n)?n:n+"")in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}vp.displayName="Cell";var xp={widthCache:{},cacheCount:0},Op={position:"absolute",top:"-20000px",left:0,padding:0,margin:0,border:"none",whiteSpace:"pre"},jp="recharts_measurement_span";var Sp=function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(null==t||_f.isSsr)return{width:0,height:0};var r,n=(r=gp({},e),Object.keys(r).forEach(function(t){r[t]||delete r[t]}),r),o=JSON.stringify({text:t,copyStyle:n});if(xp.widthCache[o])return xp.widthCache[o];try{var i=document.getElementById(jp);i||((i=document.createElement("span")).setAttribute("id",jp),i.setAttribute("aria-hidden","true"),document.body.appendChild(i));var a=gp(gp({},Op),n);Object.assign(i.style,a),i.textContent="".concat(t);var c=i.getBoundingClientRect(),u={width:c.width,height:c.height};return xp.widthCache[o]=u,++xp.cacheCount>2e3&&(xp.cacheCount=0,xp.widthCache={}),u}catch(l){return{width:0,height:0}}};function kp(t){return(kp="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function Pp(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,c=[],u=!0,l=!1;try{if(i=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;u=!1}else for(;!(u=(n=i.call(r)).done)&&(c.push(n.value),c.length!==e);u=!0);}catch(s){l=!0,o=s}finally{try{if(!u&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(l)throw o}}return c}}(t,e)||function(t,e){if(!t)return;if("string"==typeof t)return Ap(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);"Object"===r&&t.constructor&&(r=t.constructor.name);if("Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return Ap(t,e)}(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Ap(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}function Ep(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,Mp(n.key),n)}}function Mp(t){var e=function(t,e){if("object"!=kp(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e);if("object"!=kp(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(t)}(t,"string");return"symbol"==kp(e)?e:e+""}var _p=/(-?\d+(?:\.\d+)?[a-zA-Z%]*)([*/])(-?\d+(?:\.\d+)?[a-zA-Z%]*)/,Tp=/(-?\d+(?:\.\d+)?[a-zA-Z%]*)([+-])(-?\d+(?:\.\d+)?[a-zA-Z%]*)/,Cp=/^px|cm|vh|vw|em|rem|%|mm|in|pt|pc|ex|ch|vmin|vmax|Q$/,Dp=/(-?\d+(?:\.\d+)?)([a-zA-Z%]+)?/,Ip={cm:96/2.54,mm:96/25.4,pt:96/72,pc:16,in:96,Q:96/101.6,px:1},Np=Object.keys(Ip),Bp="NaN";var Lp=function(){function t(e,r){!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t),this.num=e,this.unit=r,this.num=e,this.unit=r,Number.isNaN(e)&&(this.unit=""),""===r||Cp.test(r)||(this.num=NaN,this.unit=""),Np.includes(r)&&(this.num=function(t,e){return t*Ip[e]}(e,r),this.unit="px")}return e=t,n=[{key:"parse",value:function(e){var r,n=Pp(null!==(r=Dp.exec(e))&&void 0!==r?r:[],3),o=n[1],i=n[2];return new t(parseFloat(o),null!=i?i:"")}}],(r=[{key:"add",value:function(e){return this.unit!==e.unit?new t(NaN,""):new t(this.num+e.num,this.unit)}},{key:"subtract",value:function(e){return this.unit!==e.unit?new t(NaN,""):new t(this.num-e.num,this.unit)}},{key:"multiply",value:function(e){return""!==this.unit&&""!==e.unit&&this.unit!==e.unit?new t(NaN,""):new t(this.num*e.num,this.unit||e.unit)}},{key:"divide",value:function(e){return""!==this.unit&&""!==e.unit&&this.unit!==e.unit?new t(NaN,""):new t(this.num/e.num,this.unit||e.unit)}},{key:"toString",value:function(){return"".concat(this.num).concat(this.unit)}},{key:"isNaN",value:function(){return Number.isNaN(this.num)}}])&&Ep(e.prototype,r),n&&Ep(e,n),Object.defineProperty(e,"prototype",{writable:!1}),e;var e,r,n}();function Rp(t){if(t.includes(Bp))return Bp;for(var e=t;e.includes("*")||e.includes("/");){var r,n=Pp(null!==(r=_p.exec(e))&&void 0!==r?r:[],4),o=n[1],i=n[2],a=n[3],c=Lp.parse(null!=o?o:""),u=Lp.parse(null!=a?a:""),l="*"===i?c.multiply(u):c.divide(u);if(l.isNaN())return Bp;e=e.replace(_p,l.toString())}for(;e.includes("+")||/.-\d+(?:\.\d+)?/.test(e);){var s,f=Pp(null!==(s=Tp.exec(e))&&void 0!==s?s:[],4),p=f[1],h=f[2],y=f[3],d=Lp.parse(null!=p?p:""),v=Lp.parse(null!=y?y:""),m="+"===h?d.add(v):d.subtract(v);if(m.isNaN())return Bp;e=e.replace(Tp,m.toString())}return e}var zp=/\(([^()]*)\)/;function Up(t){var e=t.replace(/\s+/g,"");return e=function(t){for(var e=t;e.includes("(");){var r=Pp(zp.exec(e),2)[1];e=e.replace(zp,Rp(r))}return e}(e),e=Rp(e)}function $p(t){var e=function(t){try{return Up(t)}catch(e){return Bp}}(t.slice(5,-1));return e===Bp?"":e}var qp=["x","y","lineHeight","capHeight","scaleToFit","textAnchor","verticalAnchor","fill"],Fp=["dx","dy","angle","className","breakAll"];function Wp(){return Wp=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},Wp.apply(this,arguments)}function Hp(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],e.indexOf(r)>=0||Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}function Vp(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,c=[],u=!0,l=!1;try{if(i=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;u=!1}else for(;!(u=(n=i.call(r)).done)&&(c.push(n.value),c.length!==e);u=!0);}catch(s){l=!0,o=s}finally{try{if(!u&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(l)throw o}}return c}}(t,e)||function(t,e){if(!t)return;if("string"==typeof t)return Xp(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);"Object"===r&&t.constructor&&(r=t.constructor.name);if("Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return Xp(t,e)}(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Xp(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}var Gp=/[ \f\n\r\t\v\u2028\u2029]+/,Yp=function(t){var e=t.children,r=t.breakAll,n=t.style;try{var o=[];return Gr(e)||(o=r?e.toString().split(""):e.toString().split(Gp)),{wordsWithComputedWidth:o.map(function(t){return{word:t,width:Sp(t,n).width}}),spaceWidth:r?0:Sp(" ",n).width}}catch(i){return null}},Kp=function(t){return[{words:Gr(t)?[]:t.toString().split(Gp)}]},Zp=function(t){var e=t.width,r=t.scaleToFit,n=t.children,o=t.style,i=t.breakAll,a=t.maxLines;if((e||r)&&!_f.isSsr){var c=Yp({breakAll:i,children:n,style:o});return c?function(t,e,r,n,o){var i=t.maxLines,a=t.children,c=t.style,u=t.breakAll,l=An(i),s=a,f=function(){return(arguments.length>0&&void 0!==arguments[0]?arguments[0]:[]).reduce(function(t,e){var i=e.word,a=e.width,c=t[t.length-1];if(c&&(null==n||o||c.width+a+r<Number(n)))c.words.push(i),c.width+=a+r;else{var u={words:[i],width:a};t.push(u)}return t},[])},p=f(e);if(!l)return p;for(var h,y=function(t){var e=s.slice(0,t),r=Yp({breakAll:u,style:c,children:e+"…"}).wordsWithComputedWidth,o=f(r),a=o.length>i||function(t){return t.reduce(function(t,e){return t.width>e.width?t:e})}(o).width>Number(n);return[a,o]},d=0,v=s.length-1,m=0;d<=v&&m<=s.length-1;){var b=Math.floor((d+v)/2),g=Vp(y(b-1),2),w=g[0],x=g[1],O=Vp(y(b),1)[0];if(w||O||(d=b+1),w&&O&&(v=b-1),!w&&O){h=x;break}m++}return h||p}({breakAll:i,children:n,maxLines:a,style:o},c.wordsWithComputedWidth,c.spaceWidth,e,r):Kp(n)}return Kp(n)},Jp="#808080",Qp=function(e){var r=e.x,o=void 0===r?0:r,i=e.y,a=void 0===i?0:i,c=e.lineHeight,u=void 0===c?"1em":c,l=e.capHeight,s=void 0===l?"0.71em":l,f=e.scaleToFit,p=void 0!==f&&f,h=e.textAnchor,y=void 0===h?"start":h,d=e.verticalAnchor,v=void 0===d?"end":d,m=e.fill,b=void 0===m?Jp:m,g=Hp(e,qp),w=t.useMemo(function(){return Zp({breakAll:g.breakAll,children:g.children,maxLines:g.maxLines,scaleToFit:p,style:g.style,width:g.width})},[g.breakAll,g.children,g.maxLines,p,g.style,g.width]),x=g.dx,O=g.dy,j=g.angle,S=g.className,k=g.breakAll,P=Hp(g,Fp);if(!En(o)||!En(a))return null;var A,E=o+(An(x)?x:0),M=a+(An(O)?O:0);switch(v){case"start":A=$p("calc(".concat(s,")"));break;case"middle":A=$p("calc(".concat((w.length-1)/2," * -").concat(u," + (").concat(s," / 2))"));break;default:A=$p("calc(".concat(w.length-1," * -").concat(u,")"))}var _=[];if(p){var T=w[0].width,C=g.width;_.push("scale(".concat((An(C)?C/T:1)/T,")"))}return j&&_.push("rotate(".concat(j,", ").concat(E,", ").concat(M,")")),_.length&&(P.transform=_.join(" ")),n.createElement("text",Wp({},no(P,!0),{x:E,y:M,className:St("recharts-text",S),textAnchor:y,fill:b.includes("url")?Jp:b}),w.map(function(t,e){var r=t.words.join(k?"":" ");return n.createElement("tspan",{x:E,dy:0===e?A:u,key:"".concat(r,"-").concat(e)},r)}))};function th(t,e){return null==t||null==e?NaN:t<e?-1:t>e?1:t>=e?0:NaN}function eh(t,e){return null==t||null==e?NaN:e<t?-1:e>t?1:e>=t?0:NaN}function rh(t){let e,r,n;function o(t,n,o=0,i=t.length){if(o<i){if(0!==e(n,n))return i;do{const e=o+i>>>1;r(t[e],n)<0?o=e+1:i=e}while(o<i)}return o}return 2!==t.length?(e=th,r=(e,r)=>th(t(e),r),n=(e,r)=>t(e)-r):(e=t===th||t===eh?t:nh,r=t,n=t),{left:o,center:function(t,e,r=0,i=t.length){const a=o(t,e,r,i-1);return a>r&&n(t[a-1],e)>-n(t[a],e)?a-1:a},right:function(t,n,o=0,i=t.length){if(o<i){if(0!==e(n,n))return i;do{const e=o+i>>>1;r(t[e],n)<=0?o=e+1:i=e}while(o<i)}return o}}}function nh(){return 0}function oh(t){return null===t?NaN:+t}const ih=rh(th).right;rh(oh).center;class ah extends Map{constructor(t,e=uh){if(super(),Object.defineProperties(this,{_intern:{value:new Map},_key:{value:e}}),null!=t)for(const[r,n]of t)this.set(r,n)}get(t){return super.get(ch(this,t))}has(t){return super.has(ch(this,t))}set(t,e){return super.set(function({_intern:t,_key:e},r){const n=e(r);return t.has(n)?t.get(n):(t.set(n,r),r)}(this,t),e)}delete(t){return super.delete(function({_intern:t,_key:e},r){const n=e(r);t.has(n)&&(r=t.get(n),t.delete(n));return r}(this,t))}}function ch({_intern:t,_key:e},r){const n=e(r);return t.has(n)?t.get(n):r}function uh(t){return null!==t&&"object"==typeof t?t.valueOf():t}function lh(t,e){return(null==t||!(t>=t))-(null==e||!(e>=e))||(t<e?-1:t>e?1:0)}const sh=Math.sqrt(50),fh=Math.sqrt(10),ph=Math.sqrt(2);function hh(t,e,r){const n=(e-t)/Math.max(0,r),o=Math.floor(Math.log10(n)),i=n/Math.pow(10,o),a=i>=sh?10:i>=fh?5:i>=ph?2:1;let c,u,l;return o<0?(l=Math.pow(10,-o)/a,c=Math.round(t*l),u=Math.round(e*l),c/l<t&&++c,u/l>e&&--u,l=-l):(l=Math.pow(10,o)*a,c=Math.round(t/l),u=Math.round(e/l),c*l<t&&++c,u*l>e&&--u),u<c&&.5<=r&&r<2?hh(t,e,2*r):[c,u,l]}function yh(t,e,r){if(!((r=+r)>0))return[];if((t=+t)===(e=+e))return[t];const n=e<t,[o,i,a]=n?hh(e,t,r):hh(t,e,r);if(!(i>=o))return[];const c=i-o+1,u=new Array(c);if(n)if(a<0)for(let l=0;l<c;++l)u[l]=(i-l)/-a;else for(let l=0;l<c;++l)u[l]=(i-l)*a;else if(a<0)for(let l=0;l<c;++l)u[l]=(o+l)/-a;else for(let l=0;l<c;++l)u[l]=(o+l)*a;return u}function dh(t,e,r){return hh(t=+t,e=+e,r=+r)[2]}function vh(t,e,r){r=+r;const n=(e=+e)<(t=+t),o=n?dh(e,t,r):dh(t,e,r);return(n?-1:1)*(o<0?1/-o:o)}function mh(t,e){let r;for(const n of t)null!=n&&(r<n||void 0===r&&n>=n)&&(r=n);return r}function bh(t,e){let r;for(const n of t)null!=n&&(r>n||void 0===r&&n>=n)&&(r=n);return r}function gh(t,e,r=0,n=1/0,o){if(e=Math.floor(e),r=Math.floor(Math.max(0,r)),n=Math.floor(Math.min(t.length-1,n)),!(r<=e&&e<=n))return t;for(o=void 0===o?lh:function(t=th){if(t===th)return lh;if("function"!=typeof t)throw new TypeError("compare is not a function");return(e,r)=>{const n=t(e,r);return n||0===n?n:(0===t(r,r))-(0===t(e,e))}}(o);n>r;){if(n-r>600){const i=n-r+1,a=e-r+1,c=Math.log(i),u=.5*Math.exp(2*c/3),l=.5*Math.sqrt(c*u*(i-u)/i)*(a-i/2<0?-1:1);gh(t,e,Math.max(r,Math.floor(e-a*u/i+l)),Math.min(n,Math.floor(e+(i-a)*u/i+l)),o)}const i=t[e];let a=r,c=n;for(wh(t,r,e),o(t[n],i)>0&&wh(t,r,n);a<c;){for(wh(t,a,c),++a,--c;o(t[a],i)<0;)++a;for(;o(t[c],i)>0;)--c}0===o(t[r],i)?wh(t,r,c):(++c,wh(t,c,n)),c<=e&&(r=c+1),e<=c&&(n=c-1)}return t}function wh(t,e,r){const n=t[e];t[e]=t[r],t[r]=n}function xh(t,e,r=oh){if((n=t.length)&&!isNaN(e=+e)){if(e<=0||n<2)return+r(t[0],0,t);if(e>=1)return+r(t[n-1],n-1,t);var n,o=(n-1)*e,i=Math.floor(o),a=+r(t[i],i,t);return a+(+r(t[i+1],i+1,t)-a)*(o-i)}}function Oh(t,e){switch(arguments.length){case 0:break;case 1:this.range(t);break;default:this.range(e).domain(t)}return this}function jh(t,e){switch(arguments.length){case 0:break;case 1:"function"==typeof t?this.interpolator(t):this.range(t);break;default:this.domain(t),"function"==typeof e?this.interpolator(e):this.range(e)}return this}const Sh=Symbol("implicit");function kh(){var t=new ah,e=[],r=[],n=Sh;function o(o){let i=t.get(o);if(void 0===i){if(n!==Sh)return n;t.set(o,i=e.push(o)-1)}return r[i%r.length]}return o.domain=function(r){if(!arguments.length)return e.slice();e=[],t=new ah;for(const n of r)t.has(n)||t.set(n,e.push(n)-1);return o},o.range=function(t){return arguments.length?(r=Array.from(t),o):r.slice()},o.unknown=function(t){return arguments.length?(n=t,o):n},o.copy=function(){return kh(e,r).unknown(n)},Oh.apply(o,arguments),o}function Ph(){var t,e,r=kh().unknown(void 0),n=r.domain,o=r.range,i=0,a=1,c=!1,u=0,l=0,s=.5;function f(){var r=n().length,f=a<i,p=f?a:i,h=f?i:a;t=(h-p)/Math.max(1,r-u+2*l),c&&(t=Math.floor(t)),p+=(h-p-t*(r-u))*s,e=t*(1-u),c&&(p=Math.round(p),e=Math.round(e));var y=function(t,e,r){t=+t,e=+e,r=(o=arguments.length)<2?(e=t,t=0,1):o<3?1:+r;for(var n=-1,o=0|Math.max(0,Math.ceil((e-t)/r)),i=new Array(o);++n<o;)i[n]=t+n*r;return i}(r).map(function(e){return p+t*e});return o(f?y.reverse():y)}return delete r.unknown,r.domain=function(t){return arguments.length?(n(t),f()):n()},r.range=function(t){return arguments.length?([i,a]=t,i=+i,a=+a,f()):[i,a]},r.rangeRound=function(t){return[i,a]=t,i=+i,a=+a,c=!0,f()},r.bandwidth=function(){return e},r.step=function(){return t},r.round=function(t){return arguments.length?(c=!!t,f()):c},r.padding=function(t){return arguments.length?(u=Math.min(1,l=+t),f()):u},r.paddingInner=function(t){return arguments.length?(u=Math.min(1,t),f()):u},r.paddingOuter=function(t){return arguments.length?(l=+t,f()):l},r.align=function(t){return arguments.length?(s=Math.max(0,Math.min(1,t)),f()):s},r.copy=function(){return Ph(n(),[i,a]).round(c).paddingInner(u).paddingOuter(l).align(s)},Oh.apply(f(),arguments)}function Ah(t){var e=t.copy;return t.padding=t.paddingOuter,delete t.paddingInner,delete t.paddingOuter,t.copy=function(){return Ah(e())},t}function Eh(){return Ah(Ph.apply(null,arguments).paddingInner(1))}function Mh(t,e,r){t.prototype=e.prototype=r,r.constructor=t}function _h(t,e){var r=Object.create(t.prototype);for(var n in e)r[n]=e[n];return r}function Th(){}var Ch=.7,Dh=1/Ch,Ih="\\s*([+-]?\\d+)\\s*",Nh="\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)\\s*",Bh="\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)%\\s*",Lh=/^#([0-9a-f]{3,8})$/,Rh=new RegExp(`^rgb\\(${Ih},${Ih},${Ih}\\)$`),zh=new RegExp(`^rgb\\(${Bh},${Bh},${Bh}\\)$`),Uh=new RegExp(`^rgba\\(${Ih},${Ih},${Ih},${Nh}\\)$`),$h=new RegExp(`^rgba\\(${Bh},${Bh},${Bh},${Nh}\\)$`),qh=new RegExp(`^hsl\\(${Nh},${Bh},${Bh}\\)$`),Fh=new RegExp(`^hsla\\(${Nh},${Bh},${Bh},${Nh}\\)$`),Wh={aliceblue:15792383,antiquewhite:16444375,aqua:65535,aquamarine:8388564,azure:15794175,beige:16119260,bisque:16770244,black:0,blanchedalmond:16772045,blue:255,blueviolet:9055202,brown:10824234,burlywood:14596231,cadetblue:6266528,chartreuse:8388352,chocolate:13789470,coral:16744272,cornflowerblue:6591981,cornsilk:16775388,crimson:14423100,cyan:65535,darkblue:139,darkcyan:35723,darkgoldenrod:12092939,darkgray:11119017,darkgreen:25600,darkgrey:11119017,darkkhaki:12433259,darkmagenta:9109643,darkolivegreen:5597999,darkorange:16747520,darkorchid:10040012,darkred:9109504,darksalmon:15308410,darkseagreen:9419919,darkslateblue:4734347,darkslategray:3100495,darkslategrey:3100495,darkturquoise:52945,darkviolet:9699539,deeppink:16716947,deepskyblue:49151,dimgray:6908265,dimgrey:6908265,dodgerblue:2003199,firebrick:11674146,floralwhite:16775920,forestgreen:2263842,fuchsia:16711935,gainsboro:14474460,ghostwhite:16316671,gold:16766720,goldenrod:14329120,gray:8421504,green:32768,greenyellow:11403055,grey:8421504,honeydew:15794160,hotpink:16738740,indianred:13458524,indigo:4915330,ivory:16777200,khaki:15787660,lavender:15132410,lavenderblush:16773365,lawngreen:8190976,lemonchiffon:16775885,lightblue:11393254,lightcoral:15761536,lightcyan:14745599,lightgoldenrodyellow:16448210,lightgray:13882323,lightgreen:9498256,lightgrey:13882323,lightpink:16758465,lightsalmon:16752762,lightseagreen:2142890,lightskyblue:8900346,lightslategray:7833753,lightslategrey:7833753,lightsteelblue:11584734,lightyellow:16777184,lime:65280,limegreen:3329330,linen:16445670,magenta:16711935,maroon:8388608,mediumaquamarine:6737322,mediumblue:205,mediumorchid:12211667,mediumpurple:9662683,mediumseagreen:3978097,mediumslateblue:8087790,mediumspringgreen:64154,mediumturquoise:4772300,mediumvioletred:13047173,midnightblue:1644912,mintcream:16121850,mistyrose:16770273,moccasin:16770229,navajowhite:16768685,navy:128,oldlace:16643558,olive:8421376,olivedrab:7048739,orange:16753920,orangered:16729344,orchid:14315734,palegoldenrod:15657130,palegreen:10025880,paleturquoise:11529966,palevioletred:14381203,papayawhip:16773077,peachpuff:16767673,peru:13468991,pink:16761035,plum:14524637,powderblue:11591910,purple:8388736,rebeccapurple:6697881,red:16711680,rosybrown:12357519,royalblue:4286945,saddlebrown:9127187,salmon:16416882,sandybrown:16032864,seagreen:3050327,seashell:16774638,sienna:10506797,silver:12632256,skyblue:8900331,slateblue:6970061,slategray:7372944,slategrey:7372944,snow:16775930,springgreen:65407,steelblue:4620980,tan:13808780,teal:32896,thistle:14204888,tomato:16737095,turquoise:4251856,violet:15631086,wheat:16113331,white:16777215,whitesmoke:16119285,yellow:16776960,yellowgreen:10145074};function Hh(){return this.rgb().formatHex()}function Vh(){return this.rgb().formatRgb()}function Xh(t){var e,r;return t=(t+"").trim().toLowerCase(),(e=Lh.exec(t))?(r=e[1].length,e=parseInt(e[1],16),6===r?Gh(e):3===r?new Zh(e>>8&15|e>>4&240,e>>4&15|240&e,(15&e)<<4|15&e,1):8===r?Yh(e>>24&255,e>>16&255,e>>8&255,(255&e)/255):4===r?Yh(e>>12&15|e>>8&240,e>>8&15|e>>4&240,e>>4&15|240&e,((15&e)<<4|15&e)/255):null):(e=Rh.exec(t))?new Zh(e[1],e[2],e[3],1):(e=zh.exec(t))?new Zh(255*e[1]/100,255*e[2]/100,255*e[3]/100,1):(e=Uh.exec(t))?Yh(e[1],e[2],e[3],e[4]):(e=$h.exec(t))?Yh(255*e[1]/100,255*e[2]/100,255*e[3]/100,e[4]):(e=qh.exec(t))?ny(e[1],e[2]/100,e[3]/100,1):(e=Fh.exec(t))?ny(e[1],e[2]/100,e[3]/100,e[4]):Wh.hasOwnProperty(t)?Gh(Wh[t]):"transparent"===t?new Zh(NaN,NaN,NaN,0):null}function Gh(t){return new Zh(t>>16&255,t>>8&255,255&t,1)}function Yh(t,e,r,n){return n<=0&&(t=e=r=NaN),new Zh(t,e,r,n)}function Kh(t,e,r,n){return 1===arguments.length?((o=t)instanceof Th||(o=Xh(o)),o?new Zh((o=o.rgb()).r,o.g,o.b,o.opacity):new Zh):new Zh(t,e,r,null==n?1:n);var o}function Zh(t,e,r,n){this.r=+t,this.g=+e,this.b=+r,this.opacity=+n}function Jh(){return`#${ry(this.r)}${ry(this.g)}${ry(this.b)}`}function Qh(){const t=ty(this.opacity);return`${1===t?"rgb(":"rgba("}${ey(this.r)}, ${ey(this.g)}, ${ey(this.b)}${1===t?")":`, ${t})`}`}function ty(t){return isNaN(t)?1:Math.max(0,Math.min(1,t))}function ey(t){return Math.max(0,Math.min(255,Math.round(t)||0))}function ry(t){return((t=ey(t))<16?"0":"")+t.toString(16)}function ny(t,e,r,n){return n<=0?t=e=r=NaN:r<=0||r>=1?t=e=NaN:e<=0&&(t=NaN),new iy(t,e,r,n)}function oy(t){if(t instanceof iy)return new iy(t.h,t.s,t.l,t.opacity);if(t instanceof Th||(t=Xh(t)),!t)return new iy;if(t instanceof iy)return t;var e=(t=t.rgb()).r/255,r=t.g/255,n=t.b/255,o=Math.min(e,r,n),i=Math.max(e,r,n),a=NaN,c=i-o,u=(i+o)/2;return c?(a=e===i?(r-n)/c+6*(r<n):r===i?(n-e)/c+2:(e-r)/c+4,c/=u<.5?i+o:2-i-o,a*=60):c=u>0&&u<1?0:a,new iy(a,c,u,t.opacity)}function iy(t,e,r,n){this.h=+t,this.s=+e,this.l=+r,this.opacity=+n}function ay(t){return(t=(t||0)%360)<0?t+360:t}function cy(t){return Math.max(0,Math.min(1,t||0))}function uy(t,e,r){return 255*(t<60?e+(r-e)*t/60:t<180?r:t<240?e+(r-e)*(240-t)/60:e)}Mh(Th,Xh,{copy(t){return Object.assign(new this.constructor,this,t)},displayable(){return this.rgb().displayable()},hex:Hh,formatHex:Hh,formatHex8:function(){return this.rgb().formatHex8()},formatHsl:function(){return oy(this).formatHsl()},formatRgb:Vh,toString:Vh}),Mh(Zh,Kh,_h(Th,{brighter(t){return t=null==t?Dh:Math.pow(Dh,t),new Zh(this.r*t,this.g*t,this.b*t,this.opacity)},darker(t){return t=null==t?Ch:Math.pow(Ch,t),new Zh(this.r*t,this.g*t,this.b*t,this.opacity)},rgb(){return this},clamp(){return new Zh(ey(this.r),ey(this.g),ey(this.b),ty(this.opacity))},displayable(){return-.5<=this.r&&this.r<255.5&&-.5<=this.g&&this.g<255.5&&-.5<=this.b&&this.b<255.5&&0<=this.opacity&&this.opacity<=1},hex:Jh,formatHex:Jh,formatHex8:function(){return`#${ry(this.r)}${ry(this.g)}${ry(this.b)}${ry(255*(isNaN(this.opacity)?1:this.opacity))}`},formatRgb:Qh,toString:Qh})),Mh(iy,function(t,e,r,n){return 1===arguments.length?oy(t):new iy(t,e,r,null==n?1:n)},_h(Th,{brighter(t){return t=null==t?Dh:Math.pow(Dh,t),new iy(this.h,this.s,this.l*t,this.opacity)},darker(t){return t=null==t?Ch:Math.pow(Ch,t),new iy(this.h,this.s,this.l*t,this.opacity)},rgb(){var t=this.h%360+360*(this.h<0),e=isNaN(t)||isNaN(this.s)?0:this.s,r=this.l,n=r+(r<.5?r:1-r)*e,o=2*r-n;return new Zh(uy(t>=240?t-240:t+120,o,n),uy(t,o,n),uy(t<120?t+240:t-120,o,n),this.opacity)},clamp(){return new iy(ay(this.h),cy(this.s),cy(this.l),ty(this.opacity))},displayable(){return(0<=this.s&&this.s<=1||isNaN(this.s))&&0<=this.l&&this.l<=1&&0<=this.opacity&&this.opacity<=1},formatHsl(){const t=ty(this.opacity);return`${1===t?"hsl(":"hsla("}${ay(this.h)}, ${100*cy(this.s)}%, ${100*cy(this.l)}%${1===t?")":`, ${t})`}`}}));const ly=t=>()=>t;function sy(t){return 1===(t=+t)?fy:function(e,r){return r-e?function(t,e,r){return t=Math.pow(t,r),e=Math.pow(e,r)-t,r=1/r,function(n){return Math.pow(t+n*e,r)}}(e,r,t):ly(isNaN(e)?r:e)}}function fy(t,e){var r=e-t;return r?function(t,e){return function(r){return t+r*e}}(t,r):ly(isNaN(t)?e:t)}const py=function t(e){var r=sy(e);function n(t,e){var n=r((t=Kh(t)).r,(e=Kh(e)).r),o=r(t.g,e.g),i=r(t.b,e.b),a=fy(t.opacity,e.opacity);return function(e){return t.r=n(e),t.g=o(e),t.b=i(e),t.opacity=a(e),t+""}}return n.gamma=t,n}(1);function hy(t,e){e||(e=[]);var r,n=t?Math.min(e.length,t.length):0,o=e.slice();return function(i){for(r=0;r<n;++r)o[r]=t[r]*(1-i)+e[r]*i;return o}}function yy(t,e){var r,n=e?e.length:0,o=t?Math.min(n,t.length):0,i=new Array(o),a=new Array(n);for(r=0;r<o;++r)i[r]=xy(t[r],e[r]);for(;r<n;++r)a[r]=e[r];return function(t){for(r=0;r<o;++r)a[r]=i[r](t);return a}}function dy(t,e){var r=new Date;return t=+t,e=+e,function(n){return r.setTime(t*(1-n)+e*n),r}}function vy(t,e){return t=+t,e=+e,function(r){return t*(1-r)+e*r}}function my(t,e){var r,n={},o={};for(r in null!==t&&"object"==typeof t||(t={}),null!==e&&"object"==typeof e||(e={}),e)r in t?n[r]=xy(t[r],e[r]):o[r]=e[r];return function(t){for(r in n)o[r]=n[r](t);return o}}var by=/[-+]?(?:\d+\.?\d*|\.?\d+)(?:[eE][-+]?\d+)?/g,gy=new RegExp(by.source,"g");function wy(t,e){var r,n,o,i=by.lastIndex=gy.lastIndex=0,a=-1,c=[],u=[];for(t+="",e+="";(r=by.exec(t))&&(n=gy.exec(e));)(o=n.index)>i&&(o=e.slice(i,o),c[a]?c[a]+=o:c[++a]=o),(r=r[0])===(n=n[0])?c[a]?c[a]+=n:c[++a]=n:(c[++a]=null,u.push({i:a,x:vy(r,n)})),i=gy.lastIndex;return i<e.length&&(o=e.slice(i),c[a]?c[a]+=o:c[++a]=o),c.length<2?u[0]?function(t){return function(e){return t(e)+""}}(u[0].x):function(t){return function(){return t}}(e):(e=u.length,function(t){for(var r,n=0;n<e;++n)c[(r=u[n]).i]=r.x(t);return c.join("")})}function xy(t,e){var r,n,o=typeof e;return null==e||"boolean"===o?ly(e):("number"===o?vy:"string"===o?(r=Xh(e))?(e=r,py):wy:e instanceof Xh?py:e instanceof Date?dy:(n=e,!ArrayBuffer.isView(n)||n instanceof DataView?Array.isArray(e)?yy:"function"!=typeof e.valueOf&&"function"!=typeof e.toString||isNaN(e)?my:vy:hy))(t,e)}function Oy(t,e){return t=+t,e=+e,function(r){return Math.round(t*(1-r)+e*r)}}function jy(t){return+t}var Sy=[0,1];function ky(t){return t}function Py(t,e){return(e-=t=+t)?function(r){return(r-t)/e}:(r=isNaN(e)?NaN:.5,function(){return r});var r}function Ay(t,e,r){var n=t[0],o=t[1],i=e[0],a=e[1];return o<n?(n=Py(o,n),i=r(a,i)):(n=Py(n,o),i=r(i,a)),function(t){return i(n(t))}}function Ey(t,e,r){var n=Math.min(t.length,e.length)-1,o=new Array(n),i=new Array(n),a=-1;for(t[n]<t[0]&&(t=t.slice().reverse(),e=e.slice().reverse());++a<n;)o[a]=Py(t[a],t[a+1]),i[a]=r(e[a],e[a+1]);return function(e){var r=ih(t,e,1,n)-1;return i[r](o[r](e))}}function My(t,e){return e.domain(t.domain()).range(t.range()).interpolate(t.interpolate()).clamp(t.clamp()).unknown(t.unknown())}function _y(){var t,e,r,n,o,i,a=Sy,c=Sy,u=xy,l=ky;function s(){var t,e,r,u=Math.min(a.length,c.length);return l!==ky&&(t=a[0],e=a[u-1],t>e&&(r=t,t=e,e=r),l=function(r){return Math.max(t,Math.min(e,r))}),n=u>2?Ey:Ay,o=i=null,f}function f(e){return null==e||isNaN(e=+e)?r:(o||(o=n(a.map(t),c,u)))(t(l(e)))}return f.invert=function(r){return l(e((i||(i=n(c,a.map(t),vy)))(r)))},f.domain=function(t){return arguments.length?(a=Array.from(t,jy),s()):a.slice()},f.range=function(t){return arguments.length?(c=Array.from(t),s()):c.slice()},f.rangeRound=function(t){return c=Array.from(t),u=Oy,s()},f.clamp=function(t){return arguments.length?(l=!!t||ky,s()):l!==ky},f.interpolate=function(t){return arguments.length?(u=t,s()):u},f.unknown=function(t){return arguments.length?(r=t,f):r},function(r,n){return t=r,e=n,s()}}function Ty(){return _y()(ky,ky)}function Cy(t,e){if((r=(t=e?t.toExponential(e-1):t.toExponential()).indexOf("e"))<0)return null;var r,n=t.slice(0,r);return[n.length>1?n[0]+n.slice(2):n,+t.slice(r+1)]}function Dy(t){return(t=Cy(Math.abs(t)))?t[1]:NaN}var Iy,Ny=/^(?:(.)?([<>=^]))?([+\-( ])?([$#])?(0)?(\d+)?(,)?(\.\d+)?(~)?([a-z%])?$/i;function By(t){if(!(e=Ny.exec(t)))throw new Error("invalid format: "+t);var e;return new Ly({fill:e[1],align:e[2],sign:e[3],symbol:e[4],zero:e[5],width:e[6],comma:e[7],precision:e[8]&&e[8].slice(1),trim:e[9],type:e[10]})}function Ly(t){this.fill=void 0===t.fill?" ":t.fill+"",this.align=void 0===t.align?">":t.align+"",this.sign=void 0===t.sign?"-":t.sign+"",this.symbol=void 0===t.symbol?"":t.symbol+"",this.zero=!!t.zero,this.width=void 0===t.width?void 0:+t.width,this.comma=!!t.comma,this.precision=void 0===t.precision?void 0:+t.precision,this.trim=!!t.trim,this.type=void 0===t.type?"":t.type+""}function Ry(t,e){var r=Cy(t,e);if(!r)return t+"";var n=r[0],o=r[1];return o<0?"0."+new Array(-o).join("0")+n:n.length>o+1?n.slice(0,o+1)+"."+n.slice(o+1):n+new Array(o-n.length+2).join("0")}By.prototype=Ly.prototype,Ly.prototype.toString=function(){return this.fill+this.align+this.sign+this.symbol+(this.zero?"0":"")+(void 0===this.width?"":Math.max(1,0|this.width))+(this.comma?",":"")+(void 0===this.precision?"":"."+Math.max(0,0|this.precision))+(this.trim?"~":"")+this.type};const zy={"%":(t,e)=>(100*t).toFixed(e),b:t=>Math.round(t).toString(2),c:t=>t+"",d:function(t){return Math.abs(t=Math.round(t))>=1e21?t.toLocaleString("en").replace(/,/g,""):t.toString(10)},e:(t,e)=>t.toExponential(e),f:(t,e)=>t.toFixed(e),g:(t,e)=>t.toPrecision(e),o:t=>Math.round(t).toString(8),p:(t,e)=>Ry(100*t,e),r:Ry,s:function(t,e){var r=Cy(t,e);if(!r)return t+"";var n=r[0],o=r[1],i=o-(Iy=3*Math.max(-8,Math.min(8,Math.floor(o/3))))+1,a=n.length;return i===a?n:i>a?n+new Array(i-a+1).join("0"):i>0?n.slice(0,i)+"."+n.slice(i):"0."+new Array(1-i).join("0")+Cy(t,Math.max(0,e+i-1))[0]},X:t=>Math.round(t).toString(16).toUpperCase(),x:t=>Math.round(t).toString(16)};function Uy(t){return t}var $y,qy,Fy,Wy=Array.prototype.map,Hy=["y","z","a","f","p","n","µ","m","","k","M","G","T","P","E","Z","Y"];function Vy(t){var e,r,n=void 0===t.grouping||void 0===t.thousands?Uy:(e=Wy.call(t.grouping,Number),r=t.thousands+"",function(t,n){for(var o=t.length,i=[],a=0,c=e[0],u=0;o>0&&c>0&&(u+c+1>n&&(c=Math.max(1,n-u)),i.push(t.substring(o-=c,o+c)),!((u+=c+1)>n));)c=e[a=(a+1)%e.length];return i.reverse().join(r)}),o=void 0===t.currency?"":t.currency[0]+"",i=void 0===t.currency?"":t.currency[1]+"",a=void 0===t.decimal?".":t.decimal+"",c=void 0===t.numerals?Uy:function(t){return function(e){return e.replace(/[0-9]/g,function(e){return t[+e]})}}(Wy.call(t.numerals,String)),u=void 0===t.percent?"%":t.percent+"",l=void 0===t.minus?"−":t.minus+"",s=void 0===t.nan?"NaN":t.nan+"";function f(t){var e=(t=By(t)).fill,r=t.align,f=t.sign,p=t.symbol,h=t.zero,y=t.width,d=t.comma,v=t.precision,m=t.trim,b=t.type;"n"===b?(d=!0,b="g"):zy[b]||(void 0===v&&(v=12),m=!0,b="g"),(h||"0"===e&&"="===r)&&(h=!0,e="0",r="=");var g="$"===p?o:"#"===p&&/[boxX]/.test(b)?"0"+b.toLowerCase():"",w="$"===p?i:/[%p]/.test(b)?u:"",x=zy[b],O=/[defgprs%]/.test(b);function j(t){var o,i,u,p=g,j=w;if("c"===b)j=x(t)+j,t="";else{var S=(t=+t)<0||1/t<0;if(t=isNaN(t)?s:x(Math.abs(t),v),m&&(t=function(t){t:for(var e,r=t.length,n=1,o=-1;n<r;++n)switch(t[n]){case".":o=e=n;break;case"0":0===o&&(o=n),e=n;break;default:if(!+t[n])break t;o>0&&(o=0)}return o>0?t.slice(0,o)+t.slice(e+1):t}(t)),S&&0===+t&&"+"!==f&&(S=!1),p=(S?"("===f?f:l:"-"===f||"("===f?"":f)+p,j=("s"===b?Hy[8+Iy/3]:"")+j+(S&&"("===f?")":""),O)for(o=-1,i=t.length;++o<i;)if(48>(u=t.charCodeAt(o))||u>57){j=(46===u?a+t.slice(o+1):t.slice(o))+j,t=t.slice(0,o);break}}d&&!h&&(t=n(t,1/0));var k=p.length+t.length+j.length,P=k<y?new Array(y-k+1).join(e):"";switch(d&&h&&(t=n(P+t,P.length?y-j.length:1/0),P=""),r){case"<":t=p+t+j+P;break;case"=":t=p+P+t+j;break;case"^":t=P.slice(0,k=P.length>>1)+p+t+j+P.slice(k);break;default:t=P+p+t+j}return c(t)}return v=void 0===v?6:/[gprs]/.test(b)?Math.max(1,Math.min(21,v)):Math.max(0,Math.min(20,v)),j.toString=function(){return t+""},j}return{format:f,formatPrefix:function(t,e){var r=f(((t=By(t)).type="f",t)),n=3*Math.max(-8,Math.min(8,Math.floor(Dy(e)/3))),o=Math.pow(10,-n),i=Hy[8+n/3];return function(t){return r(o*t)+i}}}}function Xy(t,e,r,n){var o,i=vh(t,e,r);switch((n=By(null==n?",f":n)).type){case"s":var a=Math.max(Math.abs(t),Math.abs(e));return null!=n.precision||isNaN(o=function(t,e){return Math.max(0,3*Math.max(-8,Math.min(8,Math.floor(Dy(e)/3)))-Dy(Math.abs(t)))}(i,a))||(n.precision=o),Fy(n,a);case"":case"e":case"g":case"p":case"r":null!=n.precision||isNaN(o=function(t,e){return t=Math.abs(t),e=Math.abs(e)-t,Math.max(0,Dy(e)-Dy(t))+1}(i,Math.max(Math.abs(t),Math.abs(e))))||(n.precision=o-("e"===n.type));break;case"f":case"%":null!=n.precision||isNaN(o=function(t){return Math.max(0,-Dy(Math.abs(t)))}(i))||(n.precision=o-2*("%"===n.type))}return qy(n)}function Gy(t){var e=t.domain;return t.ticks=function(t){var r=e();return yh(r[0],r[r.length-1],null==t?10:t)},t.tickFormat=function(t,r){var n=e();return Xy(n[0],n[n.length-1],null==t?10:t,r)},t.nice=function(r){null==r&&(r=10);var n,o,i=e(),a=0,c=i.length-1,u=i[a],l=i[c],s=10;for(l<u&&(o=u,u=l,l=o,o=a,a=c,c=o);s-- >0;){if((o=dh(u,l,r))===n)return i[a]=u,i[c]=l,e(i);if(o>0)u=Math.floor(u/o)*o,l=Math.ceil(l/o)*o;else{if(!(o<0))break;u=Math.ceil(u*o)/o,l=Math.floor(l*o)/o}n=o}return t},t}function Yy(){var t=Ty();return t.copy=function(){return My(t,Yy())},Oh.apply(t,arguments),Gy(t)}function Ky(t,e){var r,n=0,o=(t=t.slice()).length-1,i=t[n],a=t[o];return a<i&&(r=n,n=o,o=r,r=i,i=a,a=r),t[n]=e.floor(i),t[o]=e.ceil(a),t}function Zy(t){return Math.log(t)}function Jy(t){return Math.exp(t)}function Qy(t){return-Math.log(-t)}function td(t){return-Math.exp(-t)}function ed(t){return isFinite(t)?+("1e"+t):t<0?0:t}function rd(t){return(e,r)=>-t(-e,r)}function nd(t){const e=t(Zy,Jy),r=e.domain;let n,o,i=10;function a(){return n=function(t){return t===Math.E?Math.log:10===t&&Math.log10||2===t&&Math.log2||(t=Math.log(t),e=>Math.log(e)/t)}(i),o=function(t){return 10===t?ed:t===Math.E?Math.exp:e=>Math.pow(t,e)}(i),r()[0]<0?(n=rd(n),o=rd(o),t(Qy,td)):t(Zy,Jy),e}return e.base=function(t){return arguments.length?(i=+t,a()):i},e.domain=function(t){return arguments.length?(r(t),a()):r()},e.ticks=t=>{const e=r();let a=e[0],c=e[e.length-1];const u=c<a;u&&([a,c]=[c,a]);let l,s,f=n(a),p=n(c);const h=null==t?10:+t;let y=[];if(!(i%1)&&p-f<h){if(f=Math.floor(f),p=Math.ceil(p),a>0){for(;f<=p;++f)for(l=1;l<i;++l)if(s=f<0?l/o(-f):l*o(f),!(s<a)){if(s>c)break;y.push(s)}}else for(;f<=p;++f)for(l=i-1;l>=1;--l)if(s=f>0?l/o(-f):l*o(f),!(s<a)){if(s>c)break;y.push(s)}2*y.length<h&&(y=yh(a,c,h))}else y=yh(f,p,Math.min(p-f,h)).map(o);return u?y.reverse():y},e.tickFormat=(t,r)=>{if(null==t&&(t=10),null==r&&(r=10===i?"s":","),"function"!=typeof r&&(i%1||null!=(r=By(r)).precision||(r.trim=!0),r=qy(r)),t===1/0)return r;const a=Math.max(1,i*t/e.ticks().length);return t=>{let e=t/o(Math.round(n(t)));return e*i<i-.5&&(e*=i),e<=a?r(t):""}},e.nice=()=>r(Ky(r(),{floor:t=>o(Math.floor(n(t))),ceil:t=>o(Math.ceil(n(t)))})),e}function od(t){return function(e){return Math.sign(e)*Math.log1p(Math.abs(e/t))}}function id(t){return function(e){return Math.sign(e)*Math.expm1(Math.abs(e))*t}}function ad(t){var e=1,r=t(od(e),id(e));return r.constant=function(r){return arguments.length?t(od(e=+r),id(e)):e},Gy(r)}function cd(t){return function(e){return e<0?-Math.pow(-e,t):Math.pow(e,t)}}function ud(t){return t<0?-Math.sqrt(-t):Math.sqrt(t)}function ld(t){return t<0?-t*t:t*t}function sd(t){var e=t(ky,ky),r=1;return e.exponent=function(e){return arguments.length?1===(r=+e)?t(ky,ky):.5===r?t(ud,ld):t(cd(r),cd(1/r)):r},Gy(e)}function fd(){var t=sd(_y());return t.copy=function(){return My(t,fd()).exponent(t.exponent())},Oh.apply(t,arguments),t}function pd(t){return Math.sign(t)*t*t}$y=Vy({thousands:",",grouping:[3],currency:["$",""]}),qy=$y.format,Fy=$y.formatPrefix;const hd=new Date,yd=new Date;function dd(t,e,r,n){function o(e){return t(e=0===arguments.length?new Date:new Date(+e)),e}return o.floor=e=>(t(e=new Date(+e)),e),o.ceil=r=>(t(r=new Date(r-1)),e(r,1),t(r),r),o.round=t=>{const e=o(t),r=o.ceil(t);return t-e<r-t?e:r},o.offset=(t,r)=>(e(t=new Date(+t),null==r?1:Math.floor(r)),t),o.range=(r,n,i)=>{const a=[];if(r=o.ceil(r),i=null==i?1:Math.floor(i),!(r<n&&i>0))return a;let c;do{a.push(c=new Date(+r)),e(r,i),t(r)}while(c<r&&r<n);return a},o.filter=r=>dd(e=>{if(e>=e)for(;t(e),!r(e);)e.setTime(e-1)},(t,n)=>{if(t>=t)if(n<0)for(;++n<=0;)for(;e(t,-1),!r(t););else for(;--n>=0;)for(;e(t,1),!r(t););}),r&&(o.count=(e,n)=>(hd.setTime(+e),yd.setTime(+n),t(hd),t(yd),Math.floor(r(hd,yd))),o.every=t=>(t=Math.floor(t),isFinite(t)&&t>0?t>1?o.filter(n?e=>n(e)%t===0:e=>o.count(0,e)%t===0):o:null)),o}const vd=dd(()=>{},(t,e)=>{t.setTime(+t+e)},(t,e)=>e-t);vd.every=t=>(t=Math.floor(t),isFinite(t)&&t>0?t>1?dd(e=>{e.setTime(Math.floor(e/t)*t)},(e,r)=>{e.setTime(+e+r*t)},(e,r)=>(r-e)/t):vd:null),vd.range;const md=1e3,bd=6e4,gd=36e5,wd=864e5,xd=6048e5,Od=2592e6,jd=31536e6,Sd=dd(t=>{t.setTime(t-t.getMilliseconds())},(t,e)=>{t.setTime(+t+e*md)},(t,e)=>(e-t)/md,t=>t.getUTCSeconds());Sd.range;const kd=dd(t=>{t.setTime(t-t.getMilliseconds()-t.getSeconds()*md)},(t,e)=>{t.setTime(+t+e*bd)},(t,e)=>(e-t)/bd,t=>t.getMinutes());kd.range;const Pd=dd(t=>{t.setUTCSeconds(0,0)},(t,e)=>{t.setTime(+t+e*bd)},(t,e)=>(e-t)/bd,t=>t.getUTCMinutes());Pd.range;const Ad=dd(t=>{t.setTime(t-t.getMilliseconds()-t.getSeconds()*md-t.getMinutes()*bd)},(t,e)=>{t.setTime(+t+e*gd)},(t,e)=>(e-t)/gd,t=>t.getHours());Ad.range;const Ed=dd(t=>{t.setUTCMinutes(0,0,0)},(t,e)=>{t.setTime(+t+e*gd)},(t,e)=>(e-t)/gd,t=>t.getUTCHours());Ed.range;const Md=dd(t=>t.setHours(0,0,0,0),(t,e)=>t.setDate(t.getDate()+e),(t,e)=>(e-t-(e.getTimezoneOffset()-t.getTimezoneOffset())*bd)/wd,t=>t.getDate()-1);Md.range;const _d=dd(t=>{t.setUTCHours(0,0,0,0)},(t,e)=>{t.setUTCDate(t.getUTCDate()+e)},(t,e)=>(e-t)/wd,t=>t.getUTCDate()-1);_d.range;const Td=dd(t=>{t.setUTCHours(0,0,0,0)},(t,e)=>{t.setUTCDate(t.getUTCDate()+e)},(t,e)=>(e-t)/wd,t=>Math.floor(t/wd));function Cd(t){return dd(e=>{e.setDate(e.getDate()-(e.getDay()+7-t)%7),e.setHours(0,0,0,0)},(t,e)=>{t.setDate(t.getDate()+7*e)},(t,e)=>(e-t-(e.getTimezoneOffset()-t.getTimezoneOffset())*bd)/xd)}Td.range;const Dd=Cd(0),Id=Cd(1),Nd=Cd(2),Bd=Cd(3),Ld=Cd(4),Rd=Cd(5),zd=Cd(6);function Ud(t){return dd(e=>{e.setUTCDate(e.getUTCDate()-(e.getUTCDay()+7-t)%7),e.setUTCHours(0,0,0,0)},(t,e)=>{t.setUTCDate(t.getUTCDate()+7*e)},(t,e)=>(e-t)/xd)}Dd.range,Id.range,Nd.range,Bd.range,Ld.range,Rd.range,zd.range;const $d=Ud(0),qd=Ud(1),Fd=Ud(2),Wd=Ud(3),Hd=Ud(4),Vd=Ud(5),Xd=Ud(6);$d.range,qd.range,Fd.range,Wd.range,Hd.range,Vd.range,Xd.range;const Gd=dd(t=>{t.setDate(1),t.setHours(0,0,0,0)},(t,e)=>{t.setMonth(t.getMonth()+e)},(t,e)=>e.getMonth()-t.getMonth()+12*(e.getFullYear()-t.getFullYear()),t=>t.getMonth());Gd.range;const Yd=dd(t=>{t.setUTCDate(1),t.setUTCHours(0,0,0,0)},(t,e)=>{t.setUTCMonth(t.getUTCMonth()+e)},(t,e)=>e.getUTCMonth()-t.getUTCMonth()+12*(e.getUTCFullYear()-t.getUTCFullYear()),t=>t.getUTCMonth());Yd.range;const Kd=dd(t=>{t.setMonth(0,1),t.setHours(0,0,0,0)},(t,e)=>{t.setFullYear(t.getFullYear()+e)},(t,e)=>e.getFullYear()-t.getFullYear(),t=>t.getFullYear());Kd.every=t=>isFinite(t=Math.floor(t))&&t>0?dd(e=>{e.setFullYear(Math.floor(e.getFullYear()/t)*t),e.setMonth(0,1),e.setHours(0,0,0,0)},(e,r)=>{e.setFullYear(e.getFullYear()+r*t)}):null,Kd.range;const Zd=dd(t=>{t.setUTCMonth(0,1),t.setUTCHours(0,0,0,0)},(t,e)=>{t.setUTCFullYear(t.getUTCFullYear()+e)},(t,e)=>e.getUTCFullYear()-t.getUTCFullYear(),t=>t.getUTCFullYear());function Jd(t,e,r,n,o,i){const a=[[Sd,1,md],[Sd,5,5e3],[Sd,15,15e3],[Sd,30,3e4],[i,1,bd],[i,5,3e5],[i,15,9e5],[i,30,18e5],[o,1,gd],[o,3,108e5],[o,6,216e5],[o,12,432e5],[n,1,wd],[n,2,1728e5],[r,1,xd],[e,1,Od],[e,3,7776e6],[t,1,jd]];function c(e,r,n){const o=Math.abs(r-e)/n,i=rh(([,,t])=>t).right(a,o);if(i===a.length)return t.every(vh(e/jd,r/jd,n));if(0===i)return vd.every(Math.max(vh(e,r,n),1));const[c,u]=a[o/a[i-1][2]<a[i][2]/o?i-1:i];return c.every(u)}return[function(t,e,r){const n=e<t;n&&([t,e]=[e,t]);const o=r&&"function"==typeof r.range?r:c(t,e,r),i=o?o.range(t,+e+1):[];return n?i.reverse():i},c]}Zd.every=t=>isFinite(t=Math.floor(t))&&t>0?dd(e=>{e.setUTCFullYear(Math.floor(e.getUTCFullYear()/t)*t),e.setUTCMonth(0,1),e.setUTCHours(0,0,0,0)},(e,r)=>{e.setUTCFullYear(e.getUTCFullYear()+r*t)}):null,Zd.range;const[Qd,tv]=Jd(Zd,Yd,$d,Td,Ed,Pd),[ev,rv]=Jd(Kd,Gd,Dd,Md,Ad,kd);function nv(t){if(0<=t.y&&t.y<100){var e=new Date(-1,t.m,t.d,t.H,t.M,t.S,t.L);return e.setFullYear(t.y),e}return new Date(t.y,t.m,t.d,t.H,t.M,t.S,t.L)}function ov(t){if(0<=t.y&&t.y<100){var e=new Date(Date.UTC(-1,t.m,t.d,t.H,t.M,t.S,t.L));return e.setUTCFullYear(t.y),e}return new Date(Date.UTC(t.y,t.m,t.d,t.H,t.M,t.S,t.L))}function iv(t,e,r){return{y:t,m:e,d:r,H:0,M:0,S:0,L:0}}var av,cv,uv,lv={"-":"",_:" ",0:"0"},sv=/^\s*\d+/,fv=/^%/,pv=/[\\^$*+?|[\]().{}]/g;function hv(t,e,r){var n=t<0?"-":"",o=(n?-t:t)+"",i=o.length;return n+(i<r?new Array(r-i+1).join(e)+o:o)}function yv(t){return t.replace(pv,"\\$&")}function dv(t){return new RegExp("^(?:"+t.map(yv).join("|")+")","i")}function vv(t){return new Map(t.map((t,e)=>[t.toLowerCase(),e]))}function mv(t,e,r){var n=sv.exec(e.slice(r,r+1));return n?(t.w=+n[0],r+n[0].length):-1}function bv(t,e,r){var n=sv.exec(e.slice(r,r+1));return n?(t.u=+n[0],r+n[0].length):-1}function gv(t,e,r){var n=sv.exec(e.slice(r,r+2));return n?(t.U=+n[0],r+n[0].length):-1}function wv(t,e,r){var n=sv.exec(e.slice(r,r+2));return n?(t.V=+n[0],r+n[0].length):-1}function xv(t,e,r){var n=sv.exec(e.slice(r,r+2));return n?(t.W=+n[0],r+n[0].length):-1}function Ov(t,e,r){var n=sv.exec(e.slice(r,r+4));return n?(t.y=+n[0],r+n[0].length):-1}function jv(t,e,r){var n=sv.exec(e.slice(r,r+2));return n?(t.y=+n[0]+(+n[0]>68?1900:2e3),r+n[0].length):-1}function Sv(t,e,r){var n=/^(Z)|([+-]\d\d)(?::?(\d\d))?/.exec(e.slice(r,r+6));return n?(t.Z=n[1]?0:-(n[2]+(n[3]||"00")),r+n[0].length):-1}function kv(t,e,r){var n=sv.exec(e.slice(r,r+1));return n?(t.q=3*n[0]-3,r+n[0].length):-1}function Pv(t,e,r){var n=sv.exec(e.slice(r,r+2));return n?(t.m=n[0]-1,r+n[0].length):-1}function Av(t,e,r){var n=sv.exec(e.slice(r,r+2));return n?(t.d=+n[0],r+n[0].length):-1}function Ev(t,e,r){var n=sv.exec(e.slice(r,r+3));return n?(t.m=0,t.d=+n[0],r+n[0].length):-1}function Mv(t,e,r){var n=sv.exec(e.slice(r,r+2));return n?(t.H=+n[0],r+n[0].length):-1}function _v(t,e,r){var n=sv.exec(e.slice(r,r+2));return n?(t.M=+n[0],r+n[0].length):-1}function Tv(t,e,r){var n=sv.exec(e.slice(r,r+2));return n?(t.S=+n[0],r+n[0].length):-1}function Cv(t,e,r){var n=sv.exec(e.slice(r,r+3));return n?(t.L=+n[0],r+n[0].length):-1}function Dv(t,e,r){var n=sv.exec(e.slice(r,r+6));return n?(t.L=Math.floor(n[0]/1e3),r+n[0].length):-1}function Iv(t,e,r){var n=fv.exec(e.slice(r,r+1));return n?r+n[0].length:-1}function Nv(t,e,r){var n=sv.exec(e.slice(r));return n?(t.Q=+n[0],r+n[0].length):-1}function Bv(t,e,r){var n=sv.exec(e.slice(r));return n?(t.s=+n[0],r+n[0].length):-1}function Lv(t,e){return hv(t.getDate(),e,2)}function Rv(t,e){return hv(t.getHours(),e,2)}function zv(t,e){return hv(t.getHours()%12||12,e,2)}function Uv(t,e){return hv(1+Md.count(Kd(t),t),e,3)}function $v(t,e){return hv(t.getMilliseconds(),e,3)}function qv(t,e){return $v(t,e)+"000"}function Fv(t,e){return hv(t.getMonth()+1,e,2)}function Wv(t,e){return hv(t.getMinutes(),e,2)}function Hv(t,e){return hv(t.getSeconds(),e,2)}function Vv(t){var e=t.getDay();return 0===e?7:e}function Xv(t,e){return hv(Dd.count(Kd(t)-1,t),e,2)}function Gv(t){var e=t.getDay();return e>=4||0===e?Ld(t):Ld.ceil(t)}function Yv(t,e){return t=Gv(t),hv(Ld.count(Kd(t),t)+(4===Kd(t).getDay()),e,2)}function Kv(t){return t.getDay()}function Zv(t,e){return hv(Id.count(Kd(t)-1,t),e,2)}function Jv(t,e){return hv(t.getFullYear()%100,e,2)}function Qv(t,e){return hv((t=Gv(t)).getFullYear()%100,e,2)}function tm(t,e){return hv(t.getFullYear()%1e4,e,4)}function em(t,e){var r=t.getDay();return hv((t=r>=4||0===r?Ld(t):Ld.ceil(t)).getFullYear()%1e4,e,4)}function rm(t){var e=t.getTimezoneOffset();return(e>0?"-":(e*=-1,"+"))+hv(e/60|0,"0",2)+hv(e%60,"0",2)}function nm(t,e){return hv(t.getUTCDate(),e,2)}function om(t,e){return hv(t.getUTCHours(),e,2)}function im(t,e){return hv(t.getUTCHours()%12||12,e,2)}function am(t,e){return hv(1+_d.count(Zd(t),t),e,3)}function cm(t,e){return hv(t.getUTCMilliseconds(),e,3)}function um(t,e){return cm(t,e)+"000"}function lm(t,e){return hv(t.getUTCMonth()+1,e,2)}function sm(t,e){return hv(t.getUTCMinutes(),e,2)}function fm(t,e){return hv(t.getUTCSeconds(),e,2)}function pm(t){var e=t.getUTCDay();return 0===e?7:e}function hm(t,e){return hv($d.count(Zd(t)-1,t),e,2)}function ym(t){var e=t.getUTCDay();return e>=4||0===e?Hd(t):Hd.ceil(t)}function dm(t,e){return t=ym(t),hv(Hd.count(Zd(t),t)+(4===Zd(t).getUTCDay()),e,2)}function vm(t){return t.getUTCDay()}function mm(t,e){return hv(qd.count(Zd(t)-1,t),e,2)}function bm(t,e){return hv(t.getUTCFullYear()%100,e,2)}function gm(t,e){return hv((t=ym(t)).getUTCFullYear()%100,e,2)}function wm(t,e){return hv(t.getUTCFullYear()%1e4,e,4)}function xm(t,e){var r=t.getUTCDay();return hv((t=r>=4||0===r?Hd(t):Hd.ceil(t)).getUTCFullYear()%1e4,e,4)}function Om(){return"+0000"}function jm(){return"%"}function Sm(t){return+t}function km(t){return Math.floor(+t/1e3)}function Pm(t){return new Date(t)}function Am(t){return t instanceof Date?+t:+new Date(+t)}function Em(t,e,r,n,o,i,a,c,u,l){var s=Ty(),f=s.invert,p=s.domain,h=l(".%L"),y=l(":%S"),d=l("%I:%M"),v=l("%I %p"),m=l("%a %d"),b=l("%b %d"),g=l("%B"),w=l("%Y");function x(t){return(u(t)<t?h:c(t)<t?y:a(t)<t?d:i(t)<t?v:n(t)<t?o(t)<t?m:b:r(t)<t?g:w)(t)}return s.invert=function(t){return new Date(f(t))},s.domain=function(t){return arguments.length?p(Array.from(t,Am)):p().map(Pm)},s.ticks=function(e){var r=p();return t(r[0],r[r.length-1],null==e?10:e)},s.tickFormat=function(t,e){return null==e?x:l(e)},s.nice=function(t){var r=p();return t&&"function"==typeof t.range||(t=e(r[0],r[r.length-1],null==t?10:t)),t?p(Ky(r,t)):s},s.copy=function(){return My(s,Em(t,e,r,n,o,i,a,c,u,l))},s}function Mm(){var t,e,r,n,o,i=0,a=1,c=ky,u=!1;function l(e){return null==e||isNaN(e=+e)?o:c(0===r?.5:(e=(n(e)-t)*r,u?Math.max(0,Math.min(1,e)):e))}function s(t){return function(e){var r,n;return arguments.length?([r,n]=e,c=t(r,n),l):[c(0),c(1)]}}return l.domain=function(o){return arguments.length?([i,a]=o,t=n(i=+i),e=n(a=+a),r=t===e?0:1/(e-t),l):[i,a]},l.clamp=function(t){return arguments.length?(u=!!t,l):u},l.interpolator=function(t){return arguments.length?(c=t,l):c},l.range=s(xy),l.rangeRound=s(Oy),l.unknown=function(t){return arguments.length?(o=t,l):o},function(o){return n=o,t=o(i),e=o(a),r=t===e?0:1/(e-t),l}}function _m(t,e){return e.domain(t.domain()).interpolator(t.interpolator()).clamp(t.clamp()).unknown(t.unknown())}function Tm(){var t=sd(Mm());return t.copy=function(){return _m(t,Tm()).exponent(t.exponent())},jh.apply(t,arguments)}function Cm(){var t,e,r,n,o,i,a,c=0,u=.5,l=1,s=1,f=ky,p=!1;function h(t){return isNaN(t=+t)?a:(t=.5+((t=+i(t))-e)*(s*t<s*e?n:o),f(p?Math.max(0,Math.min(1,t)):t))}function y(t){return function(e){var r,n,o;return arguments.length?([r,n,o]=e,f=function(t,e){void 0===e&&(e=t,t=xy);for(var r=0,n=e.length-1,o=e[0],i=new Array(n<0?0:n);r<n;)i[r]=t(o,o=e[++r]);return function(t){var e=Math.max(0,Math.min(n-1,Math.floor(t*=n)));return i[e](t-e)}}(t,[r,n,o]),h):[f(0),f(.5),f(1)]}}return h.domain=function(a){return arguments.length?([c,u,l]=a,t=i(c=+c),e=i(u=+u),r=i(l=+l),n=t===e?0:.5/(e-t),o=e===r?0:.5/(r-e),s=e<t?-1:1,h):[c,u,l]},h.clamp=function(t){return arguments.length?(p=!!t,h):p},h.interpolator=function(t){return arguments.length?(f=t,h):f},h.range=y(xy),h.rangeRound=y(Oy),h.unknown=function(t){return arguments.length?(a=t,h):a},function(a){return i=a,t=a(c),e=a(u),r=a(l),n=t===e?0:.5/(e-t),o=e===r?0:.5/(r-e),s=e<t?-1:1,h}}function Dm(){var t=sd(Cm());return t.copy=function(){return _m(t,Dm()).exponent(t.exponent())},jh.apply(t,arguments)}!function(t){av=function(t){var e=t.dateTime,r=t.date,n=t.time,o=t.periods,i=t.days,a=t.shortDays,c=t.months,u=t.shortMonths,l=dv(o),s=vv(o),f=dv(i),p=vv(i),h=dv(a),y=vv(a),d=dv(c),v=vv(c),m=dv(u),b=vv(u),g={a:function(t){return a[t.getDay()]},A:function(t){return i[t.getDay()]},b:function(t){return u[t.getMonth()]},B:function(t){return c[t.getMonth()]},c:null,d:Lv,e:Lv,f:qv,g:Qv,G:em,H:Rv,I:zv,j:Uv,L:$v,m:Fv,M:Wv,p:function(t){return o[+(t.getHours()>=12)]},q:function(t){return 1+~~(t.getMonth()/3)},Q:Sm,s:km,S:Hv,u:Vv,U:Xv,V:Yv,w:Kv,W:Zv,x:null,X:null,y:Jv,Y:tm,Z:rm,"%":jm},w={a:function(t){return a[t.getUTCDay()]},A:function(t){return i[t.getUTCDay()]},b:function(t){return u[t.getUTCMonth()]},B:function(t){return c[t.getUTCMonth()]},c:null,d:nm,e:nm,f:um,g:gm,G:xm,H:om,I:im,j:am,L:cm,m:lm,M:sm,p:function(t){return o[+(t.getUTCHours()>=12)]},q:function(t){return 1+~~(t.getUTCMonth()/3)},Q:Sm,s:km,S:fm,u:pm,U:hm,V:dm,w:vm,W:mm,x:null,X:null,y:bm,Y:wm,Z:Om,"%":jm},x={a:function(t,e,r){var n=h.exec(e.slice(r));return n?(t.w=y.get(n[0].toLowerCase()),r+n[0].length):-1},A:function(t,e,r){var n=f.exec(e.slice(r));return n?(t.w=p.get(n[0].toLowerCase()),r+n[0].length):-1},b:function(t,e,r){var n=m.exec(e.slice(r));return n?(t.m=b.get(n[0].toLowerCase()),r+n[0].length):-1},B:function(t,e,r){var n=d.exec(e.slice(r));return n?(t.m=v.get(n[0].toLowerCase()),r+n[0].length):-1},c:function(t,r,n){return S(t,e,r,n)},d:Av,e:Av,f:Dv,g:jv,G:Ov,H:Mv,I:Mv,j:Ev,L:Cv,m:Pv,M:_v,p:function(t,e,r){var n=l.exec(e.slice(r));return n?(t.p=s.get(n[0].toLowerCase()),r+n[0].length):-1},q:kv,Q:Nv,s:Bv,S:Tv,u:bv,U:gv,V:wv,w:mv,W:xv,x:function(t,e,n){return S(t,r,e,n)},X:function(t,e,r){return S(t,n,e,r)},y:jv,Y:Ov,Z:Sv,"%":Iv};function O(t,e){return function(r){var n,o,i,a=[],c=-1,u=0,l=t.length;for(r instanceof Date||(r=new Date(+r));++c<l;)37===t.charCodeAt(c)&&(a.push(t.slice(u,c)),null!=(o=lv[n=t.charAt(++c)])?n=t.charAt(++c):o="e"===n?" ":"0",(i=e[n])&&(n=i(r,o)),a.push(n),u=c+1);return a.push(t.slice(u,c)),a.join("")}}function j(t,e){return function(r){var n,o,i=iv(1900,void 0,1);if(S(i,t,r+="",0)!=r.length)return null;if("Q"in i)return new Date(i.Q);if("s"in i)return new Date(1e3*i.s+("L"in i?i.L:0));if(e&&!("Z"in i)&&(i.Z=0),"p"in i&&(i.H=i.H%12+12*i.p),void 0===i.m&&(i.m="q"in i?i.q:0),"V"in i){if(i.V<1||i.V>53)return null;"w"in i||(i.w=1),"Z"in i?(o=(n=ov(iv(i.y,0,1))).getUTCDay(),n=o>4||0===o?qd.ceil(n):qd(n),n=_d.offset(n,7*(i.V-1)),i.y=n.getUTCFullYear(),i.m=n.getUTCMonth(),i.d=n.getUTCDate()+(i.w+6)%7):(o=(n=nv(iv(i.y,0,1))).getDay(),n=o>4||0===o?Id.ceil(n):Id(n),n=Md.offset(n,7*(i.V-1)),i.y=n.getFullYear(),i.m=n.getMonth(),i.d=n.getDate()+(i.w+6)%7)}else("W"in i||"U"in i)&&("w"in i||(i.w="u"in i?i.u%7:"W"in i?1:0),o="Z"in i?ov(iv(i.y,0,1)).getUTCDay():nv(iv(i.y,0,1)).getDay(),i.m=0,i.d="W"in i?(i.w+6)%7+7*i.W-(o+5)%7:i.w+7*i.U-(o+6)%7);return"Z"in i?(i.H+=i.Z/100|0,i.M+=i.Z%100,ov(i)):nv(i)}}function S(t,e,r,n){for(var o,i,a=0,c=e.length,u=r.length;a<c;){if(n>=u)return-1;if(37===(o=e.charCodeAt(a++))){if(o=e.charAt(a++),!(i=x[o in lv?e.charAt(a++):o])||(n=i(t,r,n))<0)return-1}else if(o!=r.charCodeAt(n++))return-1}return n}return g.x=O(r,g),g.X=O(n,g),g.c=O(e,g),w.x=O(r,w),w.X=O(n,w),w.c=O(e,w),{format:function(t){var e=O(t+="",g);return e.toString=function(){return t},e},parse:function(t){var e=j(t+="",!1);return e.toString=function(){return t},e},utcFormat:function(t){var e=O(t+="",w);return e.toString=function(){return t},e},utcParse:function(t){var e=j(t+="",!0);return e.toString=function(){return t},e}}}(t),cv=av.format,av.parse,uv=av.utcFormat,av.utcParse}({dateTime:"%x, %X",date:"%-m/%-d/%Y",time:"%-I:%M:%S %p",periods:["AM","PM"],days:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"],shortDays:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],months:["January","February","March","April","May","June","July","August","September","October","November","December"],shortMonths:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"]});const Im=Object.freeze(Object.defineProperty({__proto__:null,scaleBand:Ph,scaleDiverging:function t(){var e=Gy(Cm()(ky));return e.copy=function(){return _m(e,t())},jh.apply(e,arguments)},scaleDivergingLog:function t(){var e=nd(Cm()).domain([.1,1,10]);return e.copy=function(){return _m(e,t()).base(e.base())},jh.apply(e,arguments)},scaleDivergingPow:Dm,scaleDivergingSqrt:function(){return Dm.apply(null,arguments).exponent(.5)},scaleDivergingSymlog:function t(){var e=ad(Cm());return e.copy=function(){return _m(e,t()).constant(e.constant())},jh.apply(e,arguments)},scaleIdentity:function t(e){var r;function n(t){return null==t||isNaN(t=+t)?r:t}return n.invert=n,n.domain=n.range=function(t){return arguments.length?(e=Array.from(t,jy),n):e.slice()},n.unknown=function(t){return arguments.length?(r=t,n):r},n.copy=function(){return t(e).unknown(r)},e=arguments.length?Array.from(e,jy):[0,1],Gy(n)},scaleImplicit:Sh,scaleLinear:Yy,scaleLog:function t(){const e=nd(_y()).domain([1,10]);return e.copy=()=>My(e,t()).base(e.base()),Oh.apply(e,arguments),e},scaleOrdinal:kh,scalePoint:Eh,scalePow:fd,scaleQuantile:function t(){var e,r=[],n=[],o=[];function i(){var t=0,e=Math.max(1,n.length);for(o=new Array(e-1);++t<e;)o[t-1]=xh(r,t/e);return a}function a(t){return null==t||isNaN(t=+t)?e:n[ih(o,t)]}return a.invertExtent=function(t){var e=n.indexOf(t);return e<0?[NaN,NaN]:[e>0?o[e-1]:r[0],e<o.length?o[e]:r[r.length-1]]},a.domain=function(t){if(!arguments.length)return r.slice();r=[];for(let e of t)null==e||isNaN(e=+e)||r.push(e);return r.sort(th),i()},a.range=function(t){return arguments.length?(n=Array.from(t),i()):n.slice()},a.unknown=function(t){return arguments.length?(e=t,a):e},a.quantiles=function(){return o.slice()},a.copy=function(){return t().domain(r).range(n).unknown(e)},Oh.apply(a,arguments)},scaleQuantize:function t(){var e,r=0,n=1,o=1,i=[.5],a=[0,1];function c(t){return null!=t&&t<=t?a[ih(i,t,0,o)]:e}function u(){var t=-1;for(i=new Array(o);++t<o;)i[t]=((t+1)*n-(t-o)*r)/(o+1);return c}return c.domain=function(t){return arguments.length?([r,n]=t,r=+r,n=+n,u()):[r,n]},c.range=function(t){return arguments.length?(o=(a=Array.from(t)).length-1,u()):a.slice()},c.invertExtent=function(t){var e=a.indexOf(t);return e<0?[NaN,NaN]:e<1?[r,i[0]]:e>=o?[i[o-1],n]:[i[e-1],i[e]]},c.unknown=function(t){return arguments.length?(e=t,c):c},c.thresholds=function(){return i.slice()},c.copy=function(){return t().domain([r,n]).range(a).unknown(e)},Oh.apply(Gy(c),arguments)},scaleRadial:function t(){var e,r=Ty(),n=[0,1],o=!1;function i(t){var n=function(t){return Math.sign(t)*Math.sqrt(Math.abs(t))}(r(t));return isNaN(n)?e:o?Math.round(n):n}return i.invert=function(t){return r.invert(pd(t))},i.domain=function(t){return arguments.length?(r.domain(t),i):r.domain()},i.range=function(t){return arguments.length?(r.range((n=Array.from(t,jy)).map(pd)),i):n.slice()},i.rangeRound=function(t){return i.range(t).round(!0)},i.round=function(t){return arguments.length?(o=!!t,i):o},i.clamp=function(t){return arguments.length?(r.clamp(t),i):r.clamp()},i.unknown=function(t){return arguments.length?(e=t,i):e},i.copy=function(){return t(r.domain(),n).round(o).clamp(r.clamp()).unknown(e)},Oh.apply(i,arguments),Gy(i)},scaleSequential:function t(){var e=Gy(Mm()(ky));return e.copy=function(){return _m(e,t())},jh.apply(e,arguments)},scaleSequentialLog:function t(){var e=nd(Mm()).domain([1,10]);return e.copy=function(){return _m(e,t()).base(e.base())},jh.apply(e,arguments)},scaleSequentialPow:Tm,scaleSequentialQuantile:function t(){var e=[],r=ky;function n(t){if(null!=t&&!isNaN(t=+t))return r((ih(e,t,1)-1)/(e.length-1))}return n.domain=function(t){if(!arguments.length)return e.slice();e=[];for(let r of t)null==r||isNaN(r=+r)||e.push(r);return e.sort(th),n},n.interpolator=function(t){return arguments.length?(r=t,n):r},n.range=function(){return e.map((t,n)=>r(n/(e.length-1)))},n.quantiles=function(t){return Array.from({length:t+1},(r,n)=>function(t,e){if((r=(t=Float64Array.from(function*(t){for(let e of t)null!=e&&(e=+e)>=e&&(yield e)}(t))).length)&&!isNaN(e=+e)){if(e<=0||r<2)return bh(t);if(e>=1)return mh(t);var r,n=(r-1)*e,o=Math.floor(n),i=mh(gh(t,o).subarray(0,o+1));return i+(bh(t.subarray(o+1))-i)*(n-o)}}(e,n/t))},n.copy=function(){return t(r).domain(e)},jh.apply(n,arguments)},scaleSequentialSqrt:function(){return Tm.apply(null,arguments).exponent(.5)},scaleSequentialSymlog:function t(){var e=ad(Mm());return e.copy=function(){return _m(e,t()).constant(e.constant())},jh.apply(e,arguments)},scaleSqrt:function(){return fd.apply(null,arguments).exponent(.5)},scaleSymlog:function t(){var e=ad(_y());return e.copy=function(){return My(e,t()).constant(e.constant())},Oh.apply(e,arguments)},scaleThreshold:function t(){var e,r=[.5],n=[0,1],o=1;function i(t){return null!=t&&t<=t?n[ih(r,t,0,o)]:e}return i.domain=function(t){return arguments.length?(r=Array.from(t),o=Math.min(r.length,n.length-1),i):r.slice()},i.range=function(t){return arguments.length?(n=Array.from(t),o=Math.min(r.length,n.length-1),i):n.slice()},i.invertExtent=function(t){var e=n.indexOf(t);return[r[e-1],r[e]]},i.unknown=function(t){return arguments.length?(e=t,i):e},i.copy=function(){return t().domain(r).range(n).unknown(e)},Oh.apply(i,arguments)},scaleTime:function(){return Oh.apply(Em(ev,rv,Kd,Gd,Dd,Md,Ad,kd,Sd,cv).domain([new Date(2e3,0,1),new Date(2e3,0,2)]),arguments)},scaleUtc:function(){return Oh.apply(Em(Qd,tv,Zd,Yd,$d,_d,Ed,Pd,Sd,uv).domain([Date.UTC(2e3,0,1),Date.UTC(2e3,0,2)]),arguments)},tickFormat:Xy},Symbol.toStringTag,{value:"Module"}));var Nm=Ht;var Bm=function(t,e,r){for(var n=-1,o=t.length;++n<o;){var i=t[n],a=e(i);if(null!=a&&(void 0===c?a==a&&!Nm(a):r(a,c)))var c=a,u=i}return u};var Lm=Bm,Rm=function(t,e){return t>e},zm=vl;const Um=r(function(t){return t&&t.length?Lm(t,zm,Rm):void 0});var $m=Bm,qm=function(t,e){return t<e},Fm=vl;const Wm=r(function(t){return t&&t.length?$m(t,Fm,qm):void 0});var Hm=Pr,Vm=Al,Xm=xs,Gm=kt;var Ym=hs,Km=function(t,e){return(Gm(t)?Hm:Xm)(t,Vm(e))};const Zm=r(function(t,e){return Ym(Km(t,e),1)});var Jm=Wu;const Qm=r(function(t,e){return Jm(t,e)});var tb,eb=1e9,rb=!0,nb="[DecimalError] ",ob=nb+"Invalid argument: ",ib=nb+"Exponent out of range: ",ab=Math.floor,cb=Math.pow,ub=/^(\d+(\.\d*)?|\.\d+)(e[+-]?\d+)?$/i,lb=1e7,sb=9007199254740991,fb=ab(1286742750677284.5),pb={};function hb(t,e){var r,n,o,i,a,c,u,l,s=t.constructor,f=s.precision;if(!t.s||!e.s)return e.s||(e=new s(t)),rb?jb(e,f):e;if(u=t.d,l=e.d,a=t.e,o=e.e,u=u.slice(),i=a-o){for(i<0?(n=u,i=-i,c=l.length):(n=l,o=a,c=u.length),i>(c=(a=Math.ceil(f/7))>c?a+1:c+1)&&(i=c,n.length=1),n.reverse();i--;)n.push(0);n.reverse()}for((c=u.length)-(i=l.length)<0&&(i=c,n=l,l=u,u=n),r=0;i;)r=(u[--i]=u[i]+l[i]+r)/lb|0,u[i]%=lb;for(r&&(u.unshift(r),++o),c=u.length;0==u[--c];)u.pop();return e.d=u,e.e=o,rb?jb(e,f):e}function yb(t,e,r){if(t!==~~t||t<e||t>r)throw Error(ob+t)}function db(t){var e,r,n,o=t.length-1,i="",a=t[0];if(o>0){for(i+=a,e=1;e<o;e++)(r=7-(n=t[e]+"").length)&&(i+=wb(r)),i+=n;(r=7-(n=(a=t[e])+"").length)&&(i+=wb(r))}else if(0===a)return"0";for(;a%10==0;)a/=10;return i+a}pb.absoluteValue=pb.abs=function(){var t=new this.constructor(this);return t.s&&(t.s=1),t},pb.comparedTo=pb.cmp=function(t){var e,r,n,o,i=this;if(t=new i.constructor(t),i.s!==t.s)return i.s||-t.s;if(i.e!==t.e)return i.e>t.e^i.s<0?1:-1;for(e=0,r=(n=i.d.length)<(o=t.d.length)?n:o;e<r;++e)if(i.d[e]!==t.d[e])return i.d[e]>t.d[e]^i.s<0?1:-1;return n===o?0:n>o^i.s<0?1:-1},pb.decimalPlaces=pb.dp=function(){var t=this,e=t.d.length-1,r=7*(e-t.e);if(e=t.d[e])for(;e%10==0;e/=10)r--;return r<0?0:r},pb.dividedBy=pb.div=function(t){return vb(this,new this.constructor(t))},pb.dividedToIntegerBy=pb.idiv=function(t){var e=this.constructor;return jb(vb(this,new e(t),0,1),e.precision)},pb.equals=pb.eq=function(t){return!this.cmp(t)},pb.exponent=function(){return bb(this)},pb.greaterThan=pb.gt=function(t){return this.cmp(t)>0},pb.greaterThanOrEqualTo=pb.gte=function(t){return this.cmp(t)>=0},pb.isInteger=pb.isint=function(){return this.e>this.d.length-2},pb.isNegative=pb.isneg=function(){return this.s<0},pb.isPositive=pb.ispos=function(){return this.s>0},pb.isZero=function(){return 0===this.s},pb.lessThan=pb.lt=function(t){return this.cmp(t)<0},pb.lessThanOrEqualTo=pb.lte=function(t){return this.cmp(t)<1},pb.logarithm=pb.log=function(t){var e,r=this,n=r.constructor,o=n.precision,i=o+5;if(void 0===t)t=new n(10);else if((t=new n(t)).s<1||t.eq(tb))throw Error(nb+"NaN");if(r.s<1)throw Error(nb+(r.s?"NaN":"-Infinity"));return r.eq(tb)?new n(0):(rb=!1,e=vb(xb(r,i),xb(t,i),i),rb=!0,jb(e,o))},pb.minus=pb.sub=function(t){var e=this;return t=new e.constructor(t),e.s==t.s?Sb(e,t):hb(e,(t.s=-t.s,t))},pb.modulo=pb.mod=function(t){var e,r=this,n=r.constructor,o=n.precision;if(!(t=new n(t)).s)throw Error(nb+"NaN");return r.s?(rb=!1,e=vb(r,t,0,1).times(t),rb=!0,r.minus(e)):jb(new n(r),o)},pb.naturalExponential=pb.exp=function(){return mb(this)},pb.naturalLogarithm=pb.ln=function(){return xb(this)},pb.negated=pb.neg=function(){var t=new this.constructor(this);return t.s=-t.s||0,t},pb.plus=pb.add=function(t){var e=this;return t=new e.constructor(t),e.s==t.s?hb(e,t):Sb(e,(t.s=-t.s,t))},pb.precision=pb.sd=function(t){var e,r,n,o=this;if(void 0!==t&&t!==!!t&&1!==t&&0!==t)throw Error(ob+t);if(e=bb(o)+1,r=7*(n=o.d.length-1)+1,n=o.d[n]){for(;n%10==0;n/=10)r--;for(n=o.d[0];n>=10;n/=10)r++}return t&&e>r?e:r},pb.squareRoot=pb.sqrt=function(){var t,e,r,n,o,i,a,c=this,u=c.constructor;if(c.s<1){if(!c.s)return new u(0);throw Error(nb+"NaN")}for(t=bb(c),rb=!1,0==(o=Math.sqrt(+c))||o==1/0?(((e=db(c.d)).length+t)%2==0&&(e+="0"),o=Math.sqrt(e),t=ab((t+1)/2)-(t<0||t%2),n=new u(e=o==1/0?"5e"+t:(e=o.toExponential()).slice(0,e.indexOf("e")+1)+t)):n=new u(o.toString()),o=a=(r=u.precision)+3;;)if(n=(i=n).plus(vb(c,i,a+2)).times(.5),db(i.d).slice(0,a)===(e=db(n.d)).slice(0,a)){if(e=e.slice(a-3,a+1),o==a&&"4999"==e){if(jb(i,r+1,0),i.times(i).eq(c)){n=i;break}}else if("9999"!=e)break;a+=4}return rb=!0,jb(n,r)},pb.times=pb.mul=function(t){var e,r,n,o,i,a,c,u,l,s=this,f=s.constructor,p=s.d,h=(t=new f(t)).d;if(!s.s||!t.s)return new f(0);for(t.s*=s.s,r=s.e+t.e,(u=p.length)<(l=h.length)&&(i=p,p=h,h=i,a=u,u=l,l=a),i=[],n=a=u+l;n--;)i.push(0);for(n=l;--n>=0;){for(e=0,o=u+n;o>n;)c=i[o]+h[n]*p[o-n-1]+e,i[o--]=c%lb|0,e=c/lb|0;i[o]=(i[o]+e)%lb|0}for(;!i[--a];)i.pop();return e?++r:i.shift(),t.d=i,t.e=r,rb?jb(t,f.precision):t},pb.toDecimalPlaces=pb.todp=function(t,e){var r=this,n=r.constructor;return r=new n(r),void 0===t?r:(yb(t,0,eb),void 0===e?e=n.rounding:yb(e,0,8),jb(r,t+bb(r)+1,e))},pb.toExponential=function(t,e){var r,n=this,o=n.constructor;return void 0===t?r=kb(n,!0):(yb(t,0,eb),void 0===e?e=o.rounding:yb(e,0,8),r=kb(n=jb(new o(n),t+1,e),!0,t+1)),r},pb.toFixed=function(t,e){var r,n,o=this,i=o.constructor;return void 0===t?kb(o):(yb(t,0,eb),void 0===e?e=i.rounding:yb(e,0,8),r=kb((n=jb(new i(o),t+bb(o)+1,e)).abs(),!1,t+bb(n)+1),o.isneg()&&!o.isZero()?"-"+r:r)},pb.toInteger=pb.toint=function(){var t=this,e=t.constructor;return jb(new e(t),bb(t)+1,e.rounding)},pb.toNumber=function(){return+this},pb.toPower=pb.pow=function(t){var e,r,n,o,i,a,c=this,u=c.constructor,l=+(t=new u(t));if(!t.s)return new u(tb);if(!(c=new u(c)).s){if(t.s<1)throw Error(nb+"Infinity");return c}if(c.eq(tb))return c;if(n=u.precision,t.eq(tb))return jb(c,n);if(a=(e=t.e)>=(r=t.d.length-1),i=c.s,a){if((r=l<0?-l:l)<=sb){for(o=new u(tb),e=Math.ceil(n/7+4),rb=!1;r%2&&Pb((o=o.times(c)).d,e),0!==(r=ab(r/2));)Pb((c=c.times(c)).d,e);return rb=!0,t.s<0?new u(tb).div(o):jb(o,n)}}else if(i<0)throw Error(nb+"NaN");return i=i<0&&1&t.d[Math.max(e,r)]?-1:1,c.s=1,rb=!1,o=t.times(xb(c,n+12)),rb=!0,(o=mb(o)).s=i,o},pb.toPrecision=function(t,e){var r,n,o=this,i=o.constructor;return void 0===t?n=kb(o,(r=bb(o))<=i.toExpNeg||r>=i.toExpPos):(yb(t,1,eb),void 0===e?e=i.rounding:yb(e,0,8),n=kb(o=jb(new i(o),t,e),t<=(r=bb(o))||r<=i.toExpNeg,t)),n},pb.toSignificantDigits=pb.tosd=function(t,e){var r=this.constructor;return void 0===t?(t=r.precision,e=r.rounding):(yb(t,1,eb),void 0===e?e=r.rounding:yb(e,0,8)),jb(new r(this),t,e)},pb.toString=pb.valueOf=pb.val=pb.toJSON=pb[Symbol.for("nodejs.util.inspect.custom")]=function(){var t=this,e=bb(t),r=t.constructor;return kb(t,e<=r.toExpNeg||e>=r.toExpPos)};var vb=function(){function t(t,e){var r,n=0,o=t.length;for(t=t.slice();o--;)r=t[o]*e+n,t[o]=r%lb|0,n=r/lb|0;return n&&t.unshift(n),t}function e(t,e,r,n){var o,i;if(r!=n)i=r>n?1:-1;else for(o=i=0;o<r;o++)if(t[o]!=e[o]){i=t[o]>e[o]?1:-1;break}return i}function r(t,e,r){for(var n=0;r--;)t[r]-=n,n=t[r]<e[r]?1:0,t[r]=n*lb+t[r]-e[r];for(;!t[0]&&t.length>1;)t.shift()}return function(n,o,i,a){var c,u,l,s,f,p,h,y,d,v,m,b,g,w,x,O,j,S,k=n.constructor,P=n.s==o.s?1:-1,A=n.d,E=o.d;if(!n.s)return new k(n);if(!o.s)throw Error(nb+"Division by zero");for(u=n.e-o.e,j=E.length,x=A.length,y=(h=new k(P)).d=[],l=0;E[l]==(A[l]||0);)++l;if(E[l]>(A[l]||0)&&--u,(b=null==i?i=k.precision:a?i+(bb(n)-bb(o))+1:i)<0)return new k(0);if(b=b/7+2|0,l=0,1==j)for(s=0,E=E[0],b++;(l<x||s)&&b--;l++)g=s*lb+(A[l]||0),y[l]=g/E|0,s=g%E|0;else{for((s=lb/(E[0]+1)|0)>1&&(E=t(E,s),A=t(A,s),j=E.length,x=A.length),w=j,v=(d=A.slice(0,j)).length;v<j;)d[v++]=0;(S=E.slice()).unshift(0),O=E[0],E[1]>=lb/2&&++O;do{s=0,(c=e(E,d,j,v))<0?(m=d[0],j!=v&&(m=m*lb+(d[1]||0)),(s=m/O|0)>1?(s>=lb&&(s=lb-1),1==(c=e(f=t(E,s),d,p=f.length,v=d.length))&&(s--,r(f,j<p?S:E,p))):(0==s&&(c=s=1),f=E.slice()),(p=f.length)<v&&f.unshift(0),r(d,f,v),-1==c&&(c=e(E,d,j,v=d.length))<1&&(s++,r(d,j<v?S:E,v)),v=d.length):0===c&&(s++,d=[0]),y[l++]=s,c&&d[0]?d[v++]=A[w]||0:(d=[A[w]],v=1)}while((w++<x||void 0!==d[0])&&b--)}return y[0]||y.shift(),h.e=u,jb(h,a?i+bb(h)+1:i)}}();function mb(t,e){var r,n,o,i,a,c=0,u=0,l=t.constructor,s=l.precision;if(bb(t)>16)throw Error(ib+bb(t));if(!t.s)return new l(tb);for(null==e?(rb=!1,a=s):a=e,i=new l(.03125);t.abs().gte(.1);)t=t.times(i),u+=5;for(a+=Math.log(cb(2,u))/Math.LN10*2+5|0,r=n=o=new l(tb),l.precision=a;;){if(n=jb(n.times(t),a),r=r.times(++c),db((i=o.plus(vb(n,r,a))).d).slice(0,a)===db(o.d).slice(0,a)){for(;u--;)o=jb(o.times(o),a);return l.precision=s,null==e?(rb=!0,jb(o,s)):o}o=i}}function bb(t){for(var e=7*t.e,r=t.d[0];r>=10;r/=10)e++;return e}function gb(t,e,r){if(e>t.LN10.sd())throw rb=!0,r&&(t.precision=r),Error(nb+"LN10 precision limit exceeded");return jb(new t(t.LN10),e)}function wb(t){for(var e="";t--;)e+="0";return e}function xb(t,e){var r,n,o,i,a,c,u,l,s,f=1,p=t,h=p.d,y=p.constructor,d=y.precision;if(p.s<1)throw Error(nb+(p.s?"NaN":"-Infinity"));if(p.eq(tb))return new y(0);if(null==e?(rb=!1,l=d):l=e,p.eq(10))return null==e&&(rb=!0),gb(y,l);if(l+=10,y.precision=l,n=(r=db(h)).charAt(0),i=bb(p),!(Math.abs(i)<15e14))return u=gb(y,l+2,d).times(i+""),p=xb(new y(n+"."+r.slice(1)),l-10).plus(u),y.precision=d,null==e?(rb=!0,jb(p,d)):p;for(;n<7&&1!=n||1==n&&r.charAt(1)>3;)n=(r=db((p=p.times(t)).d)).charAt(0),f++;for(i=bb(p),n>1?(p=new y("0."+r),i++):p=new y(n+"."+r.slice(1)),c=a=p=vb(p.minus(tb),p.plus(tb),l),s=jb(p.times(p),l),o=3;;){if(a=jb(a.times(s),l),db((u=c.plus(vb(a,new y(o),l))).d).slice(0,l)===db(c.d).slice(0,l))return c=c.times(2),0!==i&&(c=c.plus(gb(y,l+2,d).times(i+""))),c=vb(c,new y(f),l),y.precision=d,null==e?(rb=!0,jb(c,d)):c;c=u,o+=2}}function Ob(t,e){var r,n,o;for((r=e.indexOf("."))>-1&&(e=e.replace(".","")),(n=e.search(/e/i))>0?(r<0&&(r=n),r+=+e.slice(n+1),e=e.substring(0,n)):r<0&&(r=e.length),n=0;48===e.charCodeAt(n);)++n;for(o=e.length;48===e.charCodeAt(o-1);)--o;if(e=e.slice(n,o)){if(o-=n,r=r-n-1,t.e=ab(r/7),t.d=[],n=(r+1)%7,r<0&&(n+=7),n<o){for(n&&t.d.push(+e.slice(0,n)),o-=7;n<o;)t.d.push(+e.slice(n,n+=7));n=7-(e=e.slice(n)).length}else n-=o;for(;n--;)e+="0";if(t.d.push(+e),rb&&(t.e>fb||t.e<-fb))throw Error(ib+r)}else t.s=0,t.e=0,t.d=[0];return t}function jb(t,e,r){var n,o,i,a,c,u,l,s,f=t.d;for(a=1,i=f[0];i>=10;i/=10)a++;if((n=e-a)<0)n+=7,o=e,l=f[s=0];else{if((s=Math.ceil((n+1)/7))>=(i=f.length))return t;for(l=i=f[s],a=1;i>=10;i/=10)a++;o=(n%=7)-7+a}if(void 0!==r&&(c=l/(i=cb(10,a-o-1))%10|0,u=e<0||void 0!==f[s+1]||l%i,u=r<4?(c||u)&&(0==r||r==(t.s<0?3:2)):c>5||5==c&&(4==r||u||6==r&&(n>0?o>0?l/cb(10,a-o):0:f[s-1])%10&1||r==(t.s<0?8:7))),e<1||!f[0])return u?(i=bb(t),f.length=1,e=e-i-1,f[0]=cb(10,(7-e%7)%7),t.e=ab(-e/7)||0):(f.length=1,f[0]=t.e=t.s=0),t;if(0==n?(f.length=s,i=1,s--):(f.length=s+1,i=cb(10,7-n),f[s]=o>0?(l/cb(10,a-o)%cb(10,o)|0)*i:0),u)for(;;){if(0==s){(f[0]+=i)==lb&&(f[0]=1,++t.e);break}if(f[s]+=i,f[s]!=lb)break;f[s--]=0,i=1}for(n=f.length;0===f[--n];)f.pop();if(rb&&(t.e>fb||t.e<-fb))throw Error(ib+bb(t));return t}function Sb(t,e){var r,n,o,i,a,c,u,l,s,f,p=t.constructor,h=p.precision;if(!t.s||!e.s)return e.s?e.s=-e.s:e=new p(t),rb?jb(e,h):e;if(u=t.d,f=e.d,n=e.e,l=t.e,u=u.slice(),a=l-n){for((s=a<0)?(r=u,a=-a,c=f.length):(r=f,n=l,c=u.length),a>(o=Math.max(Math.ceil(h/7),c)+2)&&(a=o,r.length=1),r.reverse(),o=a;o--;)r.push(0);r.reverse()}else{for((s=(o=u.length)<(c=f.length))&&(c=o),o=0;o<c;o++)if(u[o]!=f[o]){s=u[o]<f[o];break}a=0}for(s&&(r=u,u=f,f=r,e.s=-e.s),c=u.length,o=f.length-c;o>0;--o)u[c++]=0;for(o=f.length;o>a;){if(u[--o]<f[o]){for(i=o;i&&0===u[--i];)u[i]=lb-1;--u[i],u[o]+=lb}u[o]-=f[o]}for(;0===u[--c];)u.pop();for(;0===u[0];u.shift())--n;return u[0]?(e.d=u,e.e=n,rb?jb(e,h):e):new p(0)}function kb(t,e,r){var n,o=bb(t),i=db(t.d),a=i.length;return e?(r&&(n=r-a)>0?i=i.charAt(0)+"."+i.slice(1)+wb(n):a>1&&(i=i.charAt(0)+"."+i.slice(1)),i=i+(o<0?"e":"e+")+o):o<0?(i="0."+wb(-o-1)+i,r&&(n=r-a)>0&&(i+=wb(n))):o>=a?(i+=wb(o+1-a),r&&(n=r-o-1)>0&&(i=i+"."+wb(n))):((n=o+1)<a&&(i=i.slice(0,n)+"."+i.slice(n)),r&&(n=r-a)>0&&(o+1===a&&(i+="."),i+=wb(n))),t.s<0?"-"+i:i}function Pb(t,e){if(t.length>e)return t.length=e,!0}function Ab(t){if(!t||"object"!=typeof t)throw Error(nb+"Object expected");var e,r,n,o=["precision",1,eb,"rounding",0,8,"toExpNeg",-1/0,0,"toExpPos",0,1/0];for(e=0;e<o.length;e+=3)if(void 0!==(n=t[r=o[e]])){if(!(ab(n)===n&&n>=o[e+1]&&n<=o[e+2]))throw Error(ob+r+": "+n);this[r]=n}if(void 0!==(n=t[r="LN10"])){if(n!=Math.LN10)throw Error(ob+r+": "+n);this[r]=new this(n)}return this}var Eb=function t(e){var r,n,o;function i(t){var e=this;if(!(e instanceof i))return new i(t);if(e.constructor=i,t instanceof i)return e.s=t.s,e.e=t.e,void(e.d=(t=t.d)?t.slice():t);if("number"==typeof t){if(0*t!=0)throw Error(ob+t);if(t>0)e.s=1;else{if(!(t<0))return e.s=0,e.e=0,void(e.d=[0]);t=-t,e.s=-1}return t===~~t&&t<1e7?(e.e=0,void(e.d=[t])):Ob(e,t.toString())}if("string"!=typeof t)throw Error(ob+t);if(45===t.charCodeAt(0)?(t=t.slice(1),e.s=-1):e.s=1,!ub.test(t))throw Error(ob+t);Ob(e,t)}if(i.prototype=pb,i.ROUND_UP=0,i.ROUND_DOWN=1,i.ROUND_CEIL=2,i.ROUND_FLOOR=3,i.ROUND_HALF_UP=4,i.ROUND_HALF_DOWN=5,i.ROUND_HALF_EVEN=6,i.ROUND_HALF_CEIL=7,i.ROUND_HALF_FLOOR=8,i.clone=t,i.config=i.set=Ab,void 0===e&&(e={}),e)for(o=["precision","rounding","toExpNeg","toExpPos","LN10"],r=0;r<o.length;)e.hasOwnProperty(n=o[r++])||(e[n]=this[n]);return i.config(e),i}({precision:20,rounding:4,toExpNeg:-7,toExpPos:21,LN10:"2.302585092994045684017991454684364207601101488628772976033327900967572609677352480235997205089598298341967784042286"});tb=new Eb(1);const Mb=Eb;function _b(t){return function(t){if(Array.isArray(t))return Tb(t)}(t)||function(t){if("undefined"!=typeof Symbol&&Symbol.iterator in Object(t))return Array.from(t)}(t)||function(t,e){if(!t)return;if("string"==typeof t)return Tb(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);"Object"===r&&t.constructor&&(r=t.constructor.name);if("Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return Tb(t,e)}(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Tb(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}var Cb=function(t){return t},Db={"@@functional/placeholder":!0},Ib=function(t){return t===Db},Nb=function(t){return function e(){return 0===arguments.length||1===arguments.length&&Ib(arguments.length<=0?void 0:arguments[0])?e:t.apply(void 0,arguments)}},Bb=function t(e,r){return 1===e?r:Nb(function(){for(var n=arguments.length,o=new Array(n),i=0;i<n;i++)o[i]=arguments[i];var a=o.filter(function(t){return t!==Db}).length;return a>=e?r.apply(void 0,o):t(e-a,Nb(function(){for(var t=arguments.length,e=new Array(t),n=0;n<t;n++)e[n]=arguments[n];var i=o.map(function(t){return Ib(t)?e.shift():t});return r.apply(void 0,_b(i).concat(e))}))})},Lb=function(t){return Bb(t.length,t)},Rb=function(t,e){for(var r=[],n=t;n<e;++n)r[n-t]=n;return r},zb=Lb(function(t,e){return Array.isArray(e)?e.map(t):Object.keys(e).map(function(t){return e[t]}).map(t)}),Ub=function(t){return Array.isArray(t)?t.reverse():t.split("").reverse.join("")},$b=function(t){var e=null,r=null;return function(){for(var n=arguments.length,o=new Array(n),i=0;i<n;i++)o[i]=arguments[i];return e&&o.every(function(t,r){return t===e[r]})?r:(e=o,r=t.apply(void 0,o))}};const qb={rangeStep:function(t,e,r){for(var n=new Mb(t),o=0,i=[];n.lt(e)&&o<1e5;)i.push(n.toNumber()),n=n.add(r),o++;return i},getDigitCount:function(t){return 0===t?1:Math.floor(new Mb(t).abs().log(10).toNumber())+1},interpolateNumber:Lb(function(t,e,r){var n=+t;return n+r*(+e-n)}),uninterpolateNumber:Lb(function(t,e,r){var n=e-+t;return(r-t)/(n=n||1/0)}),uninterpolateTruncation:Lb(function(t,e,r){var n=e-+t;return n=n||1/0,Math.max(0,Math.min(1,(r-t)/n))})};function Fb(t){return function(t){if(Array.isArray(t))return Vb(t)}(t)||function(t){if("undefined"!=typeof Symbol&&Symbol.iterator in Object(t))return Array.from(t)}(t)||Hb(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Wb(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){if("undefined"==typeof Symbol||!(Symbol.iterator in Object(t)))return;var r=[],n=!0,o=!1,i=void 0;try{for(var a,c=t[Symbol.iterator]();!(n=(a=c.next()).done)&&(r.push(a.value),!e||r.length!==e);n=!0);}catch(u){o=!0,i=u}finally{try{n||null==c.return||c.return()}finally{if(o)throw i}}return r}(t,e)||Hb(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Hb(t,e){if(t){if("string"==typeof t)return Vb(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);return"Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r?Array.from(t):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?Vb(t,e):void 0}}function Vb(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}function Xb(t){var e=Wb(t,2),r=e[0],n=e[1],o=r,i=n;return r>n&&(o=n,i=r),[o,i]}function Gb(t,e,r){if(t.lte(0))return new Mb(0);var n=qb.getDigitCount(t.toNumber()),o=new Mb(10).pow(n),i=t.div(o),a=1!==n?.05:.1,c=new Mb(Math.ceil(i.div(a).toNumber())).add(r).mul(a).mul(o);return e?c:new Mb(Math.ceil(c))}function Yb(t,e,r){var n=1,o=new Mb(t);if(!o.isint()&&r){var i=Math.abs(t);i<1?(n=new Mb(10).pow(qb.getDigitCount(t)-1),o=new Mb(Math.floor(o.div(n).toNumber())).mul(n)):i>1&&(o=new Mb(Math.floor(t)))}else 0===t?o=new Mb(Math.floor((e-1)/2)):r||(o=new Mb(Math.floor(t)));var a=Math.floor((e-1)/2),c=function(){for(var t=arguments.length,e=new Array(t),r=0;r<t;r++)e[r]=arguments[r];if(!e.length)return Cb;var n=e.reverse(),o=n[0],i=n.slice(1);return function(){return i.reduce(function(t,e){return e(t)},o.apply(void 0,arguments))}}(zb(function(t){return o.add(new Mb(t-a).mul(n)).toNumber()}),Rb);return c(0,e)}function Kb(t,e,r,n){var o=arguments.length>4&&void 0!==arguments[4]?arguments[4]:0;if(!Number.isFinite((e-t)/(r-1)))return{step:new Mb(0),tickMin:new Mb(0),tickMax:new Mb(0)};var i,a=Gb(new Mb(e).sub(t).div(r-1),n,o);i=t<=0&&e>=0?new Mb(0):(i=new Mb(t).add(e).div(2)).sub(new Mb(i).mod(a));var c=Math.ceil(i.sub(t).div(a).toNumber()),u=Math.ceil(new Mb(e).sub(i).div(a).toNumber()),l=c+u+1;return l>r?Kb(t,e,r,n,o+1):(l<r&&(u=e>0?u+(r-l):u,c=e>0?c:c+(r-l)),{step:a,tickMin:i.sub(new Mb(c).mul(a)),tickMax:i.add(new Mb(u).mul(a))})}var Zb=$b(function(t){var e=Wb(t,2),r=e[0],n=e[1],o=arguments.length>1&&void 0!==arguments[1]?arguments[1]:6,i=!(arguments.length>2&&void 0!==arguments[2])||arguments[2],a=Math.max(o,2),c=Wb(Xb([r,n]),2),u=c[0],l=c[1];if(u===-1/0||l===1/0){var s=l===1/0?[u].concat(Fb(Rb(0,o-1).map(function(){return 1/0}))):[].concat(Fb(Rb(0,o-1).map(function(){return-1/0})),[l]);return r>n?Ub(s):s}if(u===l)return Yb(u,o,i);var f=Kb(u,l,a,i),p=f.step,h=f.tickMin,y=f.tickMax,d=qb.rangeStep(h,y.add(new Mb(.1).mul(p)),p);return r>n?Ub(d):d}),Jb=$b(function(t,e){var r=Wb(t,2),n=r[0],o=r[1],i=!(arguments.length>2&&void 0!==arguments[2])||arguments[2],a=Wb(Xb([n,o]),2),c=a[0],u=a[1];if(c===-1/0||u===1/0)return[n,o];if(c===u)return[c];var l=Math.max(e,2),s=Gb(new Mb(u).sub(c).div(l-1),i,0),f=[].concat(Fb(qb.rangeStep(new Mb(c),new Mb(u).sub(new Mb(.99).mul(s)),s)),[u]);return n>o?Ub(f):f});function Qb(t,e){throw new Error("Invariant failed")}var tg=["offset","layout","width","dataKey","data","dataPointFormatter","xAxis","yAxis"];function eg(t){return(eg="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function rg(){return rg=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},rg.apply(this,arguments)}function ng(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,c=[],u=!0,l=!1;try{if(i=(r=r.call(t)).next,0===e);else for(;!(u=(n=i.call(r)).done)&&(c.push(n.value),c.length!==e);u=!0);}catch(s){l=!0,o=s}finally{try{if(!u&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(l)throw o}}return c}}(t,e)||function(t,e){if(!t)return;if("string"==typeof t)return og(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);"Object"===r&&t.constructor&&(r=t.constructor.name);if("Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return og(t,e)}(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function og(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}function ig(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],e.indexOf(r)>=0||Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}function ag(t,e,r){return e&&function(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,pg(n.key),n)}}(t.prototype,e),Object.defineProperty(t,"prototype",{writable:!1}),t}function cg(t,e,r){return e=lg(e),function(t,e){if(e&&("object"===eg(e)||"function"==typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(t,ug()?Reflect.construct(e,r||[],lg(t).constructor):e.apply(t,r))}function ug(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(e){}return(ug=function(){return!!t})()}function lg(t){return(lg=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function sg(t,e){return(sg=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function fg(t,e,r){return(e=pg(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function pg(t){var e=function(t,e){if("object"!=eg(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=eg(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==eg(e)?e:e+""}var hg=function(){function t(){return function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t),cg(this,t,arguments)}return function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&sg(t,e)}(t,n.Component),ag(t,[{key:"render",value:function(){var t=this.props,e=t.offset,r=t.layout,o=t.width,i=t.dataKey,a=t.data,c=t.dataPointFormatter,u=t.xAxis,l=t.yAxis,s=ig(t,tg),f=no(s,!1);"x"===this.props.direction&&"number"!==u.type&&Qb();var p=a.map(function(t){var a=c(t,i),s=a.x,p=a.y,h=a.value,y=a.errorVal;if(!y)return null;var d,v,m=[];if(Array.isArray(y)){var b=ng(y,2);d=b[0],v=b[1]}else d=v=y;if("vertical"===r){var g=u.scale,w=p+e,x=w+o,O=w-o,j=g(h-d),S=g(h+v);m.push({x1:S,y1:x,x2:S,y2:O}),m.push({x1:j,y1:w,x2:S,y2:w}),m.push({x1:j,y1:x,x2:j,y2:O})}else if("horizontal"===r){var k=l.scale,P=s+e,A=P-o,E=P+o,M=k(h-d),_=k(h+v);m.push({x1:A,y1:_,x2:E,y2:_}),m.push({x1:P,y1:M,x2:P,y2:_}),m.push({x1:A,y1:M,x2:E,y2:M})}return n.createElement(yo,rg({className:"recharts-errorBar",key:"bar-".concat(m.map(function(t){return"".concat(t.x1,"-").concat(t.x2,"-").concat(t.y1,"-").concat(t.y2)}))},f),m.map(function(t){return n.createElement("line",rg({},t,{key:"line-".concat(t.x1,"-").concat(t.x2,"-").concat(t.y1,"-").concat(t.y2)}))}))});return n.createElement(yo,{className:"recharts-errorBars"},p)}}])}();function yg(t){return(yg="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function dg(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function vg(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?dg(Object(r),!0).forEach(function(e){mg(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):dg(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function mg(t,e,r){var n;return n=function(t,e){if("object"!=yg(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=yg(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(e,"string"),(e="symbol"==yg(n)?n:n+"")in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}fg(hg,"defaultProps",{stroke:"black",strokeWidth:1.5,width:5,offset:0,layout:"horizontal"}),fg(hg,"displayName","ErrorBar");var bg=function(t){var e=t.children,r=t.formattedGraphicalItems,n=t.legendWidth,o=t.legendContent,i=to(e,cs);if(!i)return null;var a,c=cs.defaultProps,u=void 0!==c?vg(vg({},c),i.props):{};return a=i.props&&i.props.payload?i.props&&i.props.payload:"children"===o?(r||[]).reduce(function(t,e){var r=e.item,n=e.props,o=n.sectors||n.data||[];return t.concat(o.map(function(t){return{type:i.props.iconType||r.props.legendType,value:t.name,color:t.fill,payload:t}}))},[]):(r||[]).map(function(t){var e=t.item,r=e.type.defaultProps,n=void 0!==r?vg(vg({},r),e.props):{},o=n.dataKey,i=n.name,a=n.legendType;return{inactive:n.hide,dataKey:o,type:u.iconType||a||"square",color:Ag(e),value:i||o,payload:n}}),vg(vg(vg({},u),cs.getWithHeight(i,n)),{},{payload:a,item:i})};function gg(t){return(gg="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function wg(t){return function(t){if(Array.isArray(t))return xg(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||function(t,e){if(!t)return;if("string"==typeof t)return xg(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);"Object"===r&&t.constructor&&(r=t.constructor.name);if("Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return xg(t,e)}(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function xg(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}function Og(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function jg(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?Og(Object(r),!0).forEach(function(e){Sg(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):Og(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function Sg(t,e,r){var n;return n=function(t,e){if("object"!=gg(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=gg(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(e,"string"),(e="symbol"==gg(n)?n:n+"")in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function kg(t,e,r){return Gr(t)||Gr(e)?r:En(e)?Xr(t,e,r):re(e)?e(t):r}function Pg(t,e,r,n){var o=Zm(t,function(t){return kg(t,e)});if("number"===r){var i=o.filter(function(t){return An(t)||parseFloat(t)});return i.length?[Wm(i),Um(i)]:[1/0,-1/0]}return(n?o.filter(function(t){return!Gr(t)}):o).map(function(t){return En(t)||t instanceof Date?t:""})}var Ag=function(t){var e,r,n=t.type.displayName,o=null!==(e=t.type)&&void 0!==e&&e.defaultProps?jg(jg({},t.type.defaultProps),t.props):t.props,i=o.stroke,a=o.fill;switch(n){case"Line":r=i;break;case"Area":case"Radar":r=i&&"none"!==i?i:a;break;default:r=a}return r},Eg=function(t,e,r,n,o){var i=Qn(e.props.children,hg).filter(function(t){return function(t,e,r){return!!Gr(e)||("horizontal"===t?"yAxis"===e:"vertical"===t||"x"===r?"xAxis"===e:"y"!==r||"yAxis"===e)}(n,o,t.props.direction)});if(i&&i.length){var a=i.map(function(t){return t.props.dataKey});return t.reduce(function(t,e){var n=kg(e,r);if(Gr(n))return t;var o=Array.isArray(n)?[Wm(n),Um(n)]:[n,n],i=a.reduce(function(t,r){var n=kg(e,r,0),i=o[0]-Math.abs(Array.isArray(n)?n[0]:n),a=o[1]+Math.abs(Array.isArray(n)?n[1]:n);return[Math.min(i,t[0]),Math.max(a,t[1])]},[1/0,-1/0]);return[Math.min(i[0],t[0]),Math.max(i[1],t[1])]},[1/0,-1/0])}return null},Mg=function(t,e,r,n,o){var i=e.map(function(e){var i=e.props.dataKey;return"number"===r&&i&&Eg(t,e,i,n)||Pg(t,i,r,o)});if("number"===r)return i.reduce(function(t,e){return[Math.min(t[0],e[0]),Math.max(t[1],e[1])]},[1/0,-1/0]);var a={};return i.reduce(function(t,e){for(var r=0,n=e.length;r<n;r++)a[e[r]]||(a[e[r]]=!0,t.push(e[r]));return t},[])},_g=function(t,e){return"horizontal"===t&&"xAxis"===e||"vertical"===t&&"yAxis"===e||"centric"===t&&"angleAxis"===e||"radial"===t&&"radiusAxis"===e},Tg=function(t,e,r,n){if(n)return t.map(function(t){return t.coordinate});var o,i,a=t.map(function(t){return t.coordinate===e&&(o=!0),t.coordinate===r&&(i=!0),t.coordinate});return o||a.push(e),i||a.push(r),a},Cg=function(t,e,r){if(!t)return null;var n=t.scale,o=t.duplicateDomain,i=t.type,a=t.range,c="scaleBand"===t.realScaleType?n.bandwidth()/2:2,u=(e||r)&&"category"===i&&n.bandwidth?n.bandwidth()/c:0;return u="angleAxis"===t.axisType&&(null==a?void 0:a.length)>=2?2*kn(a[0]-a[1])*u:u,e&&(t.ticks||t.niceTicks)?(t.ticks||t.niceTicks).map(function(t){var e=o?o.indexOf(t):t;return{coordinate:n(e)+u,value:t,offset:u}}).filter(function(t){return!Sn(t.coordinate)}):t.isCategorical&&t.categoricalDomain?t.categoricalDomain.map(function(t,e){return{coordinate:n(t)+u,value:t,index:e,offset:u}}):n.ticks&&!r?n.ticks(t.tickCount).map(function(t){return{coordinate:n(t)+u,value:t,offset:u}}):n.domain().map(function(t,e){return{coordinate:n(t)+u,value:o?o[t]:t,index:e,offset:u}})},Dg=new WeakMap,Ig=function(t,e){if("function"!=typeof e)return t;Dg.has(t)||Dg.set(t,new WeakMap);var r=Dg.get(t);if(r.has(e))return r.get(e);var n=function(){t.apply(void 0,arguments),e.apply(void 0,arguments)};return r.set(e,n),n},Ng=1e-4,Bg={sign:function(t){var e=t.length;if(!(e<=0))for(var r=0,n=t[0].length;r<n;++r)for(var o=0,i=0,a=0;a<e;++a){var c=Sn(t[a][r][1])?t[a][r][0]:t[a][r][1];c>=0?(t[a][r][0]=o,t[a][r][1]=o+c,o=t[a][r][1]):(t[a][r][0]=i,t[a][r][1]=i+c,i=t[a][r][1])}},expand:function(t,e){if((n=t.length)>0){for(var r,n,o,i=0,a=t[0].length;i<a;++i){for(o=r=0;r<n;++r)o+=t[r][i][1]||0;if(o)for(r=0;r<n;++r)t[r][i][1]/=o}qi(t,e)}},none:qi,silhouette:function(t,e){if((r=t.length)>0){for(var r,n=0,o=t[e[0]],i=o.length;n<i;++n){for(var a=0,c=0;a<r;++a)c+=t[a][n][1]||0;o[n][1]+=o[n][0]=-c/2}qi(t,e)}},wiggle:function(t,e){if((o=t.length)>0&&(n=(r=t[e[0]]).length)>0){for(var r,n,o,i=0,a=1;a<n;++a){for(var c=0,u=0,l=0;c<o;++c){for(var s=t[e[c]],f=s[a][1]||0,p=(f-(s[a-1][1]||0))/2,h=0;h<c;++h){var y=t[e[h]];p+=(y[a][1]||0)-(y[a-1][1]||0)}u+=f,l+=p*f}r[a-1][1]+=r[a-1][0]=i,u&&(i-=l/u)}r[a-1][1]+=r[a-1][0]=i,qi(t,e)}},positive:function(t){var e=t.length;if(!(e<=0))for(var r=0,n=t[0].length;r<n;++r)for(var o=0,i=0;i<e;++i){var a=Sn(t[i][r][1])?t[i][r][0]:t[i][r][1];a>=0?(t[i][r][0]=o,t[i][r][1]=o+a,o=t[i][r][1]):(t[i][r][0]=0,t[i][r][1]=0)}}},Lg=function(t,e,r){var n=e.map(function(t){return t.props.dataKey}),o=Bg[r],i=function(){var t=qo([]),e=Fi,r=qi,n=Wi;function o(o){var i,a,c=Array.from(t.apply(this,arguments),Hi),u=c.length,l=-1;for(const t of o)for(i=0,++l;i<u;++i)(c[i][l]=[0,+n(t,c[i].key,l,o)]).data=t;for(i=0,a=ei(e(c));i<u;++i)c[a[i]].index=i;return r(c,a),c}return o.keys=function(e){return arguments.length?(t="function"==typeof e?e:qo(Array.from(e)),o):t},o.value=function(t){return arguments.length?(n="function"==typeof t?t:qo(+t),o):n},o.order=function(t){return arguments.length?(e=null==t?Fi:"function"==typeof t?t:qo(Array.from(t)),o):e},o.offset=function(t){return arguments.length?(r=null==t?qi:t,o):r},o}().keys(n).value(function(t,e){return+kg(t,e,0)}).order(Fi).offset(o);return i(t)};function Rg(t){var e=t.axis,r=t.ticks,n=t.bandSize,o=t.entry,i=t.index,a=t.dataKey;if("category"===e.type){if(!e.allowDuplicatedCategory&&e.dataKey&&!Gr(o[e.dataKey])){var c=In(r,"value",o[e.dataKey]);if(c)return c.coordinate+n/2}return r[i]?r[i].coordinate+n/2:null}var u=kg(o,Gr(a)?e.dataKey:a);return Gr(u)?null:e.scale(u)}var zg=function(t){var e=t.axis,r=t.ticks,n=t.offset,o=t.bandSize,i=t.entry,a=t.index;if("category"===e.type)return r[a]?r[a].coordinate+n:null;var c=kg(i,e.dataKey,e.domain[a]);return Gr(c)?null:e.scale(c)-o/2+n},Ug=function(t,e,r){return Object.keys(t).reduce(function(n,o){var i=t[o].stackedData.reduce(function(t,n){var o=n.slice(e,r+1).reduce(function(t,e){return[Wm(e.concat([t[0]]).filter(An)),Um(e.concat([t[1]]).filter(An))]},[1/0,-1/0]);return[Math.min(t[0],o[0]),Math.max(t[1],o[1])]},[1/0,-1/0]);return[Math.min(i[0],n[0]),Math.max(i[1],n[1])]},[1/0,-1/0]).map(function(t){return t===1/0||t===-1/0?0:t})},$g=/^dataMin[\s]*-[\s]*([0-9]+([.]{1}[0-9]+){0,1})$/,qg=/^dataMax[\s]*\+[\s]*([0-9]+([.]{1}[0-9]+){0,1})$/,Fg=function(t,e,r){if(re(t))return t(e,r);if(!Array.isArray(t))return e;var n=[];if(An(t[0]))n[0]=r?t[0]:Math.min(t[0],e[0]);else if($g.test(t[0])){var o=+$g.exec(t[0])[1];n[0]=e[0]-o}else re(t[0])?n[0]=t[0](e[0]):n[0]=e[0];if(An(t[1]))n[1]=r?t[1]:Math.max(t[1],e[1]);else if(qg.test(t[1])){var i=+qg.exec(t[1])[1];n[1]=e[1]+i}else re(t[1])?n[1]=t[1](e[1]):n[1]=e[1];return n},Wg=function(t,e,r){if(t&&t.scale&&t.scale.bandwidth){var n=t.scale.bandwidth();if(!r||n>0)return n}if(t&&e&&e.length>=2){for(var o=rf(e,function(t){return t.coordinate}),i=1/0,a=1,c=o.length;a<c;a++){var u=o[a],l=o[a-1];i=Math.min((u.coordinate||0)-(l.coordinate||0),i)}return i===1/0?0:i}return r?void 0:0},Hg=function(t,e,r){return t&&t.length?Qm(t,Xr(r,"type.defaultProps.domain"))?e:t:e},Vg=function(t,e){var r=t.type.defaultProps?jg(jg({},t.type.defaultProps),t.props):t.props,n=r.dataKey,o=r.name,i=r.unit,a=r.formatter,c=r.tooltipType,u=r.chartType,l=r.hide;return jg(jg({},no(t,!1)),{},{dataKey:n,unit:i,formatter:a,name:o||n,color:Ag(t),value:kg(e,n),type:c,payload:e,chartType:u,hide:l})};function Xg(t){return(Xg="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function Gg(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function Yg(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?Gg(Object(r),!0).forEach(function(e){Kg(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):Gg(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function Kg(t,e,r){var n;return n=function(t,e){if("object"!=Xg(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=Xg(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(e,"string"),(e="symbol"==Xg(n)?n:n+"")in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}var Zg=Math.PI/180,Jg=function(t){return 180*t/Math.PI},Qg=function(t,e,r,n){return{x:t+Math.cos(-Zg*n)*r,y:e+Math.sin(-Zg*n)*r}},tw=function(t,e){var r=t.x,n=t.y,o=e.cx,i=e.cy,a=function(t,e){var r=t.x,n=t.y,o=e.x,i=e.y;return Math.sqrt(Math.pow(r-o,2)+Math.pow(n-i,2))}({x:r,y:n},{x:o,y:i});if(a<=0)return{radius:a};var c=(r-o)/a,u=Math.acos(c);return n>i&&(u=2*Math.PI-u),{radius:a,angle:Jg(u),angleInRadian:u}},ew=function(t,e){var r=e.startAngle,n=e.endAngle,o=Math.floor(r/360),i=Math.floor(n/360);return t+360*Math.min(o,i)},rw=function(t,e){var r=t.x,n=t.y,o=tw({x:r,y:n},e),i=o.radius,a=o.angle,c=e.innerRadius,u=e.outerRadius;if(i<c||i>u)return!1;if(0===i)return!0;var l,s=function(t){var e=t.startAngle,r=t.endAngle,n=Math.floor(e/360),o=Math.floor(r/360),i=Math.min(n,o);return{startAngle:e-360*i,endAngle:r-360*i}}(e),f=s.startAngle,p=s.endAngle,h=a;if(f<=p){for(;h>p;)h-=360;for(;h<f;)h+=360;l=h>=f&&h<=p}else{for(;h>f;)h-=360;for(;h<p;)h+=360;l=h>=p&&h<=f}return l?Yg(Yg({},e),{},{radius:i,angle:ew(h,e)}):null};function nw(t){return(nw="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}var ow=["offset"];function iw(t){return function(t){if(Array.isArray(t))return aw(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||function(t,e){if(!t)return;if("string"==typeof t)return aw(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);"Object"===r&&t.constructor&&(r=t.constructor.name);if("Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return aw(t,e)}(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function aw(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}function cw(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],e.indexOf(r)>=0||Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}function uw(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function lw(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?uw(Object(r),!0).forEach(function(e){sw(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):uw(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function sw(t,e,r){var n;return n=function(t,e){if("object"!=nw(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=nw(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(e,"string"),(e="symbol"==nw(n)?n:n+"")in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function fw(){return fw=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},fw.apply(this,arguments)}var pw=function(t,e,r){var o,i,a=t.position,c=t.viewBox,u=t.offset,l=t.className,s=c,f=s.cx,p=s.cy,h=s.innerRadius,y=s.outerRadius,d=s.startAngle,v=s.endAngle,m=s.clockWise,b=(h+y)/2,g=function(t,e){return kn(e-t)*Math.min(Math.abs(e-t),360)}(d,v),w=g>=0?1:-1;"insideStart"===a?(o=d+w*u,i=m):"insideEnd"===a?(o=v-w*u,i=!m):"end"===a&&(o=v+w*u,i=m),i=g<=0?i:!i;var x=Qg(f,p,b,o),O=Qg(f,p,b,o+359*(i?1:-1)),j="M".concat(x.x,",").concat(x.y,"\n    A").concat(b,",").concat(b,",0,1,").concat(i?0:1,",\n    ").concat(O.x,",").concat(O.y),S=Gr(t.id)?_n("recharts-radial-line-"):t.id;return n.createElement("text",fw({},r,{dominantBaseline:"central",className:St("recharts-radial-bar-label",l)}),n.createElement("defs",null,n.createElement("path",{id:S,d:j})),n.createElement("textPath",{xlinkHref:"#".concat(S)},e))};function hw(e){var r,o=e.offset,i=lw({offset:void 0===o?5:o},cw(e,ow)),a=i.viewBox,c=i.position,u=i.value,l=i.children,s=i.content,f=i.className,p=void 0===f?"":f,h=i.textBreakAll;if(!a||Gr(u)&&Gr(l)&&!t.isValidElement(s)&&!re(s))return null;if(t.isValidElement(s))return t.cloneElement(s,i);if(re(s)){if(r=t.createElement(s,i),t.isValidElement(r))return r}else r=function(t){var e=t.value,r=t.formatter,n=Gr(t.children)?e:t.children;return re(r)?r(n):n}(i);var y=function(t){return"cx"in t&&An(t.cx)}(a),d=no(i,!0);if(y&&("insideStart"===c||"insideEnd"===c||"end"===c))return pw(i,r,d);var v=y?function(t){var e=t.viewBox,r=t.offset,n=t.position,o=e,i=o.cx,a=o.cy,c=o.innerRadius,u=o.outerRadius,l=(o.startAngle+o.endAngle)/2;if("outside"===n){var s=Qg(i,a,u+r,l),f=s.x;return{x:f,y:s.y,textAnchor:f>=i?"start":"end",verticalAnchor:"middle"}}if("center"===n)return{x:i,y:a,textAnchor:"middle",verticalAnchor:"middle"};if("centerTop"===n)return{x:i,y:a,textAnchor:"middle",verticalAnchor:"start"};if("centerBottom"===n)return{x:i,y:a,textAnchor:"middle",verticalAnchor:"end"};var p=Qg(i,a,(c+u)/2,l);return{x:p.x,y:p.y,textAnchor:"middle",verticalAnchor:"middle"}}(i):function(t){var e=t.viewBox,r=t.parentViewBox,n=t.offset,o=t.position,i=e,a=i.x,c=i.y,u=i.width,l=i.height,s=l>=0?1:-1,f=s*n,p=s>0?"end":"start",h=s>0?"start":"end",y=u>=0?1:-1,d=y*n,v=y>0?"end":"start",m=y>0?"start":"end";if("top"===o)return lw(lw({},{x:a+u/2,y:c-s*n,textAnchor:"middle",verticalAnchor:p}),r?{height:Math.max(c-r.y,0),width:u}:{});if("bottom"===o)return lw(lw({},{x:a+u/2,y:c+l+f,textAnchor:"middle",verticalAnchor:h}),r?{height:Math.max(r.y+r.height-(c+l),0),width:u}:{});if("left"===o){var b={x:a-d,y:c+l/2,textAnchor:v,verticalAnchor:"middle"};return lw(lw({},b),r?{width:Math.max(b.x-r.x,0),height:l}:{})}if("right"===o){var g={x:a+u+d,y:c+l/2,textAnchor:m,verticalAnchor:"middle"};return lw(lw({},g),r?{width:Math.max(r.x+r.width-g.x,0),height:l}:{})}var w=r?{width:u,height:l}:{};return"insideLeft"===o?lw({x:a+d,y:c+l/2,textAnchor:m,verticalAnchor:"middle"},w):"insideRight"===o?lw({x:a+u-d,y:c+l/2,textAnchor:v,verticalAnchor:"middle"},w):"insideTop"===o?lw({x:a+u/2,y:c+f,textAnchor:"middle",verticalAnchor:h},w):"insideBottom"===o?lw({x:a+u/2,y:c+l-f,textAnchor:"middle",verticalAnchor:p},w):"insideTopLeft"===o?lw({x:a+d,y:c+f,textAnchor:m,verticalAnchor:h},w):"insideTopRight"===o?lw({x:a+u-d,y:c+f,textAnchor:v,verticalAnchor:h},w):"insideBottomLeft"===o?lw({x:a+d,y:c+l-f,textAnchor:m,verticalAnchor:p},w):"insideBottomRight"===o?lw({x:a+u-d,y:c+l-f,textAnchor:v,verticalAnchor:p},w):Jt(o)&&(An(o.x)||Pn(o.x))&&(An(o.y)||Pn(o.y))?lw({x:a+Tn(o.x,u),y:c+Tn(o.y,l),textAnchor:"end",verticalAnchor:"end"},w):lw({x:a+u/2,y:c+l/2,textAnchor:"middle",verticalAnchor:"middle"},w)}(i);return n.createElement(Qp,fw({className:St("recharts-label",p)},d,v,{breakAll:h}),r)}hw.displayName="Label";var yw=function(t){var e=t.cx,r=t.cy,n=t.angle,o=t.startAngle,i=t.endAngle,a=t.r,c=t.radius,u=t.innerRadius,l=t.outerRadius,s=t.x,f=t.y,p=t.top,h=t.left,y=t.width,d=t.height,v=t.clockWise,m=t.labelViewBox;if(m)return m;if(An(y)&&An(d)){if(An(s)&&An(f))return{x:s,y:f,width:y,height:d};if(An(p)&&An(h))return{x:p,y:h,width:y,height:d}}return An(s)&&An(f)?{x:s,y:f,width:0,height:0}:An(e)&&An(r)?{cx:e,cy:r,startAngle:o||n||0,endAngle:i||n||0,innerRadius:u||0,outerRadius:l||c||a||0,clockWise:v}:t.viewBox?t.viewBox:{}};hw.parseViewBox=yw,hw.renderCallByParent=function(e,r){var o=!(arguments.length>2&&void 0!==arguments[2])||arguments[2];if(!e||!e.children&&o&&!e.label)return null;var i=e.children,a=yw(e),c=Qn(i,hw).map(function(e,n){return t.cloneElement(e,{viewBox:r||a,key:"label-".concat(n)})});if(!o)return c;var u=function(e,r){return e?!0===e?n.createElement(hw,{key:"label-implicit",viewBox:r}):En(e)?n.createElement(hw,{key:"label-implicit",viewBox:r,value:e}):t.isValidElement(e)?e.type===hw?t.cloneElement(e,{key:"label-implicit",viewBox:r}):n.createElement(hw,{key:"label-implicit",content:e,viewBox:r}):re(e)?n.createElement(hw,{key:"label-implicit",content:e,viewBox:r}):Jt(e)?n.createElement(hw,fw({viewBox:r},e,{key:"label-implicit"})):null:null}(e.label,r||a);return[u].concat(iw(c))};const dw=r(function(t){var e=null==t?0:t.length;return e?t[e-1]:void 0});function vw(t){return(vw="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}var mw=["valueAccessor"],bw=["data","dataKey","clockWise","id","textBreakAll"];function gw(t){return function(t){if(Array.isArray(t))return ww(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||function(t,e){if(!t)return;if("string"==typeof t)return ww(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);"Object"===r&&t.constructor&&(r=t.constructor.name);if("Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return ww(t,e)}(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function ww(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}function xw(){return xw=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},xw.apply(this,arguments)}function Ow(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function jw(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?Ow(Object(r),!0).forEach(function(e){Sw(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):Ow(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function Sw(t,e,r){var n;return n=function(t,e){if("object"!=vw(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=vw(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(e,"string"),(e="symbol"==vw(n)?n:n+"")in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function kw(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],e.indexOf(r)>=0||Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}var Pw=function(t){return Array.isArray(t.value)?dw(t.value):t.value};function Aw(t){var e=t.valueAccessor,r=void 0===e?Pw:e,o=kw(t,mw),i=o.data,a=o.dataKey,c=o.clockWise,u=o.id,l=o.textBreakAll,s=kw(o,bw);return i&&i.length?n.createElement(yo,{className:"recharts-label-list"},i.map(function(t,e){var o=Gr(a)?r(t,e):kg(t&&t.payload,a),i=Gr(u)?{}:{id:"".concat(u,"-").concat(e)};return n.createElement(hw,xw({},no(t,!0),s,i,{parentViewBox:t.parentViewBox,value:o,textBreakAll:l,viewBox:hw.parseViewBox(Gr(c)?t:jw(jw({},t),{},{clockWise:c})),key:"label-".concat(e),index:e}))})):null}function Ew(t){return(Ew="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function Mw(){return Mw=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},Mw.apply(this,arguments)}function _w(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function Tw(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?_w(Object(r),!0).forEach(function(e){Cw(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):_w(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function Cw(t,e,r){var n;return n=function(t,e){if("object"!=Ew(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=Ew(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(e,"string"),(e="symbol"==Ew(n)?n:n+"")in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}Aw.displayName="LabelList",Aw.renderCallByParent=function(e,r){var o=!(arguments.length>2&&void 0!==arguments[2])||arguments[2];if(!e||!e.children&&o&&!e.label)return null;var i=Qn(e.children,Aw).map(function(e,n){return t.cloneElement(e,{data:r,key:"labelList-".concat(n)})});return o?[function(t,e){return t?!0===t?n.createElement(Aw,{key:"labelList-implicit",data:e}):n.isValidElement(t)||re(t)?n.createElement(Aw,{key:"labelList-implicit",data:e,content:t}):Jt(t)?n.createElement(Aw,xw({data:e},t,{key:"labelList-implicit"})):null:null}(e.label,r)].concat(gw(i)):i};var Dw=function(t){var e=t.cx,r=t.cy,n=t.radius,o=t.angle,i=t.sign,a=t.isExternal,c=t.cornerRadius,u=t.cornerIsExternal,l=c*(a?1:-1)+n,s=Math.asin(c/l)/Zg,f=u?o:o+i*s,p=u?o-i*s:o;return{center:Qg(e,r,l,f),circleTangency:Qg(e,r,n,f),lineTangency:Qg(e,r,l*Math.cos(s*Zg),p),theta:s}},Iw=function(t){var e=t.cx,r=t.cy,n=t.innerRadius,o=t.outerRadius,i=t.startAngle,a=function(t,e){return kn(e-t)*Math.min(Math.abs(e-t),359.999)}(i,t.endAngle),c=i+a,u=Qg(e,r,o,i),l=Qg(e,r,o,c),s="M ".concat(u.x,",").concat(u.y,"\n    A ").concat(o,",").concat(o,",0,\n    ").concat(+(Math.abs(a)>180),",").concat(+(i>c),",\n    ").concat(l.x,",").concat(l.y,"\n  ");if(n>0){var f=Qg(e,r,n,i),p=Qg(e,r,n,c);s+="L ".concat(p.x,",").concat(p.y,"\n            A ").concat(n,",").concat(n,",0,\n            ").concat(+(Math.abs(a)>180),",").concat(+(i<=c),",\n            ").concat(f.x,",").concat(f.y," Z")}else s+="L ".concat(e,",").concat(r," Z");return s},Nw={cx:0,cy:0,innerRadius:0,outerRadius:0,startAngle:0,endAngle:0,cornerRadius:0,forceCornerRadius:!1,cornerIsExternal:!1},Bw=function(t){var e=Tw(Tw({},Nw),t),r=e.cx,o=e.cy,i=e.innerRadius,a=e.outerRadius,c=e.cornerRadius,u=e.forceCornerRadius,l=e.cornerIsExternal,s=e.startAngle,f=e.endAngle,p=e.className;if(a<i||s===f)return null;var h,y=St("recharts-sector",p),d=a-i,v=Tn(c,d,0,!0);return h=v>0&&Math.abs(s-f)<360?function(t){var e=t.cx,r=t.cy,n=t.innerRadius,o=t.outerRadius,i=t.cornerRadius,a=t.forceCornerRadius,c=t.cornerIsExternal,u=t.startAngle,l=t.endAngle,s=kn(l-u),f=Dw({cx:e,cy:r,radius:o,angle:u,sign:s,cornerRadius:i,cornerIsExternal:c}),p=f.circleTangency,h=f.lineTangency,y=f.theta,d=Dw({cx:e,cy:r,radius:o,angle:l,sign:-s,cornerRadius:i,cornerIsExternal:c}),v=d.circleTangency,m=d.lineTangency,b=d.theta,g=c?Math.abs(u-l):Math.abs(u-l)-y-b;if(g<0)return a?"M ".concat(h.x,",").concat(h.y,"\n        a").concat(i,",").concat(i,",0,0,1,").concat(2*i,",0\n        a").concat(i,",").concat(i,",0,0,1,").concat(2*-i,",0\n      "):Iw({cx:e,cy:r,innerRadius:n,outerRadius:o,startAngle:u,endAngle:l});var w="M ".concat(h.x,",").concat(h.y,"\n    A").concat(i,",").concat(i,",0,0,").concat(+(s<0),",").concat(p.x,",").concat(p.y,"\n    A").concat(o,",").concat(o,",0,").concat(+(g>180),",").concat(+(s<0),",").concat(v.x,",").concat(v.y,"\n    A").concat(i,",").concat(i,",0,0,").concat(+(s<0),",").concat(m.x,",").concat(m.y,"\n  ");if(n>0){var x=Dw({cx:e,cy:r,radius:n,angle:u,sign:s,isExternal:!0,cornerRadius:i,cornerIsExternal:c}),O=x.circleTangency,j=x.lineTangency,S=x.theta,k=Dw({cx:e,cy:r,radius:n,angle:l,sign:-s,isExternal:!0,cornerRadius:i,cornerIsExternal:c}),P=k.circleTangency,A=k.lineTangency,E=k.theta,M=c?Math.abs(u-l):Math.abs(u-l)-S-E;if(M<0&&0===i)return"".concat(w,"L").concat(e,",").concat(r,"Z");w+="L".concat(A.x,",").concat(A.y,"\n      A").concat(i,",").concat(i,",0,0,").concat(+(s<0),",").concat(P.x,",").concat(P.y,"\n      A").concat(n,",").concat(n,",0,").concat(+(M>180),",").concat(+(s>0),",").concat(O.x,",").concat(O.y,"\n      A").concat(i,",").concat(i,",0,0,").concat(+(s<0),",").concat(j.x,",").concat(j.y,"Z")}else w+="L".concat(e,",").concat(r,"Z");return w}({cx:r,cy:o,innerRadius:i,outerRadius:a,cornerRadius:Math.min(v,d/2),forceCornerRadius:u,cornerIsExternal:l,startAngle:s,endAngle:f}):Iw({cx:r,cy:o,innerRadius:i,outerRadius:a,startAngle:s,endAngle:f}),n.createElement("path",Mw({},no(e,!0),{className:y,d:h,role:"img"}))};function Lw(t){return(Lw="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function Rw(){return Rw=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},Rw.apply(this,arguments)}function zw(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function Uw(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?zw(Object(r),!0).forEach(function(e){$w(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):zw(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function $w(t,e,r){var n;return n=function(t,e){if("object"!=Lw(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=Lw(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(e,"string"),(e="symbol"==Lw(n)?n:n+"")in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}var qw={curveBasisClosed:function(t){return new Mi(t)},curveBasisOpen:function(t){return new _i(t)},curveBasis:function(t){return new Ei(t)},curveBumpX:function(t){return new ui(t,!0)},curveBumpY:function(t){return new ui(t,!1)},curveLinearClosed:function(t){return new Ti(t)},curveLinear:ni,curveMonotoneX:function(t){return new Bi(t)},curveMonotoneY:function(t){return new Li(t)},curveNatural:function(t){return new zi(t)},curveStep:function(t){return new $i(t,.5)},curveStepAfter:function(t){return new $i(t,1)},curveStepBefore:function(t){return new $i(t,0)}},Fw=function(t){return t.x===+t.x&&t.y===+t.y},Ww=function(t){return t.x},Hw=function(t){return t.y},Vw=function(t){var e,r=t.type,n=void 0===r?"linear":r,o=t.points,i=void 0===o?[]:o,a=t.baseLine,c=t.layout,u=t.connectNulls,l=void 0!==u&&u,s=function(t,e){if(re(t))return t;var r="curve".concat($o(t));return"curveMonotone"!==r&&"curveBump"!==r||!e?qw[r]||ni:qw["".concat(r).concat("vertical"===e?"Y":"X")]}(n,c),f=l?i.filter(function(t){return Fw(t)}):i;if(Array.isArray(a)){var p=l?a.filter(function(t){return Fw(t)}):a,h=f.map(function(t,e){return Uw(Uw({},t),{},{base:p[e]})});return(e="vertical"===c?ci().y(Hw).x1(Ww).x0(function(t){return t.base.x}):ci().x(Ww).y1(Hw).y0(function(t){return t.base.y})).defined(Fw).curve(s),e(h)}return(e="vertical"===c&&An(a)?ci().y(Hw).x1(Ww).x0(a):An(a)?ci().x(Ww).y1(Hw).y0(a):ai().x(Ww).y(Hw)).defined(Fw).curve(s),e(f)},Xw=function(t){var e=t.className,r=t.points,o=t.path,i=t.pathRef;if(!(r&&r.length||o))return null;var a=r&&r.length?Vw(t):o;return n.createElement("path",Rw({},no(t,!1),qn(t),{className:St("recharts-curve",e),d:a,ref:i}))},Gw={exports:{}};function Yw(){}function Kw(){}Kw.resetWarningCache=Yw;Gw.exports=function(){function t(t,e,r,n,o,i){if("SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED"!==i){var a=new Error("Calling PropTypes validators directly is not supported by the `prop-types` package. Use PropTypes.checkPropTypes() to call them. Read more at http://fb.me/use-check-prop-types");throw a.name="Invariant Violation",a}}function e(){return t}t.isRequired=t;var r={array:t,bigint:t,bool:t,func:t,number:t,object:t,string:t,symbol:t,any:t,arrayOf:e,element:t,elementType:t,instanceOf:e,node:t,objectOf:e,oneOf:e,oneOfType:e,shape:e,exact:e,checkPropTypes:Kw,resetWarningCache:Yw};return r.PropTypes=r,r}();const Zw=r(Gw.exports);var Jw=Object.getOwnPropertyNames,Qw=Object.getOwnPropertySymbols,tx=Object.prototype.hasOwnProperty;function ex(t,e){return function(r,n,o){return t(r,n,o)&&e(r,n,o)}}function rx(t){return function(e,r,n){if(!e||!r||"object"!=typeof e||"object"!=typeof r)return t(e,r,n);var o=n.cache,i=o.get(e),a=o.get(r);if(i&&a)return i===r&&a===e;o.set(e,r),o.set(r,e);var c=t(e,r,n);return o.delete(e),o.delete(r),c}}function nx(t){return Jw(t).concat(Qw(t))}var ox=Object.hasOwn||function(t,e){return tx.call(t,e)};function ix(t,e){return t===e||!t&&!e&&t!=t&&e!=e}var ax=Object.getOwnPropertyDescriptor,cx=Object.keys;function ux(t,e,r){var n=t.length;if(e.length!==n)return!1;for(;n-- >0;)if(!r.equals(t[n],e[n],n,n,t,e,r))return!1;return!0}function lx(t,e){return ix(t.getTime(),e.getTime())}function sx(t,e){return t.name===e.name&&t.message===e.message&&t.cause===e.cause&&t.stack===e.stack}function fx(t,e){return t===e}function px(t,e,r){var n=t.size;if(n!==e.size)return!1;if(!n)return!0;for(var o,i,a=new Array(n),c=t.entries(),u=0;(o=c.next())&&!o.done;){for(var l=e.entries(),s=!1,f=0;(i=l.next())&&!i.done;)if(a[f])f++;else{var p=o.value,h=i.value;if(r.equals(p[0],h[0],u,f,t,e,r)&&r.equals(p[1],h[1],p[0],h[0],t,e,r)){s=a[f]=!0;break}f++}if(!s)return!1;u++}return!0}var hx=ix;function yx(t,e,r){var n=cx(t),o=n.length;if(cx(e).length!==o)return!1;for(;o-- >0;)if(!xx(t,e,r,n[o]))return!1;return!0}function dx(t,e,r){var n,o,i,a=nx(t),c=a.length;if(nx(e).length!==c)return!1;for(;c-- >0;){if(!xx(t,e,r,n=a[c]))return!1;if(o=ax(t,n),i=ax(e,n),(o||i)&&(!o||!i||o.configurable!==i.configurable||o.enumerable!==i.enumerable||o.writable!==i.writable))return!1}return!0}function vx(t,e){return ix(t.valueOf(),e.valueOf())}function mx(t,e){return t.source===e.source&&t.flags===e.flags}function bx(t,e,r){var n=t.size;if(n!==e.size)return!1;if(!n)return!0;for(var o,i,a=new Array(n),c=t.values();(o=c.next())&&!o.done;){for(var u=e.values(),l=!1,s=0;(i=u.next())&&!i.done;){if(!a[s]&&r.equals(o.value,i.value,o.value,i.value,t,e,r)){l=a[s]=!0;break}s++}if(!l)return!1}return!0}function gx(t,e){var r=t.length;if(e.length!==r)return!1;for(;r-- >0;)if(t[r]!==e[r])return!1;return!0}function wx(t,e){return t.hostname===e.hostname&&t.pathname===e.pathname&&t.protocol===e.protocol&&t.port===e.port&&t.hash===e.hash&&t.username===e.username&&t.password===e.password}function xx(t,e,r,n){return!("_owner"!==n&&"__o"!==n&&"__v"!==n||!t.$$typeof&&!e.$$typeof)||ox(e,n)&&r.equals(t[n],e[n],n,n,t,e,r)}var Ox=Array.isArray,jx="function"==typeof ArrayBuffer&&ArrayBuffer.isView?ArrayBuffer.isView:null,Sx=Object.assign,kx=Object.prototype.toString.call.bind(Object.prototype.toString);var Px=Ax();function Ax(t){void 0===t&&(t={});var e,r=t.circular,n=void 0!==r&&r,o=t.createInternalComparator,i=t.createState,a=t.strict,c=void 0!==a&&a,u=function(t){var e=t.circular,r=t.createCustomConfig,n=t.strict,o={areArraysEqual:n?dx:ux,areDatesEqual:lx,areErrorsEqual:sx,areFunctionsEqual:fx,areMapsEqual:n?ex(px,dx):px,areNumbersEqual:hx,areObjectsEqual:n?dx:yx,arePrimitiveWrappersEqual:vx,areRegExpsEqual:mx,areSetsEqual:n?ex(bx,dx):bx,areTypedArraysEqual:n?dx:gx,areUrlsEqual:wx};if(r&&(o=Sx({},o,r(o))),e){var i=rx(o.areArraysEqual),a=rx(o.areMapsEqual),c=rx(o.areObjectsEqual),u=rx(o.areSetsEqual);o=Sx({},o,{areArraysEqual:i,areMapsEqual:a,areObjectsEqual:c,areSetsEqual:u})}return o}(t),l=function(t){var e=t.areArraysEqual,r=t.areDatesEqual,n=t.areErrorsEqual,o=t.areFunctionsEqual,i=t.areMapsEqual,a=t.areNumbersEqual,c=t.areObjectsEqual,u=t.arePrimitiveWrappersEqual,l=t.areRegExpsEqual,s=t.areSetsEqual,f=t.areTypedArraysEqual,p=t.areUrlsEqual;return function(t,h,y){if(t===h)return!0;if(null==t||null==h)return!1;var d=typeof t;if(d!==typeof h)return!1;if("object"!==d)return"number"===d?a(t,h,y):"function"===d&&o(t,h,y);var v=t.constructor;if(v!==h.constructor)return!1;if(v===Object)return c(t,h,y);if(Ox(t))return e(t,h,y);if(null!=jx&&jx(t))return f(t,h,y);if(v===Date)return r(t,h,y);if(v===RegExp)return l(t,h,y);if(v===Map)return i(t,h,y);if(v===Set)return s(t,h,y);var m=kx(t);return"[object Date]"===m?r(t,h,y):"[object RegExp]"===m?l(t,h,y):"[object Map]"===m?i(t,h,y):"[object Set]"===m?s(t,h,y):"[object Object]"===m?"function"!=typeof t.then&&"function"!=typeof h.then&&c(t,h,y):"[object URL]"===m?p(t,h,y):"[object Error]"===m?n(t,h,y):"[object Arguments]"===m?c(t,h,y):("[object Boolean]"===m||"[object Number]"===m||"[object String]"===m)&&u(t,h,y)}}(u);return function(t){var e=t.circular,r=t.comparator,n=t.createState,o=t.equals,i=t.strict;if(n)return function(t,a){var c=n(),u=c.cache,l=void 0===u?e?new WeakMap:void 0:u,s=c.meta;return r(t,a,{cache:l,equals:o,meta:s,strict:i})};if(e)return function(t,e){return r(t,e,{cache:new WeakMap,equals:o,meta:void 0,strict:i})};var a={cache:void 0,equals:o,meta:void 0,strict:i};return function(t,e){return r(t,e,a)}}({circular:n,comparator:l,createState:i,equals:o?o(l):(e=l,function(t,r,n,o,i,a,c){return e(t,r,c)}),strict:c})}function Ex(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,r=-1;requestAnimationFrame(function n(o){r<0&&(r=o),o-r>e?(t(o),r=-1):function(t){"undefined"!=typeof requestAnimationFrame&&requestAnimationFrame(t)}(n)})}function Mx(t){return(Mx="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function _x(t){return function(t){if(Array.isArray(t))return t}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||function(t,e){if(!t)return;if("string"==typeof t)return Tx(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);"Object"===r&&t.constructor&&(r=t.constructor.name);if("Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return Tx(t,e)}(t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Tx(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}function Cx(){var t=function(){return null},e=!1,r=function r(n){if(!e){if(Array.isArray(n)){if(!n.length)return;var o=_x(n),i=o[0],a=o.slice(1);return"number"==typeof i?void Ex(r.bind(null,a),i):(r(i),void Ex(r.bind(null,a)))}"object"===Mx(n)&&t(n),"function"==typeof n&&n()}};return{stop:function(){e=!0},start:function(t){e=!1,r(t)},subscribe:function(e){return t=e,function(){t=function(){return null}}}}}function Dx(t){return(Dx="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function Ix(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function Nx(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?Ix(Object(r),!0).forEach(function(e){Bx(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):Ix(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function Bx(t,e,r){return(e=function(t){var e=function(t,e){if("object"!==Dx(t)||null===t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!==Dx(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"===Dx(e)?e:String(e)}(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}Ax({strict:!0}),Ax({circular:!0}),Ax({circular:!0,strict:!0}),Ax({createInternalComparator:function(){return ix}}),Ax({strict:!0,createInternalComparator:function(){return ix}}),Ax({circular:!0,createInternalComparator:function(){return ix}}),Ax({circular:!0,createInternalComparator:function(){return ix},strict:!0});var Lx=function(t){return t},Rx=function(t,e){return Object.keys(e).reduce(function(r,n){return Nx(Nx({},r),{},Bx({},n,t(n,e[n])))},{})},zx=function(t,e,r){return t.map(function(t){return"".concat((n=t,n.replace(/([A-Z])/g,function(t){return"-".concat(t.toLowerCase())}))," ").concat(e,"ms ").concat(r);var n}).join(",")};function Ux(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,c=[],u=!0,l=!1;try{if(i=(r=r.call(t)).next,0===e);else for(;!(u=(n=i.call(r)).done)&&(c.push(n.value),c.length!==e);u=!0);}catch(s){l=!0,o=s}finally{try{if(!u&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(l)throw o}}return c}}(t,e)||qx(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function $x(t){return function(t){if(Array.isArray(t))return Fx(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||qx(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function qx(t,e){if(t){if("string"==typeof t)return Fx(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);return"Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r?Array.from(t):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?Fx(t,e):void 0}}function Fx(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}var Wx=1e-4,Hx=function(t,e){return[0,3*t,3*e-6*t,3*t-3*e+1]},Vx=function(t,e){return t.map(function(t,r){return t*Math.pow(e,r)}).reduce(function(t,e){return t+e})},Xx=function(t,e){return function(r){var n=Hx(t,e);return Vx(n,r)}},Gx=function(){for(var t=arguments.length,e=new Array(t),r=0;r<t;r++)e[r]=arguments[r];var n=e[0],o=e[1],i=e[2],a=e[3];if(1===e.length)switch(e[0]){case"linear":n=0,o=0,i=1,a=1;break;case"ease":n=.25,o=.1,i=.25,a=1;break;case"ease-in":n=.42,o=0,i=1,a=1;break;case"ease-out":n=.42,o=0,i=.58,a=1;break;case"ease-in-out":n=0,o=0,i=.58,a=1;break;default:var c=e[0].split("(");if("cubic-bezier"===c[0]&&4===c[1].split(")")[0].split(",").length){var u=Ux(c[1].split(")")[0].split(",").map(function(t){return parseFloat(t)}),4);n=u[0],o=u[1],i=u[2],a=u[3]}}var l,s,f=Xx(n,i),p=Xx(o,a),h=(l=n,s=i,function(t){var e=Hx(l,s),r=[].concat($x(e.map(function(t,e){return t*e}).slice(1)),[0]);return Vx(r,t)}),y=function(t){return t>1?1:t<0?0:t},d=function(t){for(var e=t>1?1:t,r=e,n=0;n<8;++n){var o=f(r)-e,i=h(r);if(Math.abs(o-e)<Wx||i<Wx)return p(r);r=y(r-o/i)}return p(r)};return d.isStepper=!1,d},Yx=function(){for(var t=arguments.length,e=new Array(t),r=0;r<t;r++)e[r]=arguments[r];var n=e[0];if("string"==typeof n)switch(n){case"ease":case"ease-in-out":case"ease-out":case"ease-in":case"linear":return Gx(n);case"spring":return function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},e=t.stiff,r=void 0===e?100:e,n=t.damping,o=void 0===n?8:n,i=t.dt,a=void 0===i?17:i,c=function(t,e,n){var i=n+(-(t-e)*r-n*o)*a/1e3,c=n*a/1e3+t;return Math.abs(c-e)<Wx&&Math.abs(i)<Wx?[e,0]:[c,i]};return c.isStepper=!0,c.dt=a,c}();default:if("cubic-bezier"===n.split("(")[0])return Gx(n)}return"function"==typeof n?n:null};function Kx(t){return(Kx="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function Zx(t){return function(t){if(Array.isArray(t))return nO(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||rO(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Jx(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function Qx(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?Jx(Object(r),!0).forEach(function(e){tO(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):Jx(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function tO(t,e,r){return(e=function(t){var e=function(t,e){if("object"!==Kx(t)||null===t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!==Kx(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"===Kx(e)?e:String(e)}(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function eO(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,c=[],u=!0,l=!1;try{if(i=(r=r.call(t)).next,0===e);else for(;!(u=(n=i.call(r)).done)&&(c.push(n.value),c.length!==e);u=!0);}catch(s){l=!0,o=s}finally{try{if(!u&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(l)throw o}}return c}}(t,e)||rO(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function rO(t,e){if(t){if("string"==typeof t)return nO(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);return"Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r?Array.from(t):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?nO(t,e):void 0}}function nO(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}var oO=function(t,e,r){return t+(e-t)*r},iO=function(t){return t.from!==t.to},aO=function t(e,r,n){var o=Rx(function(t,r){if(iO(r)){var n=eO(e(r.from,r.to,r.velocity),2),o=n[0],i=n[1];return Qx(Qx({},r),{},{from:o,velocity:i})}return r},r);return n<1?Rx(function(t,e){return iO(e)?Qx(Qx({},e),{},{velocity:oO(e.velocity,o[t].velocity,n),from:oO(e.from,o[t].from,n)}):e},r):t(e,o,n-1)};const cO=function(t,e,r,n,o){var i,a,c,u,l=(i=t,a=e,[Object.keys(i),Object.keys(a)].reduce(function(t,e){return t.filter(function(t){return e.includes(t)})})),s=l.reduce(function(r,n){return Qx(Qx({},r),{},tO({},n,[t[n],e[n]]))},{}),f=l.reduce(function(r,n){return Qx(Qx({},r),{},tO({},n,{from:t[n],velocity:0,to:e[n]}))},{}),p=-1,h=function(){return null};return h=r.isStepper?function(n){c||(c=n);var i=(n-c)/r.dt;f=aO(r,f,i),o(Qx(Qx(Qx({},t),e),Rx(function(t,e){return e.from},f))),c=n,Object.values(f).filter(iO).length&&(p=requestAnimationFrame(h))}:function(i){u||(u=i);var a=(i-u)/n,c=Rx(function(t,e){return oO.apply(void 0,Zx(e).concat([r(a)]))},s);if(o(Qx(Qx(Qx({},t),e),c)),a<1)p=requestAnimationFrame(h);else{var l=Rx(function(t,e){return oO.apply(void 0,Zx(e).concat([r(1)]))},s);o(Qx(Qx(Qx({},t),e),l))}},function(){return requestAnimationFrame(h),function(){cancelAnimationFrame(p)}}};function uO(t){return(uO="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}var lO=["children","begin","duration","attributeName","easing","isActive","steps","from","to","canBegin","onAnimationEnd","shouldReAnimate","onAnimationReStart"];function sO(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r,n,o={},i=Object.keys(t);for(n=0;n<i.length;n++)r=i[n],e.indexOf(r)>=0||(o[r]=t[r]);return o}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],e.indexOf(r)>=0||Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}function fO(t){return function(t){if(Array.isArray(t))return pO(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||function(t,e){if(!t)return;if("string"==typeof t)return pO(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);"Object"===r&&t.constructor&&(r=t.constructor.name);if("Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return pO(t,e)}(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function pO(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}function hO(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function yO(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?hO(Object(r),!0).forEach(function(e){dO(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):hO(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function dO(t,e,r){return(e=mO(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function vO(t,e,r){return e&&function(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,mO(n.key),n)}}(t.prototype,e),Object.defineProperty(t,"prototype",{writable:!1}),t}function mO(t){var e=function(t,e){if("object"!==uO(t)||null===t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!==uO(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"===uO(e)?e:String(e)}function bO(t,e){return(bO=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function gO(t){var e=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){})),!0}catch(t){return!1}}();return function(){var r,n=OO(t);if(e){var o=OO(this).constructor;r=Reflect.construct(n,arguments,o)}else r=n.apply(this,arguments);return wO(this,r)}}function wO(t,e){if(e&&("object"===uO(e)||"function"==typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return xO(t)}function xO(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}function OO(t){return(OO=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}var jO=function(){!function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&bO(t,e)}(r,t.PureComponent);var e=gO(r);function r(t,n){var o;!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,r);var i=(o=e.call(this,t,n)).props,a=i.isActive,c=i.attributeName,u=i.from,l=i.to,s=i.steps,f=i.children,p=i.duration;if(o.handleStyleChange=o.handleStyleChange.bind(xO(o)),o.changeStyle=o.changeStyle.bind(xO(o)),!a||p<=0)return o.state={style:{}},"function"==typeof f&&(o.state={style:l}),wO(o);if(s&&s.length)o.state={style:s[0].style};else if(u){if("function"==typeof f)return o.state={style:u},wO(o);o.state={style:c?dO({},c,u):u}}else o.state={style:{}};return o}return vO(r,[{key:"componentDidMount",value:function(){var t=this.props,e=t.isActive,r=t.canBegin;this.mounted=!0,e&&r&&this.runAnimation(this.props)}},{key:"componentDidUpdate",value:function(t){var e=this.props,r=e.isActive,n=e.canBegin,o=e.attributeName,i=e.shouldReAnimate,a=e.to,c=e.from,u=this.state.style;if(n)if(r){if(!(Px(t.to,a)&&t.canBegin&&t.isActive)){var l=!t.canBegin||!t.isActive;this.manager&&this.manager.stop(),this.stopJSAnimation&&this.stopJSAnimation();var s=l||i?c:t.to;if(this.state&&u){var f={style:o?dO({},o,s):s};(o&&u[o]!==s||!o&&u!==s)&&this.setState(f)}this.runAnimation(yO(yO({},this.props),{},{from:s,begin:0}))}}else{var p={style:o?dO({},o,a):a};this.state&&u&&(o&&u[o]!==a||!o&&u!==a)&&this.setState(p)}}},{key:"componentWillUnmount",value:function(){this.mounted=!1;var t=this.props.onAnimationEnd;this.unSubscribe&&this.unSubscribe(),this.manager&&(this.manager.stop(),this.manager=null),this.stopJSAnimation&&this.stopJSAnimation(),t&&t()}},{key:"handleStyleChange",value:function(t){this.changeStyle(t)}},{key:"changeStyle",value:function(t){this.mounted&&this.setState({style:t})}},{key:"runJSAnimation",value:function(t){var e=this,r=t.from,n=t.to,o=t.duration,i=t.easing,a=t.begin,c=t.onAnimationEnd,u=t.onAnimationStart,l=cO(r,n,Yx(i),o,this.changeStyle);this.manager.start([u,a,function(){e.stopJSAnimation=l()},o,c])}},{key:"runStepAnimation",value:function(t){var e=this,r=t.steps,n=t.begin,o=t.onAnimationStart,i=r[0],a=i.style,c=i.duration,u=void 0===c?0:c;return this.manager.start([o].concat(fO(r.reduce(function(t,n,o){if(0===o)return t;var i=n.duration,a=n.easing,c=void 0===a?"ease":a,u=n.style,l=n.properties,s=n.onAnimationEnd,f=o>0?r[o-1]:n,p=l||Object.keys(u);if("function"==typeof c||"spring"===c)return[].concat(fO(t),[e.runJSAnimation.bind(e,{from:f.style,to:u,duration:i,easing:c}),i]);var h=zx(p,i,c),y=yO(yO(yO({},f.style),u),{},{transition:h});return[].concat(fO(t),[y,i,s]).filter(Lx)},[a,Math.max(u,n)])),[t.onAnimationEnd]))}},{key:"runAnimation",value:function(t){this.manager||(this.manager=Cx());var e=t.begin,r=t.duration,n=t.attributeName,o=t.to,i=t.easing,a=t.onAnimationStart,c=t.onAnimationEnd,u=t.steps,l=t.children,s=this.manager;if(this.unSubscribe=s.subscribe(this.handleStyleChange),"function"!=typeof i&&"function"!=typeof l&&"spring"!==i)if(u.length>1)this.runStepAnimation(t);else{var f=n?dO({},n,o):o,p=zx(Object.keys(f),r,i);s.start([a,e,yO(yO({},f),{},{transition:p}),r,c])}else this.runJSAnimation(t)}},{key:"render",value:function(){var e=this.props,r=e.children;e.begin;var o=e.duration;e.attributeName,e.easing;var i=e.isActive;e.steps,e.from,e.to,e.canBegin,e.onAnimationEnd,e.shouldReAnimate,e.onAnimationReStart;var a=sO(e,lO),c=t.Children.count(r),u=this.state.style;if("function"==typeof r)return r(u);if(!i||0===c||o<=0)return r;var l=function(e){var r=e.props,n=r.style,o=void 0===n?{}:n,i=r.className;return t.cloneElement(e,yO(yO({},a),{},{style:yO(yO({},o),u),className:i}))};return 1===c?l(t.Children.only(r)):n.createElement("div",null,t.Children.map(r,function(t){return l(t)}))}}]),r}();function SO(t){return(SO="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function kO(){return kO=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},kO.apply(this,arguments)}function PO(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,c=[],u=!0,l=!1;try{if(i=(r=r.call(t)).next,0===e);else for(;!(u=(n=i.call(r)).done)&&(c.push(n.value),c.length!==e);u=!0);}catch(s){l=!0,o=s}finally{try{if(!u&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(l)throw o}}return c}}(t,e)||function(t,e){if(!t)return;if("string"==typeof t)return AO(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);"Object"===r&&t.constructor&&(r=t.constructor.name);if("Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return AO(t,e)}(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function AO(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}function EO(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function MO(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?EO(Object(r),!0).forEach(function(e){_O(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):EO(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function _O(t,e,r){var n;return n=function(t,e){if("object"!=SO(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=SO(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(e,"string"),(e="symbol"==SO(n)?n:n+"")in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}jO.displayName="Animate",jO.defaultProps={begin:0,duration:1e3,from:"",to:"",attributeName:"",easing:"ease",isActive:!0,canBegin:!0,steps:[],onAnimationEnd:function(){},onAnimationStart:function(){}},jO.propTypes={from:Zw.oneOfType([Zw.object,Zw.string]),to:Zw.oneOfType([Zw.object,Zw.string]),attributeName:Zw.string,duration:Zw.number,begin:Zw.number,easing:Zw.oneOfType([Zw.string,Zw.func]),steps:Zw.arrayOf(Zw.shape({duration:Zw.number.isRequired,style:Zw.object.isRequired,easing:Zw.oneOfType([Zw.oneOf(["ease","ease-in","ease-out","ease-in-out","linear"]),Zw.func]),properties:Zw.arrayOf("string"),onAnimationEnd:Zw.func})),children:Zw.oneOfType([Zw.node,Zw.func]),isActive:Zw.bool,canBegin:Zw.bool,onAnimationEnd:Zw.func,shouldReAnimate:Zw.bool,onAnimationStart:Zw.func,onAnimationReStart:Zw.func};var TO=function(t,e,r,n,o){var i,a=Math.min(Math.abs(r)/2,Math.abs(n)/2),c=n>=0?1:-1,u=r>=0?1:-1,l=n>=0&&r>=0||n<0&&r<0?1:0;if(a>0&&o instanceof Array){for(var s=[0,0,0,0],f=0;f<4;f++)s[f]=o[f]>a?a:o[f];i="M".concat(t,",").concat(e+c*s[0]),s[0]>0&&(i+="A ".concat(s[0],",").concat(s[0],",0,0,").concat(l,",").concat(t+u*s[0],",").concat(e)),i+="L ".concat(t+r-u*s[1],",").concat(e),s[1]>0&&(i+="A ".concat(s[1],",").concat(s[1],",0,0,").concat(l,",\n        ").concat(t+r,",").concat(e+c*s[1])),i+="L ".concat(t+r,",").concat(e+n-c*s[2]),s[2]>0&&(i+="A ".concat(s[2],",").concat(s[2],",0,0,").concat(l,",\n        ").concat(t+r-u*s[2],",").concat(e+n)),i+="L ".concat(t+u*s[3],",").concat(e+n),s[3]>0&&(i+="A ".concat(s[3],",").concat(s[3],",0,0,").concat(l,",\n        ").concat(t,",").concat(e+n-c*s[3])),i+="Z"}else if(a>0&&o===+o&&o>0){var p=Math.min(a,o);i="M ".concat(t,",").concat(e+c*p,"\n            A ").concat(p,",").concat(p,",0,0,").concat(l,",").concat(t+u*p,",").concat(e,"\n            L ").concat(t+r-u*p,",").concat(e,"\n            A ").concat(p,",").concat(p,",0,0,").concat(l,",").concat(t+r,",").concat(e+c*p,"\n            L ").concat(t+r,",").concat(e+n-c*p,"\n            A ").concat(p,",").concat(p,",0,0,").concat(l,",").concat(t+r-u*p,",").concat(e+n,"\n            L ").concat(t+u*p,",").concat(e+n,"\n            A ").concat(p,",").concat(p,",0,0,").concat(l,",").concat(t,",").concat(e+n-c*p," Z")}else i="M ".concat(t,",").concat(e," h ").concat(r," v ").concat(n," h ").concat(-r," Z");return i},CO=function(t,e){if(!t||!e)return!1;var r=t.x,n=t.y,o=e.x,i=e.y,a=e.width,c=e.height;if(Math.abs(a)>0&&Math.abs(c)>0){var u=Math.min(o,o+a),l=Math.max(o,o+a),s=Math.min(i,i+c),f=Math.max(i,i+c);return r>=u&&r<=l&&n>=s&&n<=f}return!1},DO={x:0,y:0,width:0,height:0,radius:0,isAnimationActive:!1,isUpdateAnimationActive:!1,animationBegin:0,animationDuration:1500,animationEasing:"ease"},IO=function(e){var r=MO(MO({},DO),e),o=t.useRef(),i=PO(t.useState(-1),2),a=i[0],c=i[1];t.useEffect(function(){if(o.current&&o.current.getTotalLength)try{var t=o.current.getTotalLength();t&&c(t)}catch(e){}},[]);var u=r.x,l=r.y,s=r.width,f=r.height,p=r.radius,h=r.className,y=r.animationEasing,d=r.animationDuration,v=r.animationBegin,m=r.isAnimationActive,b=r.isUpdateAnimationActive;if(u!==+u||l!==+l||s!==+s||f!==+f||0===s||0===f)return null;var g=St("recharts-rectangle",h);return b?n.createElement(jO,{canBegin:a>0,from:{width:s,height:f,x:u,y:l},to:{width:s,height:f,x:u,y:l},duration:d,animationEasing:y,isActive:b},function(t){var e=t.width,i=t.height,c=t.x,u=t.y;return n.createElement(jO,{canBegin:a>0,from:"0px ".concat(-1===a?1:a,"px"),to:"".concat(a,"px 0px"),attributeName:"strokeDasharray",begin:v,duration:d,isActive:m,easing:y},n.createElement("path",kO({},no(r,!0),{className:g,d:TO(c,u,e,i,p),ref:o})))}):n.createElement("path",kO({},no(r,!0),{className:g,d:TO(u,l,s,f,p)}))};function NO(){return NO=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},NO.apply(this,arguments)}var BO=function(t){var e=t.cx,r=t.cy,o=t.r,i=St("recharts-dot",t.className);return e===+e&&r===+r&&o===+o?n.createElement("circle",NO({},no(t,!1),qn(t),{className:i,cx:e,cy:r,r:o})):null};function LO(t){return(LO="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}var RO=["x","y","top","left","width","height","className"];function zO(){return zO=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},zO.apply(this,arguments)}function UO(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function $O(t,e,r){var n;return n=function(t,e){if("object"!=LO(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=LO(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(e,"string"),(e="symbol"==LO(n)?n:n+"")in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function qO(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],e.indexOf(r)>=0||Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}var FO=function(t,e,r,n,o,i){return"M".concat(t,",").concat(o,"v").concat(n,"M").concat(i,",").concat(e,"h").concat(r)},WO=function(t){var e=t.x,r=void 0===e?0:e,o=t.y,i=void 0===o?0:o,a=t.top,c=void 0===a?0:a,u=t.left,l=void 0===u?0:u,s=t.width,f=void 0===s?0:s,p=t.height,h=void 0===p?0:p,y=t.className,d=function(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?UO(Object(r),!0).forEach(function(e){$O(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):UO(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}({x:r,y:i,top:c,left:l,width:f,height:h},qO(t,RO));return An(r)&&An(i)&&An(f)&&An(h)&&An(c)&&An(l)?n.createElement("path",zO({},no(d,!0),{className:St("recharts-cross",y),d:FO(r,i,f,h,c,l)})):null},HO=Hc(Object.getPrototypeOf,Object),VO=$t,XO=HO,GO=qt,YO=Function.prototype,KO=Object.prototype,ZO=YO.toString,JO=KO.hasOwnProperty,QO=ZO.call(Object);const tj=r(function(t){if(!GO(t)||"[object Object]"!=VO(t))return!1;var e=XO(t);if(null===e)return!0;var r=JO.call(e,"constructor")&&e.constructor;return"function"==typeof r&&r instanceof r&&ZO.call(r)==QO});var ej=$t,rj=qt;const nj=r(function(t){return!0===t||!1===t||rj(t)&&"[object Boolean]"==ej(t)});function oj(t){return(oj="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function ij(){return ij=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},ij.apply(this,arguments)}function aj(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,c=[],u=!0,l=!1;try{if(i=(r=r.call(t)).next,0===e);else for(;!(u=(n=i.call(r)).done)&&(c.push(n.value),c.length!==e);u=!0);}catch(s){l=!0,o=s}finally{try{if(!u&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(l)throw o}}return c}}(t,e)||function(t,e){if(!t)return;if("string"==typeof t)return cj(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);"Object"===r&&t.constructor&&(r=t.constructor.name);if("Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return cj(t,e)}(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function cj(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}function uj(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function lj(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?uj(Object(r),!0).forEach(function(e){sj(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):uj(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function sj(t,e,r){var n;return n=function(t,e){if("object"!=oj(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=oj(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(e,"string"),(e="symbol"==oj(n)?n:n+"")in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}var fj=function(t,e,r,n,o){var i,a=r-n;return i="M ".concat(t,",").concat(e),i+="L ".concat(t+r,",").concat(e),i+="L ".concat(t+r-a/2,",").concat(e+o),i+="L ".concat(t+r-a/2-n,",").concat(e+o),i+="L ".concat(t,",").concat(e," Z")},pj={x:0,y:0,upperWidth:0,lowerWidth:0,height:0,isUpdateAnimationActive:!1,animationBegin:0,animationDuration:1500,animationEasing:"ease"},hj=function(e){var r=lj(lj({},pj),e),o=t.useRef(),i=aj(t.useState(-1),2),a=i[0],c=i[1];t.useEffect(function(){if(o.current&&o.current.getTotalLength)try{var t=o.current.getTotalLength();t&&c(t)}catch(e){}},[]);var u=r.x,l=r.y,s=r.upperWidth,f=r.lowerWidth,p=r.height,h=r.className,y=r.animationEasing,d=r.animationDuration,v=r.animationBegin,m=r.isUpdateAnimationActive;if(u!==+u||l!==+l||s!==+s||f!==+f||p!==+p||0===s&&0===f||0===p)return null;var b=St("recharts-trapezoid",h);return m?n.createElement(jO,{canBegin:a>0,from:{upperWidth:0,lowerWidth:0,height:p,x:u,y:l},to:{upperWidth:s,lowerWidth:f,height:p,x:u,y:l},duration:d,animationEasing:y,isActive:m},function(t){var e=t.upperWidth,i=t.lowerWidth,c=t.height,u=t.x,l=t.y;return n.createElement(jO,{canBegin:a>0,from:"0px ".concat(-1===a?1:a,"px"),to:"".concat(a,"px 0px"),attributeName:"strokeDasharray",begin:v,duration:d,easing:y},n.createElement("path",ij({},no(r,!0),{className:b,d:fj(u,l,e,i,c),ref:o})))}):n.createElement("g",null,n.createElement("path",ij({},no(r,!0),{className:b,d:fj(u,l,s,f,p)})))},yj=["option","shapeType","propTransformer","activeClassName","isActive"];function dj(t){return(dj="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function vj(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],e.indexOf(r)>=0||Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}function mj(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function bj(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?mj(Object(r),!0).forEach(function(e){gj(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):mj(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function gj(t,e,r){var n;return n=function(t,e){if("object"!=dj(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=dj(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(e,"string"),(e="symbol"==dj(n)?n:n+"")in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function wj(t,e){return bj(bj({},e),t)}function xj(t){var e=t.shapeType,r=t.elementProps;switch(e){case"rectangle":return n.createElement(IO,r);case"trapezoid":return n.createElement(hj,r);case"sector":return n.createElement(Bw,r);case"symbols":if(function(t){return"symbols"===t}(e))return n.createElement(ea,r);break;default:return null}}function Oj(e){var r,o=e.option,i=e.shapeType,a=e.propTransformer,c=void 0===a?wj:a,u=e.activeClassName,l=void 0===u?"recharts-active-shape":u,s=e.isActive,f=vj(e,yj);if(t.isValidElement(o))r=t.cloneElement(o,bj(bj({},f),function(e){return t.isValidElement(e)?e.props:e}(o)));else if(re(o))r=o(f);else if(tj(o)&&!nj(o)){var p=c(o,f);r=n.createElement(xj,{shapeType:i,elementProps:p})}else{var h=f;r=n.createElement(xj,{shapeType:i,elementProps:h})}return s?n.createElement(yo,{className:l},r):r}function jj(t,e){return null!=e&&"trapezoids"in t.props}function Sj(t,e){return null!=e&&"sectors"in t.props}function kj(t,e){return null!=e&&"points"in t.props}function Pj(t,e){var r,n,o=t.x===(null==e||null===(r=e.labelViewBox)||void 0===r?void 0:r.x)||t.x===e.x,i=t.y===(null==e||null===(n=e.labelViewBox)||void 0===n?void 0:n.y)||t.y===e.y;return o&&i}function Aj(t,e){var r=t.endAngle===e.endAngle,n=t.startAngle===e.startAngle;return r&&n}function Ej(t,e){var r=t.x===e.x,n=t.y===e.y,o=t.z===e.z;return r&&n&&o}function Mj(t){var e=t.activeTooltipItem,r=t.graphicalItem,n=t.itemData,o=function(t,e){var r;return jj(t,e)?r="trapezoids":Sj(t,e)?r="sectors":kj(t,e)&&(r="points"),r}(r,e),i=function(t,e){var r,n;return jj(t,e)?null===(r=e.tooltipPayload)||void 0===r||null===(r=r[0])||void 0===r||null===(r=r.payload)||void 0===r?void 0:r.payload:Sj(t,e)?null===(n=e.tooltipPayload)||void 0===n||null===(n=n[0])||void 0===n||null===(n=n.payload)||void 0===n?void 0:n.payload:kj(t,e)?e.payload:{}}(r,e),a=n.filter(function(t,n){var a=Qm(i,t),c=r.props[o].filter(function(t){var n=function(t,e){var r;return jj(t,e)?r=Pj:Sj(t,e)?r=Aj:kj(t,e)&&(r=Ej),r}(r,e);return n(t,e)}),u=r.props[o].indexOf(c[c.length-1]);return a&&n===u});return n.indexOf(a[a.length-1])}var _j=Math.ceil,Tj=Math.max;var Cj=tp,Dj=1/0;var Ij=function(t){return t?(t=Cj(t))===Dj||t===-1/0?17976931348623157e292*(t<0?-1:1):t==t?t:0:0===t?t:0},Nj=function(t,e,r,n){for(var o=-1,i=Tj(_j((e-t)/(r||1)),0),a=Array(i);i--;)a[n?i:++o]=t,t+=r;return a},Bj=Js,Lj=Ij;const Rj=r(function(t){return function(e,r,n){return n&&"number"!=typeof n&&Bj(e,r,n)&&(r=n=void 0),e=Lj(e),void 0===r?(r=e,e=0):r=Lj(r),n=void 0===n?e<r?1:-1:Lj(n),Nj(e,r,n,t)}}());function zj(t){return(zj="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function Uj(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function $j(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?Uj(Object(r),!0).forEach(function(e){qj(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):Uj(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function qj(t,e,r){var n;return n=function(t,e){if("object"!=zj(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=zj(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(e,"string"),(e="symbol"==zj(n)?n:n+"")in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}var Fj=["Webkit","Moz","O","ms"];function Wj(t){return(Wj="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function Hj(){return Hj=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},Hj.apply(this,arguments)}function Vj(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function Xj(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?Vj(Object(r),!0).forEach(function(e){Qj(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):Vj(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function Gj(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,tS(n.key),n)}}function Yj(t,e,r){return e=Zj(e),function(t,e){if(e&&("object"===Wj(e)||"function"==typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(t,Kj()?Reflect.construct(e,r||[],Zj(t).constructor):e.apply(t,r))}function Kj(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(e){}return(Kj=function(){return!!t})()}function Zj(t){return(Zj=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function Jj(t,e){return(Jj=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function Qj(t,e,r){return(e=tS(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function tS(t){var e=function(t,e){if("object"!=Wj(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=Wj(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==Wj(e)?e:e+""}var eS=function(t){return t.changedTouches&&!!t.changedTouches.length},rS=function(){function e(t){var r;return function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,e),Qj(r=Yj(this,e,[t]),"handleDrag",function(t){r.leaveTimer&&(clearTimeout(r.leaveTimer),r.leaveTimer=null),r.state.isTravellerMoving?r.handleTravellerMove(t):r.state.isSlideMoving&&r.handleSlideDrag(t)}),Qj(r,"handleTouchMove",function(t){null!=t.changedTouches&&t.changedTouches.length>0&&r.handleDrag(t.changedTouches[0])}),Qj(r,"handleDragEnd",function(){r.setState({isTravellerMoving:!1,isSlideMoving:!1},function(){var t=r.props,e=t.endIndex,n=t.onDragEnd,o=t.startIndex;null==n||n({endIndex:e,startIndex:o})}),r.detachDragEndListener()}),Qj(r,"handleLeaveWrapper",function(){(r.state.isTravellerMoving||r.state.isSlideMoving)&&(r.leaveTimer=window.setTimeout(r.handleDragEnd,r.props.leaveTimeOut))}),Qj(r,"handleEnterSlideOrTraveller",function(){r.setState({isTextActive:!0})}),Qj(r,"handleLeaveSlideOrTraveller",function(){r.setState({isTextActive:!1})}),Qj(r,"handleSlideDragStart",function(t){var e=eS(t)?t.changedTouches[0]:t;r.setState({isTravellerMoving:!1,isSlideMoving:!0,slideMoveStartX:e.pageX}),r.attachDragEndListener()}),r.travellerDragStartHandlers={startX:r.handleTravellerDragStart.bind(r,"startX"),endX:r.handleTravellerDragStart.bind(r,"endX")},r.state={},r}return function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&Jj(t,e)}(e,t.PureComponent),r=e,i=[{key:"renderDefaultTraveller",value:function(t){var e=t.x,r=t.y,o=t.width,i=t.height,a=t.stroke,c=Math.floor(r+i/2)-1;return n.createElement(n.Fragment,null,n.createElement("rect",{x:e,y:r,width:o,height:i,fill:a,stroke:"none"}),n.createElement("line",{x1:e+1,y1:c,x2:e+o-1,y2:c,fill:"none",stroke:"#fff"}),n.createElement("line",{x1:e+1,y1:c+2,x2:e+o-1,y2:c+2,fill:"none",stroke:"#fff"}))}},{key:"renderTraveller",value:function(t,r){return n.isValidElement(t)?n.cloneElement(t,r):re(t)?t(r):e.renderDefaultTraveller(r)}},{key:"getDerivedStateFromProps",value:function(t,e){var r=t.data,n=t.width,o=t.x,i=t.travellerWidth,a=t.updateId,c=t.startIndex,u=t.endIndex;if(r!==e.prevData||a!==e.prevUpdateId)return Xj({prevData:r,prevTravellerWidth:i,prevUpdateId:a,prevX:o,prevWidth:n},r&&r.length?function(t){var e=t.data,r=t.startIndex,n=t.endIndex,o=t.x,i=t.width,a=t.travellerWidth;if(!e||!e.length)return{};var c=e.length,u=Eh().domain(Rj(0,c)).range([o,o+i-a]),l=u.domain().map(function(t){return u(t)});return{isTextActive:!1,isSlideMoving:!1,isTravellerMoving:!1,isTravellerFocused:!1,startX:u(r),endX:u(n),scale:u,scaleValues:l}}({data:r,width:n,x:o,travellerWidth:i,startIndex:c,endIndex:u}):{scale:null,scaleValues:null});if(e.scale&&(n!==e.prevWidth||o!==e.prevX||i!==e.prevTravellerWidth)){e.scale.range([o,o+n-i]);var l=e.scale.domain().map(function(t){return e.scale(t)});return{prevData:r,prevTravellerWidth:i,prevUpdateId:a,prevX:o,prevWidth:n,startX:e.scale(t.startIndex),endX:e.scale(t.endIndex),scaleValues:l}}return null}},{key:"getIndexInRange",value:function(t,e){for(var r=0,n=t.length-1;n-r>1;){var o=Math.floor((r+n)/2);t[o]>e?n=o:r=o}return e>=t[n]?n:r}}],(o=[{key:"componentWillUnmount",value:function(){this.leaveTimer&&(clearTimeout(this.leaveTimer),this.leaveTimer=null),this.detachDragEndListener()}},{key:"getIndex",value:function(t){var r=t.startX,n=t.endX,o=this.state.scaleValues,i=this.props,a=i.gap,c=i.data.length-1,u=Math.min(r,n),l=Math.max(r,n),s=e.getIndexInRange(o,u),f=e.getIndexInRange(o,l);return{startIndex:s-s%a,endIndex:f===c?c:f-f%a}}},{key:"getTextOfTick",value:function(t){var e=this.props,r=e.data,n=e.tickFormatter,o=e.dataKey,i=kg(r[t],o,t);return re(n)?n(i,t):i}},{key:"attachDragEndListener",value:function(){window.addEventListener("mouseup",this.handleDragEnd,!0),window.addEventListener("touchend",this.handleDragEnd,!0),window.addEventListener("mousemove",this.handleDrag,!0)}},{key:"detachDragEndListener",value:function(){window.removeEventListener("mouseup",this.handleDragEnd,!0),window.removeEventListener("touchend",this.handleDragEnd,!0),window.removeEventListener("mousemove",this.handleDrag,!0)}},{key:"handleSlideDrag",value:function(t){var e=this.state,r=e.slideMoveStartX,n=e.startX,o=e.endX,i=this.props,a=i.x,c=i.width,u=i.travellerWidth,l=i.startIndex,s=i.endIndex,f=i.onChange,p=t.pageX-r;p>0?p=Math.min(p,a+c-u-o,a+c-u-n):p<0&&(p=Math.max(p,a-n,a-o));var h=this.getIndex({startX:n+p,endX:o+p});h.startIndex===l&&h.endIndex===s||!f||f(h),this.setState({startX:n+p,endX:o+p,slideMoveStartX:t.pageX})}},{key:"handleTravellerDragStart",value:function(t,e){var r=eS(e)?e.changedTouches[0]:e;this.setState({isSlideMoving:!1,isTravellerMoving:!0,movingTravellerId:t,brushMoveStartX:r.pageX}),this.attachDragEndListener()}},{key:"handleTravellerMove",value:function(t){var e=this.state,r=e.brushMoveStartX,n=e.movingTravellerId,o=e.endX,i=e.startX,a=this.state[n],c=this.props,u=c.x,l=c.width,s=c.travellerWidth,f=c.onChange,p=c.gap,h=c.data,y={startX:this.state.startX,endX:this.state.endX},d=t.pageX-r;d>0?d=Math.min(d,u+l-s-a):d<0&&(d=Math.max(d,u-a)),y[n]=a+d;var v=this.getIndex(y),m=v.startIndex,b=v.endIndex;this.setState(Qj(Qj({},n,a+d),"brushMoveStartX",t.pageX),function(){var t;f&&(t=h.length-1,("startX"===n&&(o>i?m%p===0:b%p===0)||o<i&&b===t||"endX"===n&&(o>i?b%p===0:m%p===0)||o>i&&b===t)&&f(v))})}},{key:"handleTravellerMoveKeyboard",value:function(t,e){var r=this,n=this.state,o=n.scaleValues,i=n.startX,a=n.endX,c=this.state[e],u=o.indexOf(c);if(-1!==u){var l=u+t;if(!(-1===l||l>=o.length)){var s=o[l];"startX"===e&&s>=a||"endX"===e&&s<=i||this.setState(Qj({},e,s),function(){r.props.onChange(r.getIndex({startX:r.state.startX,endX:r.state.endX}))})}}}},{key:"renderBackground",value:function(){var t=this.props,e=t.x,r=t.y,o=t.width,i=t.height,a=t.fill,c=t.stroke;return n.createElement("rect",{stroke:c,fill:a,x:e,y:r,width:o,height:i})}},{key:"renderPanorama",value:function(){var e=this.props,r=e.x,o=e.y,i=e.width,a=e.height,c=e.data,u=e.children,l=e.padding,s=t.Children.only(u);return s?n.cloneElement(s,{x:r,y:o,width:i,height:a,margin:l,compact:!0,data:c}):null}},{key:"renderTravellerLayer",value:function(t,r){var o,i,a=this,c=this.props,u=c.y,l=c.travellerWidth,s=c.height,f=c.traveller,p=c.ariaLabel,h=c.data,y=c.startIndex,d=c.endIndex,v=Math.max(t,this.props.x),m=Xj(Xj({},no(this.props,!1)),{},{x:v,y:u,width:l,height:s}),b=p||"Min value: ".concat(null===(o=h[y])||void 0===o?void 0:o.name,", Max value: ").concat(null===(i=h[d])||void 0===i?void 0:i.name);return n.createElement(yo,{tabIndex:0,role:"slider","aria-label":b,"aria-valuenow":t,className:"recharts-brush-traveller",onMouseEnter:this.handleEnterSlideOrTraveller,onMouseLeave:this.handleLeaveSlideOrTraveller,onMouseDown:this.travellerDragStartHandlers[r],onTouchStart:this.travellerDragStartHandlers[r],onKeyDown:function(t){["ArrowLeft","ArrowRight"].includes(t.key)&&(t.preventDefault(),t.stopPropagation(),a.handleTravellerMoveKeyboard("ArrowRight"===t.key?1:-1,r))},onFocus:function(){a.setState({isTravellerFocused:!0})},onBlur:function(){a.setState({isTravellerFocused:!1})},style:{cursor:"col-resize"}},e.renderTraveller(f,m))}},{key:"renderSlide",value:function(t,e){var r=this.props,o=r.y,i=r.height,a=r.stroke,c=r.travellerWidth,u=Math.min(t,e)+c,l=Math.max(Math.abs(e-t)-c,0);return n.createElement("rect",{className:"recharts-brush-slide",onMouseEnter:this.handleEnterSlideOrTraveller,onMouseLeave:this.handleLeaveSlideOrTraveller,onMouseDown:this.handleSlideDragStart,onTouchStart:this.handleSlideDragStart,style:{cursor:"move"},stroke:"none",fill:a,fillOpacity:.2,x:u,y:o,width:l,height:i})}},{key:"renderText",value:function(){var t=this.props,e=t.startIndex,r=t.endIndex,o=t.y,i=t.height,a=t.travellerWidth,c=t.stroke,u=this.state,l=u.startX,s=u.endX,f={pointerEvents:"none",fill:c};return n.createElement(yo,{className:"recharts-brush-texts"},n.createElement(Qp,Hj({textAnchor:"end",verticalAnchor:"middle",x:Math.min(l,s)-5,y:o+i/2},f),this.getTextOfTick(e)),n.createElement(Qp,Hj({textAnchor:"start",verticalAnchor:"middle",x:Math.max(l,s)+a+5,y:o+i/2},f),this.getTextOfTick(r)))}},{key:"render",value:function(){var t=this.props,e=t.data,r=t.className,o=t.children,i=t.x,a=t.y,c=t.width,u=t.height,l=t.alwaysShowText,s=this.state,f=s.startX,p=s.endX,h=s.isTextActive,y=s.isSlideMoving,d=s.isTravellerMoving,v=s.isTravellerFocused;if(!e||!e.length||!An(i)||!An(a)||!An(c)||!An(u)||c<=0||u<=0)return null;var m,b,g,w,x=St("recharts-brush",r),O=1===n.Children.count(o),j=(b="none",g=(m="userSelect").replace(/(\w)/,function(t){return t.toUpperCase()}),(w=Fj.reduce(function(t,e){return $j($j({},t),{},qj({},e+g,b))},{}))[m]=b,w);return n.createElement(yo,{className:x,onMouseLeave:this.handleLeaveWrapper,onTouchMove:this.handleTouchMove,style:j},this.renderBackground(),O&&this.renderPanorama(),this.renderSlide(f,p),this.renderTravellerLayer(f,"startX"),this.renderTravellerLayer(p,"endX"),(h||y||d||v||l)&&this.renderText())}}])&&Gj(r.prototype,o),i&&Gj(r,i),Object.defineProperty(r,"prototype",{writable:!1}),r;var r,o,i}();Qj(rS,"displayName","Brush"),Qj(rS,"defaultProps",{height:40,travellerWidth:5,gap:1,fill:"#fff",stroke:"#666",padding:{top:1,right:1,bottom:1,left:1},leaveTimeOut:1e3,alwaysShowText:!1});var nS=bs;var oS=Ta,iS=Al,aS=function(t,e){var r;return nS(t,function(t,n,o){return!(r=e(t,n,o))}),!!r},cS=kt,uS=Js;const lS=r(function(t,e,r){var n=cS(t)?oS:aS;return r&&uS(t,e,r)&&(e=void 0),n(t,iS(e))});var sS=function(t,e){var r=t.alwaysShow,n=t.ifOverflow;return r&&(n="extendDomain"),n===e},fS=zs;var pS=function(t,e,r){"__proto__"==e&&fS?fS(t,e,{configurable:!0,enumerable:!0,value:r,writable:!0}):t[e]=r},hS=vs,yS=Al;const dS=r(function(t,e){var r={};return e=yS(e),hS(t,function(t,n,o){pS(r,n,e(t,n,o))}),r});var vS=bs;var mS=function(t,e){for(var r=-1,n=null==t?0:t.length;++r<n;)if(!e(t[r],r,t))return!1;return!0},bS=function(t,e){var r=!0;return vS(t,function(t,n,o){return r=!!e(t,n,o)}),r},gS=Al,wS=kt,xS=Js;const OS=r(function(t,e,r){var n=wS(t)?mS:bS;return r&&xS(t,e,r)&&(e=void 0),n(t,gS(e))});var jS=["x","y"];function SS(t){return(SS="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function kS(){return kS=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},kS.apply(this,arguments)}function PS(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function AS(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?PS(Object(r),!0).forEach(function(e){ES(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):PS(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function ES(t,e,r){var n;return n=function(t,e){if("object"!=SS(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=SS(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(e,"string"),(e="symbol"==SS(n)?n:n+"")in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function MS(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],e.indexOf(r)>=0||Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}function _S(t,e){var r=t.x,n=t.y,o=MS(t,jS),i="".concat(r),a=parseInt(i,10),c="".concat(n),u=parseInt(c,10),l="".concat(e.height||o.height),s=parseInt(l,10),f="".concat(e.width||o.width),p=parseInt(f,10);return AS(AS(AS(AS(AS({},e),o),a?{x:a}:{}),u?{y:u}:{}),{},{height:s,width:p,name:e.name,radius:e.radius})}function TS(t){return n.createElement(Oj,kS({shapeType:"rectangle",propTransformer:_S,activeClassName:"recharts-active-bar"},t))}var CS,DS=["value","background"];function IS(t){return(IS="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function NS(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],e.indexOf(r)>=0||Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}function BS(){return BS=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},BS.apply(this,arguments)}function LS(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function RS(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?LS(Object(r),!0).forEach(function(e){WS(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):LS(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function zS(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,HS(n.key),n)}}function US(t,e,r){return e=qS(e),function(t,e){if(e&&("object"===IS(e)||"function"==typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(t,$S()?Reflect.construct(e,r||[],qS(t).constructor):e.apply(t,r))}function $S(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(e){}return($S=function(){return!!t})()}function qS(t){return(qS=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function FS(t,e){return(FS=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function WS(t,e,r){return(e=HS(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function HS(t){var e=function(t,e){if("object"!=IS(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=IS(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==IS(e)?e:e+""}var VS=function(){function e(){var t;!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,e);for(var r=arguments.length,n=new Array(r),o=0;o<r;o++)n[o]=arguments[o];return WS(t=US(this,e,[].concat(n)),"state",{isAnimationFinished:!1}),WS(t,"id",_n("recharts-bar-")),WS(t,"handleAnimationEnd",function(){var e=t.props.onAnimationEnd;t.setState({isAnimationFinished:!0}),e&&e()}),WS(t,"handleAnimationStart",function(){var e=t.props.onAnimationStart;t.setState({isAnimationFinished:!1}),e&&e()}),t}return function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&FS(t,e)}(e,t.PureComponent),r=e,i=[{key:"getDerivedStateFromProps",value:function(t,e){return t.animationId!==e.prevAnimationId?{prevAnimationId:t.animationId,curData:t.data,prevData:e.curData}:t.data!==e.curData?{curData:t.data}:null}}],(o=[{key:"renderRectanglesStatically",value:function(t){var e=this,r=this.props,o=r.shape,i=r.dataKey,a=r.activeIndex,c=r.activeBar,u=no(this.props,!1);return t&&t.map(function(t,r){var l=r===a,s=l?c:o,f=RS(RS(RS({},u),t),{},{isActive:l,option:s,index:r,dataKey:i,onAnimationStart:e.handleAnimationStart,onAnimationEnd:e.handleAnimationEnd});return n.createElement(yo,BS({className:"recharts-bar-rectangle"},Fn(e.props,t,r),{key:"rectangle-".concat(null==t?void 0:t.x,"-").concat(null==t?void 0:t.y,"-").concat(null==t?void 0:t.value,"-").concat(r)}),n.createElement(TS,f))})}},{key:"renderRectanglesWithAnimation",value:function(){var t=this,e=this.props,r=e.data,o=e.layout,i=e.isAnimationActive,a=e.animationBegin,c=e.animationDuration,u=e.animationEasing,l=e.animationId,s=this.state.prevData;return n.createElement(jO,{begin:a,duration:c,isActive:i,easing:u,from:{t:0},to:{t:1},key:"bar-".concat(l),onAnimationEnd:this.handleAnimationEnd,onAnimationStart:this.handleAnimationStart},function(e){var i=e.t,a=r.map(function(t,e){var r=s&&s[e];if(r){var n=Dn(r.x,t.x),a=Dn(r.y,t.y),c=Dn(r.width,t.width),u=Dn(r.height,t.height);return RS(RS({},t),{},{x:n(i),y:a(i),width:c(i),height:u(i)})}if("horizontal"===o){var l=Dn(0,t.height)(i);return RS(RS({},t),{},{y:t.y+t.height-l,height:l})}var f=Dn(0,t.width)(i);return RS(RS({},t),{},{width:f})});return n.createElement(yo,null,t.renderRectanglesStatically(a))})}},{key:"renderRectangles",value:function(){var t=this.props,e=t.data,r=t.isAnimationActive,n=this.state.prevData;return!(r&&e&&e.length)||n&&Qm(n,e)?this.renderRectanglesStatically(e):this.renderRectanglesWithAnimation()}},{key:"renderBackground",value:function(){var t=this,e=this.props,r=e.data,o=e.dataKey,i=e.activeIndex,a=no(this.props.background,!1);return r.map(function(e,r){e.value;var c=e.background,u=NS(e,DS);if(!c)return null;var l=RS(RS(RS(RS(RS({},u),{},{fill:"#eee"},c),a),Fn(t.props,e,r)),{},{onAnimationStart:t.handleAnimationStart,onAnimationEnd:t.handleAnimationEnd,dataKey:o,index:r,className:"recharts-bar-background-rectangle"});return n.createElement(TS,BS({key:"background-bar-".concat(r),option:t.props.background,isActive:r===i},l))})}},{key:"renderErrorBar",value:function(t,e){if(this.props.isAnimationActive&&!this.state.isAnimationFinished)return null;var r=this.props,o=r.data,i=r.xAxis,a=r.yAxis,c=r.layout,u=Qn(r.children,hg);if(!u)return null;var l="vertical"===c?o[0].height/2:o[0].width/2,s=function(t,e){var r=Array.isArray(t.value)?t.value[1]:t.value;return{x:t.x,y:t.y,value:r,errorVal:kg(t,e)}},f={clipPath:t?"url(#clipPath-".concat(e,")"):null};return n.createElement(yo,f,u.map(function(t){return n.cloneElement(t,{key:"error-bar-".concat(e,"-").concat(t.props.dataKey),data:o,xAxis:i,yAxis:a,layout:c,offset:l,dataPointFormatter:s})}))}},{key:"render",value:function(){var t=this.props,e=t.hide,r=t.data,o=t.className,i=t.xAxis,a=t.yAxis,c=t.left,u=t.top,l=t.width,s=t.height,f=t.isAnimationActive,p=t.background,h=t.id;if(e||!r||!r.length)return null;var y=this.state.isAnimationFinished,d=St("recharts-bar",o),v=i&&i.allowDataOverflow,m=a&&a.allowDataOverflow,b=v||m,g=Gr(h)?this.id:h;return n.createElement(yo,{className:d},v||m?n.createElement("defs",null,n.createElement("clipPath",{id:"clipPath-".concat(g)},n.createElement("rect",{x:v?c:c-l/2,y:m?u:u-s/2,width:v?l:2*l,height:m?s:2*s}))):null,n.createElement(yo,{className:"recharts-bar-rectangles",clipPath:b?"url(#clipPath-".concat(g,")"):null},p?this.renderBackground():null,this.renderRectangles()),this.renderErrorBar(b,g),(!f||y)&&Aw.renderCallByParent(this.props,r))}}])&&zS(r.prototype,o),i&&zS(r,i),Object.defineProperty(r,"prototype",{writable:!1}),r;var r,o,i}();function XS(t){return(XS="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function GS(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,JS(n.key),n)}}function YS(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function KS(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?YS(Object(r),!0).forEach(function(e){ZS(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):YS(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function ZS(t,e,r){return(e=JS(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function JS(t){var e=function(t,e){if("object"!=XS(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=XS(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==XS(e)?e:e+""}CS=VS,WS(VS,"displayName","Bar"),WS(VS,"defaultProps",{xAxisId:0,yAxisId:0,legendType:"rect",minPointSize:0,hide:!1,data:[],layout:"vertical",activeBar:!1,isAnimationActive:!_f.isSsr,animationBegin:0,animationDuration:400,animationEasing:"ease"}),WS(VS,"getComposedData",function(t){var e=t.props,r=t.item,n=t.barPosition,o=t.bandSize,i=t.xAxis,a=t.yAxis,c=t.xAxisTicks,u=t.yAxisTicks,l=t.stackedData,s=t.dataStartIndex,f=t.displayedData,p=t.offset,h=function(t,e){if(!t)return null;for(var r=0,n=t.length;r<n;r++)if(t[r].item===e)return t[r].position;return null}(n,r);if(!h)return null;var y=e.layout,d=r.type.defaultProps,v=void 0!==d?RS(RS({},d),r.props):r.props,m=v.dataKey,b=v.children,g=v.minPointSize,w="horizontal"===y?a:i,x=l?w.scale.domain():null,O=function(t){var e=t.numericAxis,r=e.scale.domain();if("number"===e.type){var n=Math.min(r[0],r[1]),o=Math.max(r[0],r[1]);return n<=0&&o>=0?0:o<0?o:n}return r[0]}({numericAxis:w}),j=Qn(b,vp),S=f.map(function(t,e){var n,f,p,d,v,b;l?n=function(t,e){if(!e||2!==e.length||!An(e[0])||!An(e[1]))return t;var r=Math.min(e[0],e[1]),n=Math.max(e[0],e[1]),o=[t[0],t[1]];return(!An(t[0])||t[0]<r)&&(o[0]=r),(!An(t[1])||t[1]>n)&&(o[1]=n),o[0]>n&&(o[0]=n),o[1]<r&&(o[1]=r),o}(l[s+e],x):(n=kg(t,m),Array.isArray(n)||(n=[O,n]));var w=function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;return function(r,n){if("number"==typeof t)return t;var o="number"==typeof r;return o?t(r,n):(o||Qb(),e)}}(g,CS.defaultProps.minPointSize)(n[1],e);if("horizontal"===y){var S,k=[a.scale(n[0]),a.scale(n[1])],P=k[0],A=k[1];f=zg({axis:i,ticks:c,bandSize:o,offset:h.offset,entry:t,index:e}),p=null!==(S=null!=A?A:P)&&void 0!==S?S:void 0,d=h.size;var E=P-A;if(v=Number.isNaN(E)?0:E,b={x:f,y:a.y,width:d,height:a.height},Math.abs(w)>0&&Math.abs(v)<Math.abs(w)){var M=kn(v||w)*(Math.abs(w)-Math.abs(v));p-=M,v+=M}}else{var _=[i.scale(n[0]),i.scale(n[1])],T=_[0],C=_[1];if(f=T,p=zg({axis:a,ticks:u,bandSize:o,offset:h.offset,entry:t,index:e}),d=C-T,v=h.size,b={x:i.x,y:p,width:i.width,height:v},Math.abs(w)>0&&Math.abs(d)<Math.abs(w))d+=kn(d||w)*(Math.abs(w)-Math.abs(d))}return RS(RS(RS({},t),{},{x:f,y:p,width:d,height:v,value:l?n:n[1],payload:t,background:b},j&&j[e]&&j[e].props),{},{tooltipPayload:[Vg(r,t)],tooltipPosition:{x:f+d/2,y:p+v/2}})});return RS({data:S,layout:y},p)});var QS=function(t,e,r,n,o){var i=t.width,a=t.height,c=t.layout,u=t.children,l=Object.keys(e),s={left:r.left,leftMirror:r.left,right:i-r.right,rightMirror:i-r.right,top:r.top,topMirror:r.top,bottom:a-r.bottom,bottomMirror:a-r.bottom},f=!!to(u,VS);return l.reduce(function(i,a){var u,l,p,h,y,d=e[a],v=d.orientation,m=d.domain,b=d.padding,g=void 0===b?{}:b,w=d.mirror,x=d.reversed,O="".concat(v).concat(w?"Mirror":"");if("number"===d.type&&("gap"===d.padding||"no-gap"===d.padding)){var j=m[1]-m[0],S=1/0,k=d.categoricalDomain.sort(Nn);if(k.forEach(function(t,e){e>0&&(S=Math.min((t||0)-(k[e-1]||0),S))}),Number.isFinite(S)){var P=S/j,A="vertical"===d.layout?r.height:r.width;if("gap"===d.padding&&(u=P*A/2),"no-gap"===d.padding){var E=Tn(t.barCategoryGap,P*A),M=P*A/2;u=M-E-(M-E)/A*E}}}l="xAxis"===n?[r.left+(g.left||0)+(u||0),r.left+r.width-(g.right||0)-(u||0)]:"yAxis"===n?"horizontal"===c?[r.top+r.height-(g.bottom||0),r.top+(g.top||0)]:[r.top+(g.top||0)+(u||0),r.top+r.height-(g.bottom||0)-(u||0)]:d.range,x&&(l=[l[1],l[0]]);var _=function(t,e,r){var n=t.scale,o=t.type,i=t.layout,a=t.axisType;if("auto"===n)return"radial"===i&&"radiusAxis"===a?{scale:Ph(),realScaleType:"band"}:"radial"===i&&"angleAxis"===a?{scale:Yy(),realScaleType:"linear"}:"category"===o&&e&&(e.indexOf("LineChart")>=0||e.indexOf("AreaChart")>=0||e.indexOf("ComposedChart")>=0&&!r)?{scale:Eh(),realScaleType:"point"}:"category"===o?{scale:Ph(),realScaleType:"band"}:{scale:Yy(),realScaleType:"linear"};if(Jr(n)){var c="scale".concat($o(n));return{scale:(Im[c]||Eh)(),realScaleType:Im[c]?c:"point"}}return re(n)?{scale:n}:{scale:Eh(),realScaleType:"point"}}(d,o,f),T=_.scale,C=_.realScaleType;T.domain(m).range(l),function(t){var e=t.domain();if(e&&!(e.length<=2)){var r=e.length,n=t.range(),o=Math.min(n[0],n[1])-Ng,i=Math.max(n[0],n[1])+Ng,a=t(e[0]),c=t(e[r-1]);(a<o||a>i||c<o||c>i)&&t.domain([e[0],e[r-1]])}}(T);var D=function(t,e){var r=e.realScaleType,n=e.type,o=e.tickCount,i=e.originalDomain,a=e.allowDecimals,c=r||e.scale;if("auto"!==c&&"linear"!==c)return null;if(o&&"number"===n&&i&&("auto"===i[0]||"auto"===i[1])){var u=t.domain();if(!u.length)return null;var l=Zb(u,o,a);return t.domain([Wm(l),Um(l)]),{niceTicks:l}}if(o&&"number"===n){var s=t.domain();return{niceTicks:Jb(s,o,a)}}return null}(T,KS(KS({},d),{},{realScaleType:C}));"xAxis"===n?(y="top"===v&&!w||"bottom"===v&&w,p=r.left,h=s[O]-y*d.height):"yAxis"===n&&(y="left"===v&&!w||"right"===v&&w,p=s[O]-y*d.width,h=r.top);var I=KS(KS(KS({},d),D),{},{realScaleType:C,x:p,y:h,scale:T,width:"xAxis"===n?r.width:d.width,height:"yAxis"===n?r.height:d.height});return I.bandSize=Wg(I,D),d.hide||"xAxis"!==n?d.hide||(s[O]+=(y?-1:1)*I.width):s[O]+=(y?-1:1)*I.height,KS(KS({},i),{},ZS({},a,I))},{})},tk=function(t,e){var r=t.x,n=t.y,o=e.x,i=e.y;return{x:Math.min(r,o),y:Math.min(n,i),width:Math.abs(o-r),height:Math.abs(i-n)}},ek=function(){function t(e){!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t),this.scale=e}return e=t,r=[{key:"domain",get:function(){return this.scale.domain}},{key:"range",get:function(){return this.scale.range}},{key:"rangeMin",get:function(){return this.range()[0]}},{key:"rangeMax",get:function(){return this.range()[1]}},{key:"bandwidth",get:function(){return this.scale.bandwidth}},{key:"apply",value:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=e.bandAware,n=e.position;if(void 0!==t){if(n)switch(n){case"start":default:return this.scale(t);case"middle":var o=this.bandwidth?this.bandwidth()/2:0;return this.scale(t)+o;case"end":var i=this.bandwidth?this.bandwidth():0;return this.scale(t)+i}if(r){var a=this.bandwidth?this.bandwidth()/2:0;return this.scale(t)+a}return this.scale(t)}}},{key:"isInRange",value:function(t){var e=this.range(),r=e[0],n=e[e.length-1];return r<=n?t>=r&&t<=n:t>=n&&t<=r}}],n=[{key:"create",value:function(e){return new t(e)}}],r&&GS(e.prototype,r),n&&GS(e,n),Object.defineProperty(e,"prototype",{writable:!1}),e;var e,r,n}();ZS(ek,"EPS",1e-4);var rk=function(t){var e=Object.keys(t).reduce(function(e,r){return KS(KS({},e),{},ZS({},r,ek.create(t[r])))},{});return KS(KS({},e),{},{apply:function(t){var r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=r.bandAware,o=r.position;return dS(t,function(t,r){return e[r].apply(t,{bandAware:n,position:o})})},isInRange:function(t){return OS(t,function(t,r){return e[r].isInRange(t)})}})};var nk=Al,ok=Jc,ik=ru;var ak=function(t){return function(e,r,n){var o=Object(e);if(!ok(e)){var i=nk(r);e=ik(e),r=function(t){return i(o[t],t,o)}}var a=t(e,r,n);return a>-1?o[i?e[a]:a]:void 0}},ck=Ij;var uk=El,lk=Al,sk=function(t){var e=ck(t),r=e%1;return e==e?r?e-r:e:0},fk=Math.max;const pk=r(ak(function(t,e,r){var n=null==t?0:t.length;if(!n)return-1;var o=null==r?0:sk(r);return o<0&&(o=fk(n+o,0)),uk(t,lk(e),o)}));var hk=xr(function(t){return{x:t.left,y:t.top,width:t.width,height:t.height}},function(t){return["l",t.left,"t",t.top,"w",t.width,"h",t.height].join("")}),yk=t.createContext(void 0),dk=t.createContext(void 0),vk=t.createContext(void 0),mk=t.createContext({}),bk=t.createContext(void 0),gk=t.createContext(0),wk=t.createContext(0),xk=function(t){var e=t.state,r=e.xAxisMap,o=e.yAxisMap,i=e.offset,a=t.clipPathId,c=t.children,u=t.width,l=t.height,s=hk(i);return n.createElement(yk.Provider,{value:r},n.createElement(dk.Provider,{value:o},n.createElement(mk.Provider,{value:i},n.createElement(vk.Provider,{value:s},n.createElement(bk.Provider,{value:a},n.createElement(gk.Provider,{value:l},n.createElement(wk.Provider,{value:u},c)))))))},Ok=function(e){var r=t.useContext(yk);null==r&&Qb();var n=r[e];return null==n&&Qb(),n},jk=function(e){var r=t.useContext(dk);null==r&&Qb();var n=r[e];return null==n&&Qb(),n},Sk=function(){return t.useContext(wk)},kk=function(){return t.useContext(gk)};function Pk(t){return(Pk="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function Ak(t,e,r){return e&&function(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,Nk(n.key),n)}}(t.prototype,e),Object.defineProperty(t,"prototype",{writable:!1}),t}function Ek(t,e,r){return e=_k(e),function(t,e){if(e&&("object"===Pk(e)||"function"==typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(t,Mk()?Reflect.construct(e,r||[],_k(t).constructor):e.apply(t,r))}function Mk(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(e){}return(Mk=function(){return!!t})()}function _k(t){return(_k=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function Tk(t,e){return(Tk=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function Ck(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function Dk(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?Ck(Object(r),!0).forEach(function(e){Ik(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):Ck(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function Ik(t,e,r){return(e=Nk(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function Nk(t){var e=function(t,e){if("object"!=Pk(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=Pk(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==Pk(e)?e:e+""}function Bk(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,c=[],u=!0,l=!1;try{if(i=(r=r.call(t)).next,0===e);else for(;!(u=(n=i.call(r)).done)&&(c.push(n.value),c.length!==e);u=!0);}catch(s){l=!0,o=s}finally{try{if(!u&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(l)throw o}}return c}}(t,e)||function(t,e){if(!t)return;if("string"==typeof t)return Lk(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);"Object"===r&&t.constructor&&(r=t.constructor.name);if("Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return Lk(t,e)}(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Lk(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}function Rk(){return Rk=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},Rk.apply(this,arguments)}function zk(e){var r=e.x,o=e.y,i=e.segment,a=e.xAxisId,c=e.yAxisId,u=e.shape,l=e.className,s=e.alwaysShow,f=t.useContext(bk),p=Ok(a),h=jk(c),y=t.useContext(vk);if(!f||!y)return null;vo(void 0===s,'The alwaysShow prop is deprecated. Please use ifOverflow="extendDomain" instead.');var d=function(t,e,r,n,o,i,a,c,u){var l=o.x,s=o.y,f=o.width,p=o.height;if(r){var h=u.y,y=t.y.apply(h,{position:i});if(sS(u,"discard")&&!t.y.isInRange(y))return null;var d=[{x:l+f,y:y},{x:l,y:y}];return"left"===c?d.reverse():d}if(e){var v=u.x,m=t.x.apply(v,{position:i});if(sS(u,"discard")&&!t.x.isInRange(m))return null;var b=[{x:m,y:s+p},{x:m,y:s}];return"top"===a?b.reverse():b}if(n){var g=u.segment.map(function(e){return t.apply(e,{position:i})});return sS(u,"discard")&&lS(g,function(e){return!t.isInRange(e)})?null:g}return null}(rk({x:p.scale,y:h.scale}),En(r),En(o),i&&2===i.length,y,e.position,p.orientation,h.orientation,e);if(!d)return null;var v=Bk(d,2),m=v[0],b=m.x,g=m.y,w=v[1],x=w.x,O=w.y,j=Dk(Dk({clipPath:sS(e,"hidden")?"url(#".concat(f,")"):void 0},no(e,!0)),{},{x1:b,y1:g,x2:x,y2:O});return n.createElement(yo,{className:St("recharts-reference-line",l)},function(t,e){return n.isValidElement(t)?n.cloneElement(t,e):re(t)?t(e):n.createElement("line",Rk({},e,{className:"recharts-reference-line-line"}))}(u,j),hw.renderCallByParent(e,function(t){var e=t.x1,r=t.y1,n=t.x2,o=t.y2;return tk({x:e,y:r},{x:n,y:o})}({x1:b,y1:g,x2:x,y2:O})))}var Uk=function(){function t(){return function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t),Ek(this,t,arguments)}return function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&Tk(t,e)}(t,n.Component),Ak(t,[{key:"render",value:function(){return n.createElement(zk,this.props)}}])}();function $k(){return $k=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},$k.apply(this,arguments)}function qk(t){return(qk="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function Fk(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function Wk(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?Fk(Object(r),!0).forEach(function(e){Kk(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):Fk(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function Hk(t,e,r){return e&&function(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,Zk(n.key),n)}}(t.prototype,e),Object.defineProperty(t,"prototype",{writable:!1}),t}function Vk(t,e,r){return e=Gk(e),function(t,e){if(e&&("object"===qk(e)||"function"==typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(t,Xk()?Reflect.construct(e,r||[],Gk(t).constructor):e.apply(t,r))}function Xk(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(e){}return(Xk=function(){return!!t})()}function Gk(t){return(Gk=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function Yk(t,e){return(Yk=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function Kk(t,e,r){return(e=Zk(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function Zk(t){var e=function(t,e){if("object"!=qk(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=qk(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==qk(e)?e:e+""}Ik(Uk,"displayName","ReferenceLine"),Ik(Uk,"defaultProps",{isFront:!1,ifOverflow:"discard",xAxisId:0,yAxisId:0,fill:"none",stroke:"#ccc",fillOpacity:1,strokeWidth:1,position:"middle"});var Jk=function(){function t(){return function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t),Vk(this,t,arguments)}return function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&Yk(t,e)}(t,n.Component),Hk(t,[{key:"render",value:function(){var e=this.props,r=e.x,o=e.y,i=e.r,a=e.alwaysShow,c=e.clipPathId,u=En(r),l=En(o);if(vo(void 0===a,'The alwaysShow prop is deprecated. Please use ifOverflow="extendDomain" instead.'),!u||!l)return null;var s=function(t){var e=t.x,r=t.y,n=t.xAxis,o=t.yAxis,i=rk({x:n.scale,y:o.scale}),a=i.apply({x:e,y:r},{bandAware:!0});return sS(t,"discard")&&!i.isInRange(a)?null:a}(this.props);if(!s)return null;var f=s.x,p=s.y,h=this.props,y=h.shape,d=h.className,v=Wk(Wk({clipPath:sS(this.props,"hidden")?"url(#".concat(c,")"):void 0},no(this.props,!0)),{},{cx:f,cy:p});return n.createElement(yo,{className:St("recharts-reference-dot",d)},t.renderDot(y,v),hw.renderCallByParent(this.props,{x:f-i,y:p-i,width:2*i,height:2*i}))}}])}();function Qk(){return Qk=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},Qk.apply(this,arguments)}function tP(t){return(tP="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function eP(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function rP(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?eP(Object(r),!0).forEach(function(e){uP(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):eP(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function nP(t,e,r){return e&&function(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,lP(n.key),n)}}(t.prototype,e),Object.defineProperty(t,"prototype",{writable:!1}),t}function oP(t,e,r){return e=aP(e),function(t,e){if(e&&("object"===tP(e)||"function"==typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(t,iP()?Reflect.construct(e,r||[],aP(t).constructor):e.apply(t,r))}function iP(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(e){}return(iP=function(){return!!t})()}function aP(t){return(aP=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function cP(t,e){return(cP=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function uP(t,e,r){return(e=lP(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function lP(t){var e=function(t,e){if("object"!=tP(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=tP(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==tP(e)?e:e+""}Kk(Jk,"displayName","ReferenceDot"),Kk(Jk,"defaultProps",{isFront:!1,ifOverflow:"discard",xAxisId:0,yAxisId:0,r:10,fill:"#fff",stroke:"#ccc",fillOpacity:1,strokeWidth:1}),Kk(Jk,"renderDot",function(t,e){return n.isValidElement(t)?n.cloneElement(t,e):re(t)?t(e):n.createElement(BO,$k({},e,{cx:e.cx,cy:e.cy,className:"recharts-reference-dot-dot"}))});var sP=function(){function t(){return function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t),oP(this,t,arguments)}return function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&cP(t,e)}(t,n.Component),nP(t,[{key:"render",value:function(){var e=this.props,r=e.x1,o=e.x2,i=e.y1,a=e.y2,c=e.className,u=e.alwaysShow,l=e.clipPathId;vo(void 0===u,'The alwaysShow prop is deprecated. Please use ifOverflow="extendDomain" instead.');var s=En(r),f=En(o),p=En(i),h=En(a),y=this.props.shape;if(!(s||f||p||h||y))return null;var d=function(t,e,r,n,o){var i=o.x1,a=o.x2,c=o.y1,u=o.y2,l=o.xAxis,s=o.yAxis;if(!l||!s)return null;var f=rk({x:l.scale,y:s.scale}),p={x:t?f.x.apply(i,{position:"start"}):f.x.rangeMin,y:r?f.y.apply(c,{position:"start"}):f.y.rangeMin},h={x:e?f.x.apply(a,{position:"end"}):f.x.rangeMax,y:n?f.y.apply(u,{position:"end"}):f.y.rangeMax};return!sS(o,"discard")||f.isInRange(p)&&f.isInRange(h)?tk(p,h):null}(s,f,p,h,this.props);if(!d&&!y)return null;var v=sS(this.props,"hidden")?"url(#".concat(l,")"):void 0;return n.createElement(yo,{className:St("recharts-reference-area",c)},t.renderRect(y,rP(rP({clipPath:v},no(this.props,!0)),d)),hw.renderCallByParent(this.props,d))}}])}();function fP(t,e,r){if(e<1)return[];if(1===e&&void 0===r)return t;for(var n=[],o=0;o<t.length;o+=e)n.push(t[o]);return n}function pP(t,e,r){return function(t){var e=t.width,r=t.height,n=function(t){return(t%180+180)%180}(arguments.length>1&&void 0!==arguments[1]?arguments[1]:0),o=n*Math.PI/180,i=Math.atan(r/e),a=o>i&&o<Math.PI-i?r/Math.sin(o):e/Math.cos(o);return Math.abs(a)}({width:t.width+e.width,height:t.height+e.height},r)}function hP(t,e,r,n,o){if(t*e<t*n||t*e>t*o)return!1;var i=r();return t*(e-t*i/2-n)>=0&&t*(e+t*i/2-o)<=0}function yP(t){return(yP="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function dP(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function vP(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?dP(Object(r),!0).forEach(function(e){mP(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):dP(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function mP(t,e,r){var n;return n=function(t,e){if("object"!=yP(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=yP(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(e,"string"),(e="symbol"==yP(n)?n:n+"")in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function bP(t,e,r){var n=t.tick,o=t.ticks,i=t.viewBox,a=t.minTickGap,c=t.orientation,u=t.interval,l=t.tickFormatter,s=t.unit,f=t.angle;if(!o||!o.length||!n)return[];if(An(u)||_f.isSsr)return function(t,e){return fP(t,e+1)}(o,"number"==typeof u&&An(u)?u:0);var p=[],h="top"===c||"bottom"===c?"width":"height",y=s&&"width"===h?Sp(s,{fontSize:e,letterSpacing:r}):{width:0,height:0},d=function(t,n){var o=re(l)?l(t.value,n):t.value;return"width"===h?pP(Sp(o,{fontSize:e,letterSpacing:r}),y,f):Sp(o,{fontSize:e,letterSpacing:r})[h]},v=o.length>=2?kn(o[1].coordinate-o[0].coordinate):1,m=function(t,e,r){var n="width"===r,o=t.x,i=t.y,a=t.width,c=t.height;return 1===e?{start:n?o:i,end:n?o+a:i+c}:{start:n?o+a:i+c,end:n?o:i}}(i,v,h);return"equidistantPreserveStart"===u?function(t,e,r,n,o){for(var i,a=(n||[]).slice(),c=e.start,u=e.end,l=0,s=1,f=c,p=function(){var e=null==n?void 0:n[l];if(void 0===e)return{v:fP(n,s)};var i,a=l,p=function(){return void 0===i&&(i=r(e,a)),i},h=e.coordinate,y=0===l||hP(t,h,p,f,u);y||(l=0,f=c,s+=1),y&&(f=h+t*(p()/2+o),l+=s)};s<=a.length;)if(i=p())return i.v;return[]}(v,m,d,o,a):(p="preserveStart"===u||"preserveStartEnd"===u?function(t,e,r,n,o,i){var a=(n||[]).slice(),c=a.length,u=e.start,l=e.end;if(i){var s=n[c-1],f=r(s,c-1),p=t*(s.coordinate+t*f/2-l);a[c-1]=s=vP(vP({},s),{},{tickCoord:p>0?s.coordinate-p*t:s.coordinate}),hP(t,s.tickCoord,function(){return f},u,l)&&(l=s.tickCoord-t*(f/2+o),a[c-1]=vP(vP({},s),{},{isShow:!0}))}for(var h=i?c-1:c,y=function(e){var n,i=a[e],c=function(){return void 0===n&&(n=r(i,e)),n};if(0===e){var s=t*(i.coordinate-t*c()/2-u);a[e]=i=vP(vP({},i),{},{tickCoord:s<0?i.coordinate-s*t:i.coordinate})}else a[e]=i=vP(vP({},i),{},{tickCoord:i.coordinate});hP(t,i.tickCoord,c,u,l)&&(u=i.tickCoord+t*(c()/2+o),a[e]=vP(vP({},i),{},{isShow:!0}))},d=0;d<h;d++)y(d);return a}(v,m,d,o,a,"preserveStartEnd"===u):function(t,e,r,n,o){for(var i=(n||[]).slice(),a=i.length,c=e.start,u=e.end,l=function(e){var n,l=i[e],s=function(){return void 0===n&&(n=r(l,e)),n};if(e===a-1){var f=t*(l.coordinate+t*s()/2-u);i[e]=l=vP(vP({},l),{},{tickCoord:f>0?l.coordinate-f*t:l.coordinate})}else i[e]=l=vP(vP({},l),{},{tickCoord:l.coordinate});hP(t,l.tickCoord,s,c,u)&&(u=l.tickCoord-t*(s()/2+o),i[e]=vP(vP({},l),{},{isShow:!0}))},s=a-1;s>=0;s--)l(s);return i}(v,m,d,o,a),p.filter(function(t){return t.isShow}))}uP(sP,"displayName","ReferenceArea"),uP(sP,"defaultProps",{isFront:!1,ifOverflow:"discard",xAxisId:0,yAxisId:0,r:10,fill:"#ccc",fillOpacity:.5,stroke:"none",strokeWidth:1}),uP(sP,"renderRect",function(t,e){return n.isValidElement(t)?n.cloneElement(t,e):re(t)?t(e):n.createElement(IO,Qk({},e,{className:"recharts-reference-area-rect"}))});var gP=["viewBox"],wP=["viewBox"],xP=["ticks"];function OP(t){return(OP="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function jP(){return jP=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},jP.apply(this,arguments)}function SP(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function kP(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?SP(Object(r),!0).forEach(function(e){CP(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):SP(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function PP(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],e.indexOf(r)>=0||Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}function AP(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,DP(n.key),n)}}function EP(t,e,r){return e=_P(e),function(t,e){if(e&&("object"===OP(e)||"function"==typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(t,MP()?Reflect.construct(e,r||[],_P(t).constructor):e.apply(t,r))}function MP(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(e){}return(MP=function(){return!!t})()}function _P(t){return(_P=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function TP(t,e){return(TP=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function CP(t,e,r){return(e=DP(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function DP(t){var e=function(t,e){if("object"!=OP(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=OP(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==OP(e)?e:e+""}var IP=function(){function e(t){var r;return function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,e),(r=EP(this,e,[t])).state={fontSize:"",letterSpacing:""},r}return function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&TP(t,e)}(e,t.Component),r=e,i=[{key:"renderTickItem",value:function(t,e,r){return n.isValidElement(t)?n.cloneElement(t,e):re(t)?t(e):n.createElement(Qp,jP({},e,{className:"recharts-cartesian-axis-tick-value"}),r)}}],(o=[{key:"shouldComponentUpdate",value:function(t,e){var r=t.viewBox,n=PP(t,gP),o=this.props,i=o.viewBox,a=PP(o,wP);return!Bn(r,i)||!Bn(n,a)||!Bn(e,this.state)}},{key:"componentDidMount",value:function(){var t=this.layerReference;if(t){var e=t.getElementsByClassName("recharts-cartesian-axis-tick-value")[0];e&&this.setState({fontSize:window.getComputedStyle(e).fontSize,letterSpacing:window.getComputedStyle(e).letterSpacing})}}},{key:"getTickLineCoord",value:function(t){var e,r,n,o,i,a,c=this.props,u=c.x,l=c.y,s=c.width,f=c.height,p=c.orientation,h=c.tickSize,y=c.mirror,d=c.tickMargin,v=y?-1:1,m=t.tickSize||h,b=An(t.tickCoord)?t.tickCoord:t.coordinate;switch(p){case"top":e=r=t.coordinate,a=(n=(o=l+ +!y*f)-v*m)-v*d,i=b;break;case"left":n=o=t.coordinate,i=(e=(r=u+ +!y*s)-v*m)-v*d,a=b;break;case"right":n=o=t.coordinate,i=(e=(r=u+ +y*s)+v*m)+v*d,a=b;break;default:e=r=t.coordinate,a=(n=(o=l+ +y*f)+v*m)+v*d,i=b}return{line:{x1:e,y1:n,x2:r,y2:o},tick:{x:i,y:a}}}},{key:"getTickTextAnchor",value:function(){var t,e=this.props,r=e.orientation,n=e.mirror;switch(r){case"left":t=n?"start":"end";break;case"right":t=n?"end":"start";break;default:t="middle"}return t}},{key:"getTickVerticalAnchor",value:function(){var t=this.props,e=t.orientation,r=t.mirror,n="end";switch(e){case"left":case"right":n="middle";break;case"top":n=r?"start":"end";break;default:n=r?"end":"start"}return n}},{key:"renderAxisLine",value:function(){var t=this.props,e=t.x,r=t.y,o=t.width,i=t.height,a=t.orientation,c=t.mirror,u=t.axisLine,l=kP(kP(kP({},no(this.props,!1)),no(u,!1)),{},{fill:"none"});if("top"===a||"bottom"===a){var s=+("top"===a&&!c||"bottom"===a&&c);l=kP(kP({},l),{},{x1:e,y1:r+s*i,x2:e+o,y2:r+s*i})}else{var f=+("left"===a&&!c||"right"===a&&c);l=kP(kP({},l),{},{x1:e+f*o,y1:r,x2:e+f*o,y2:r+i})}return n.createElement("line",jP({},l,{className:St("recharts-cartesian-axis-line",Xr(u,"className"))}))}},{key:"renderTicks",value:function(t,r,o){var i=this,a=this.props,c=a.tickLine,u=a.stroke,l=a.tick,s=a.tickFormatter,f=a.unit,p=bP(kP(kP({},this.props),{},{ticks:t}),r,o),h=this.getTickTextAnchor(),y=this.getTickVerticalAnchor(),d=no(this.props,!1),v=no(l,!1),m=kP(kP({},d),{},{fill:"none"},no(c,!1)),b=p.map(function(t,r){var o=i.getTickLineCoord(t),a=o.line,b=o.tick,g=kP(kP(kP(kP({textAnchor:h,verticalAnchor:y},d),{},{stroke:"none",fill:u},v),b),{},{index:r,payload:t,visibleTicksCount:p.length,tickFormatter:s});return n.createElement(yo,jP({className:"recharts-cartesian-axis-tick",key:"tick-".concat(t.value,"-").concat(t.coordinate,"-").concat(t.tickCoord)},Fn(i.props,t,r)),c&&n.createElement("line",jP({},m,a,{className:St("recharts-cartesian-axis-tick-line",Xr(c,"className"))})),l&&e.renderTickItem(l,g,"".concat(re(s)?s(t.value,r):t.value).concat(f||"")))});return n.createElement("g",{className:"recharts-cartesian-axis-ticks"},b)}},{key:"render",value:function(){var t=this,e=this.props,r=e.axisLine,o=e.width,i=e.height,a=e.ticksGenerator,c=e.className;if(e.hide)return null;var u=this.props,l=u.ticks,s=PP(u,xP),f=l;return re(a)&&(f=l&&l.length>0?a(this.props):a(s)),o<=0||i<=0||!f||!f.length?null:n.createElement(yo,{className:St("recharts-cartesian-axis",c),ref:function(e){t.layerReference=e}},r&&this.renderAxisLine(),this.renderTicks(f,this.state.fontSize,this.state.letterSpacing),hw.renderCallByParent(this.props))}}])&&AP(r.prototype,o),i&&AP(r,i),Object.defineProperty(r,"prototype",{writable:!1}),r;var r,o,i}();CP(IP,"displayName","CartesianAxis"),CP(IP,"defaultProps",{x:0,y:0,width:0,height:0,viewBox:{x:0,y:0,width:0,height:0},orientation:"bottom",ticks:[],stroke:"#666",tickLine:!0,axisLine:!0,tick:!0,mirror:!1,minTickGap:5,tickSize:6,tickMargin:2,interval:"preserveEnd"});var NP=["x1","y1","x2","y2","key"],BP=["offset"];function LP(t){return(LP="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function RP(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function zP(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?RP(Object(r),!0).forEach(function(e){UP(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):RP(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function UP(t,e,r){var n;return n=function(t,e){if("object"!=LP(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=LP(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(e,"string"),(e="symbol"==LP(n)?n:n+"")in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function $P(){return $P=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},$P.apply(this,arguments)}function qP(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],e.indexOf(r)>=0||Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}var FP=function(t){var e=t.fill;if(!e||"none"===e)return null;var r=t.fillOpacity,o=t.x,i=t.y,a=t.width,c=t.height,u=t.ry;return n.createElement("rect",{x:o,y:i,ry:u,width:a,height:c,stroke:"none",fill:e,fillOpacity:r,className:"recharts-cartesian-grid-bg"})};function WP(t,e){var r;if(n.isValidElement(t))r=n.cloneElement(t,e);else if(re(t))r=t(e);else{var o=e.x1,i=e.y1,a=e.x2,c=e.y2,u=e.key,l=qP(e,NP),s=no(l,!1);s.offset;var f=qP(s,BP);r=n.createElement("line",$P({},f,{x1:o,y1:i,x2:a,y2:c,fill:"none",key:u}))}return r}function HP(t){var e=t.x,r=t.width,o=t.horizontal,i=void 0===o||o,a=t.horizontalPoints;if(!i||!a||!a.length)return null;var c=a.map(function(n,o){var a=zP(zP({},t),{},{x1:e,y1:n,x2:e+r,y2:n,key:"line-".concat(o),index:o});return WP(i,a)});return n.createElement("g",{className:"recharts-cartesian-grid-horizontal"},c)}function VP(t){var e=t.y,r=t.height,o=t.vertical,i=void 0===o||o,a=t.verticalPoints;if(!i||!a||!a.length)return null;var c=a.map(function(n,o){var a=zP(zP({},t),{},{x1:n,y1:e,x2:n,y2:e+r,key:"line-".concat(o),index:o});return WP(i,a)});return n.createElement("g",{className:"recharts-cartesian-grid-vertical"},c)}function XP(t){var e=t.horizontalFill,r=t.fillOpacity,o=t.x,i=t.y,a=t.width,c=t.height,u=t.horizontalPoints,l=t.horizontal;if(!(void 0===l||l)||!e||!e.length)return null;var s=u.map(function(t){return Math.round(t+i-i)}).sort(function(t,e){return t-e});i!==s[0]&&s.unshift(0);var f=s.map(function(t,u){var l=!s[u+1]?i+c-t:s[u+1]-t;if(l<=0)return null;var f=u%e.length;return n.createElement("rect",{key:"react-".concat(u),y:t,x:o,height:l,width:a,stroke:"none",fill:e[f],fillOpacity:r,className:"recharts-cartesian-grid-bg"})});return n.createElement("g",{className:"recharts-cartesian-gridstripes-horizontal"},f)}function GP(t){var e=t.vertical,r=void 0===e||e,o=t.verticalFill,i=t.fillOpacity,a=t.x,c=t.y,u=t.width,l=t.height,s=t.verticalPoints;if(!r||!o||!o.length)return null;var f=s.map(function(t){return Math.round(t+a-a)}).sort(function(t,e){return t-e});a!==f[0]&&f.unshift(0);var p=f.map(function(t,e){var r=!f[e+1]?a+u-t:f[e+1]-t;if(r<=0)return null;var s=e%o.length;return n.createElement("rect",{key:"react-".concat(e),x:t,y:c,width:r,height:l,stroke:"none",fill:o[s],fillOpacity:i,className:"recharts-cartesian-grid-bg"})});return n.createElement("g",{className:"recharts-cartesian-gridstripes-vertical"},p)}var YP=function(t,e){var r=t.xAxis,n=t.width,o=t.height,i=t.offset;return Tg(bP(zP(zP(zP({},IP.defaultProps),r),{},{ticks:Cg(r,!0),viewBox:{x:0,y:0,width:n,height:o}})),i.left,i.left+i.width,e)},KP=function(t,e){var r=t.yAxis,n=t.width,o=t.height,i=t.offset;return Tg(bP(zP(zP(zP({},IP.defaultProps),r),{},{ticks:Cg(r,!0),viewBox:{x:0,y:0,width:n,height:o}})),i.top,i.top+i.height,e)},ZP={horizontal:!0,vertical:!0,horizontalPoints:[],verticalPoints:[],stroke:"#ccc",fill:"none",verticalFill:[],horizontalFill:[]};function JP(e){var r,o,i,a,c,u,l,s,f=Sk(),p=kk(),h=t.useContext(mk),y=zP(zP({},e),{},{stroke:null!==(r=e.stroke)&&void 0!==r?r:ZP.stroke,fill:null!==(o=e.fill)&&void 0!==o?o:ZP.fill,horizontal:null!==(i=e.horizontal)&&void 0!==i?i:ZP.horizontal,horizontalFill:null!==(a=e.horizontalFill)&&void 0!==a?a:ZP.horizontalFill,vertical:null!==(c=e.vertical)&&void 0!==c?c:ZP.vertical,verticalFill:null!==(u=e.verticalFill)&&void 0!==u?u:ZP.verticalFill,x:An(e.x)?e.x:h.left,y:An(e.y)?e.y:h.top,width:An(e.width)?e.width:h.width,height:An(e.height)?e.height:h.height}),d=y.x,v=y.y,m=y.width,b=y.height,g=y.syncWithTicks,w=y.horizontalValues,x=y.verticalValues,O=(l=t.useContext(yk),Cn(l)),j=(s=t.useContext(dk),pk(s,function(t){return OS(t.domain,Number.isFinite)})||Cn(s));if(!An(m)||m<=0||!An(b)||b<=0||!An(d)||d!==+d||!An(v)||v!==+v)return null;var S=y.verticalCoordinatesGenerator||YP,k=y.horizontalCoordinatesGenerator||KP,P=y.horizontalPoints,A=y.verticalPoints;if((!P||!P.length)&&re(k)){var E=w&&w.length,M=k({yAxis:j?zP(zP({},j),{},{ticks:E?w:j.ticks}):void 0,width:f,height:p,offset:h},!!E||g);vo(Array.isArray(M),"horizontalCoordinatesGenerator should return Array but instead it returned [".concat(LP(M),"]")),Array.isArray(M)&&(P=M)}if((!A||!A.length)&&re(S)){var _=x&&x.length,T=S({xAxis:O?zP(zP({},O),{},{ticks:_?x:O.ticks}):void 0,width:f,height:p,offset:h},!!_||g);vo(Array.isArray(T),"verticalCoordinatesGenerator should return Array but instead it returned [".concat(LP(T),"]")),Array.isArray(T)&&(A=T)}return n.createElement("g",{className:"recharts-cartesian-grid"},n.createElement(FP,{fill:y.fill,fillOpacity:y.fillOpacity,x:y.x,y:y.y,width:y.width,height:y.height,ry:y.ry}),n.createElement(HP,$P({},y,{offset:h,horizontalPoints:P,xAxis:O,yAxis:j})),n.createElement(VP,$P({},y,{offset:h,verticalPoints:A,xAxis:O,yAxis:j})),n.createElement(XP,$P({},y,{horizontalPoints:P})),n.createElement(GP,$P({},y,{verticalPoints:A})))}JP.displayName="CartesianGrid";var QP=["type","layout","connectNulls","ref"],tA=["key"];function eA(t){return(eA="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function rA(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],e.indexOf(r)>=0||Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}function nA(){return nA=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},nA.apply(this,arguments)}function oA(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function iA(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?oA(Object(r),!0).forEach(function(e){hA(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):oA(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function aA(t){return function(t){if(Array.isArray(t))return cA(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||function(t,e){if(!t)return;if("string"==typeof t)return cA(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);"Object"===r&&t.constructor&&(r=t.constructor.name);if("Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return cA(t,e)}(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function cA(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}function uA(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,yA(n.key),n)}}function lA(t,e,r){return e=fA(e),function(t,e){if(e&&("object"===eA(e)||"function"==typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(t,sA()?Reflect.construct(e,r||[],fA(t).constructor):e.apply(t,r))}function sA(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(e){}return(sA=function(){return!!t})()}function fA(t){return(fA=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function pA(t,e){return(pA=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function hA(t,e,r){return(e=yA(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function yA(t){var e=function(t,e){if("object"!=eA(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=eA(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==eA(e)?e:e+""}var dA=function(){function e(){var t;!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,e);for(var r=arguments.length,n=new Array(r),o=0;o<r;o++)n[o]=arguments[o];return hA(t=lA(this,e,[].concat(n)),"state",{isAnimationFinished:!0,totalLength:0}),hA(t,"generateSimpleStrokeDasharray",function(t,e){return"".concat(e,"px ").concat(t-e,"px")}),hA(t,"getStrokeDasharray",function(r,n,o){var i=o.reduce(function(t,e){return t+e});if(!i)return t.generateSimpleStrokeDasharray(n,r);for(var a=Math.floor(r/i),c=r%i,u=n-r,l=[],s=0,f=0;s<o.length;f+=o[s],++s)if(f+o[s]>c){l=[].concat(aA(o.slice(0,s)),[c-f]);break}var p=l.length%2==0?[0,u]:[u];return[].concat(aA(e.repeat(o,a)),aA(l),p).map(function(t){return"".concat(t,"px")}).join(", ")}),hA(t,"id",_n("recharts-line-")),hA(t,"pathRef",function(e){t.mainCurve=e}),hA(t,"handleAnimationEnd",function(){t.setState({isAnimationFinished:!0}),t.props.onAnimationEnd&&t.props.onAnimationEnd()}),hA(t,"handleAnimationStart",function(){t.setState({isAnimationFinished:!1}),t.props.onAnimationStart&&t.props.onAnimationStart()}),t}return function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&pA(t,e)}(e,t.PureComponent),r=e,i=[{key:"getDerivedStateFromProps",value:function(t,e){return t.animationId!==e.prevAnimationId?{prevAnimationId:t.animationId,curPoints:t.points,prevPoints:e.curPoints}:t.points!==e.curPoints?{curPoints:t.points}:null}},{key:"repeat",value:function(t,e){for(var r=t.length%2!=0?[].concat(aA(t),[0]):t,n=[],o=0;o<e;++o)n=[].concat(aA(n),aA(r));return n}},{key:"renderDotItem",value:function(t,e){var r;if(n.isValidElement(t))r=n.cloneElement(t,e);else if(re(t))r=t(e);else{var o=e.key,i=rA(e,tA),a=St("recharts-line-dot","boolean"!=typeof t?t.className:"");r=n.createElement(BO,nA({key:o},i,{className:a}))}return r}}],(o=[{key:"componentDidMount",value:function(){if(this.props.isAnimationActive){var t=this.getTotalLength();this.setState({totalLength:t})}}},{key:"componentDidUpdate",value:function(){if(this.props.isAnimationActive){var t=this.getTotalLength();t!==this.state.totalLength&&this.setState({totalLength:t})}}},{key:"getTotalLength",value:function(){var t=this.mainCurve;try{return t&&t.getTotalLength&&t.getTotalLength()||0}catch(e){return 0}}},{key:"renderErrorBar",value:function(t,e){if(this.props.isAnimationActive&&!this.state.isAnimationFinished)return null;var r=this.props,o=r.points,i=r.xAxis,a=r.yAxis,c=r.layout,u=Qn(r.children,hg);if(!u)return null;var l=function(t,e){return{x:t.x,y:t.y,value:t.value,errorVal:kg(t.payload,e)}},s={clipPath:t?"url(#clipPath-".concat(e,")"):null};return n.createElement(yo,s,u.map(function(t){return n.cloneElement(t,{key:"bar-".concat(t.props.dataKey),data:o,xAxis:i,yAxis:a,layout:c,dataPointFormatter:l})}))}},{key:"renderDots",value:function(t,r,o){if(this.props.isAnimationActive&&!this.state.isAnimationFinished)return null;var i=this.props,a=i.dot,c=i.points,u=i.dataKey,l=no(this.props,!1),s=no(a,!0),f=c.map(function(t,r){var n=iA(iA(iA({key:"dot-".concat(r),r:3},l),s),{},{index:r,cx:t.x,cy:t.y,value:t.value,dataKey:u,payload:t.payload,points:c});return e.renderDotItem(a,n)}),p={clipPath:t?"url(#clipPath-".concat(r?"":"dots-").concat(o,")"):null};return n.createElement(yo,nA({className:"recharts-line-dots",key:"dots"},p),f)}},{key:"renderCurveStatically",value:function(t,e,r,o){var i=this.props,a=i.type,c=i.layout,u=i.connectNulls;i.ref;var l=rA(i,QP),s=iA(iA(iA({},no(l,!0)),{},{fill:"none",className:"recharts-line-curve",clipPath:e?"url(#clipPath-".concat(r,")"):null,points:t},o),{},{type:a,layout:c,connectNulls:u});return n.createElement(Xw,nA({},s,{pathRef:this.pathRef}))}},{key:"renderCurveWithAnimation",value:function(t,e){var r=this,o=this.props,i=o.points,a=o.strokeDasharray,c=o.isAnimationActive,u=o.animationBegin,l=o.animationDuration,s=o.animationEasing,f=o.animationId,p=o.animateNewValues,h=o.width,y=o.height,d=this.state,v=d.prevPoints,m=d.totalLength;return n.createElement(jO,{begin:u,duration:l,isActive:c,easing:s,from:{t:0},to:{t:1},key:"line-".concat(f),onAnimationEnd:this.handleAnimationEnd,onAnimationStart:this.handleAnimationStart},function(n){var o=n.t;if(v){var c=v.length/i.length,u=i.map(function(t,e){var r=Math.floor(e*c);if(v[r]){var n=v[r],i=Dn(n.x,t.x),a=Dn(n.y,t.y);return iA(iA({},t),{},{x:i(o),y:a(o)})}if(p){var u=Dn(2*h,t.x),l=Dn(y/2,t.y);return iA(iA({},t),{},{x:u(o),y:l(o)})}return iA(iA({},t),{},{x:t.x,y:t.y})});return r.renderCurveStatically(u,t,e)}var l,s=Dn(0,m)(o);if(a){var f="".concat(a).split(/[,\s]+/gim).map(function(t){return parseFloat(t)});l=r.getStrokeDasharray(s,m,f)}else l=r.generateSimpleStrokeDasharray(m,s);return r.renderCurveStatically(i,t,e,{strokeDasharray:l})})}},{key:"renderCurve",value:function(t,e){var r=this.props,n=r.points,o=r.isAnimationActive,i=this.state,a=i.prevPoints,c=i.totalLength;return o&&n&&n.length&&(!a&&c>0||!Qm(a,n))?this.renderCurveWithAnimation(t,e):this.renderCurveStatically(n,t,e)}},{key:"render",value:function(){var t,e=this.props,r=e.hide,o=e.dot,i=e.points,a=e.className,c=e.xAxis,u=e.yAxis,l=e.top,s=e.left,f=e.width,p=e.height,h=e.isAnimationActive,y=e.id;if(r||!i||!i.length)return null;var d=this.state.isAnimationFinished,v=1===i.length,m=St("recharts-line",a),b=c&&c.allowDataOverflow,g=u&&u.allowDataOverflow,w=b||g,x=Gr(y)?this.id:y,O=null!==(t=no(o,!1))&&void 0!==t?t:{r:3,strokeWidth:2},j=O.r,S=void 0===j?3:j,k=O.strokeWidth,P=void 0===k?2:k,A=(function(t){return t&&"object"===Xn(t)&&"clipDot"in t}(o)?o:{}).clipDot,E=void 0===A||A,M=2*S+P;return n.createElement(yo,{className:m},b||g?n.createElement("defs",null,n.createElement("clipPath",{id:"clipPath-".concat(x)},n.createElement("rect",{x:b?s:s-f/2,y:g?l:l-p/2,width:b?f:2*f,height:g?p:2*p})),!E&&n.createElement("clipPath",{id:"clipPath-dots-".concat(x)},n.createElement("rect",{x:s-M/2,y:l-M/2,width:f+M,height:p+M}))):null,!v&&this.renderCurve(w,x),this.renderErrorBar(w,x),(v||o)&&this.renderDots(w,E,x),(!h||d)&&Aw.renderCallByParent(this.props,i))}}])&&uA(r.prototype,o),i&&uA(r,i),Object.defineProperty(r,"prototype",{writable:!1}),r;var r,o,i}();function vA(t){return(vA="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function mA(t,e,r){return e&&function(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,jA(n.key),n)}}(t.prototype,e),Object.defineProperty(t,"prototype",{writable:!1}),t}function bA(t,e,r){return e=wA(e),function(t,e){if(e&&("object"===vA(e)||"function"==typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(t,gA()?Reflect.construct(e,r||[],wA(t).constructor):e.apply(t,r))}function gA(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(e){}return(gA=function(){return!!t})()}function wA(t){return(wA=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function xA(t,e){return(xA=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function OA(t,e,r){return(e=jA(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function jA(t){var e=function(t,e){if("object"!=vA(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=vA(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==vA(e)?e:e+""}function SA(){return SA=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},SA.apply(this,arguments)}function kA(t){var e=t.xAxisId,r=Sk(),o=kk(),i=Ok(e);return null==i?null:n.createElement(IP,SA({},i,{className:St("recharts-".concat(i.axisType," ").concat(i.axisType),i.className),viewBox:{x:0,y:0,width:r,height:o},ticksGenerator:function(t){return Cg(t,!0)}}))}hA(dA,"displayName","Line"),hA(dA,"defaultProps",{xAxisId:0,yAxisId:0,connectNulls:!1,activeDot:!0,dot:!0,legendType:"line",stroke:"#3182bd",strokeWidth:1,fill:"#fff",points:[],isAnimationActive:!_f.isSsr,animateNewValues:!0,animationBegin:0,animationDuration:1500,animationEasing:"ease",hide:!1,label:!1}),hA(dA,"getComposedData",function(t){var e=t.props,r=t.xAxis,n=t.yAxis,o=t.xAxisTicks,i=t.yAxisTicks,a=t.dataKey,c=t.bandSize,u=t.displayedData,l=t.offset,s=e.layout;return iA({points:u.map(function(t,e){var u=kg(t,a);return"horizontal"===s?{x:Rg({axis:r,ticks:o,bandSize:c,entry:t,index:e}),y:Gr(u)?null:n.scale(u),value:u,payload:t}:{x:Gr(u)?null:r.scale(u),y:Rg({axis:n,ticks:i,bandSize:c,entry:t,index:e}),value:u,payload:t}}),layout:s},l)});var PA=function(){function t(){return function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t),bA(this,t,arguments)}return function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&xA(t,e)}(t,n.Component),mA(t,[{key:"render",value:function(){return n.createElement(kA,this.props)}}])}();function AA(t){return(AA="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function EA(t,e,r){return e&&function(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,IA(n.key),n)}}(t.prototype,e),Object.defineProperty(t,"prototype",{writable:!1}),t}function MA(t,e,r){return e=TA(e),function(t,e){if(e&&("object"===AA(e)||"function"==typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(t,_A()?Reflect.construct(e,r||[],TA(t).constructor):e.apply(t,r))}function _A(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(e){}return(_A=function(){return!!t})()}function TA(t){return(TA=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function CA(t,e){return(CA=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function DA(t,e,r){return(e=IA(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function IA(t){var e=function(t,e){if("object"!=AA(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=AA(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==AA(e)?e:e+""}function NA(){return NA=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},NA.apply(this,arguments)}OA(PA,"displayName","XAxis"),OA(PA,"defaultProps",{allowDecimals:!0,hide:!1,orientation:"bottom",width:0,height:30,mirror:!1,xAxisId:0,tickCount:5,type:"category",padding:{left:0,right:0},allowDataOverflow:!1,scale:"auto",reversed:!1,allowDuplicatedCategory:!0});var BA=function(t){var e=t.yAxisId,r=Sk(),o=kk(),i=jk(e);return null==i?null:n.createElement(IP,NA({},i,{className:St("recharts-".concat(i.axisType," ").concat(i.axisType),i.className),viewBox:{x:0,y:0,width:r,height:o},ticksGenerator:function(t){return Cg(t,!0)}}))},LA=function(){function t(){return function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t),MA(this,t,arguments)}return function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&CA(t,e)}(t,n.Component),EA(t,[{key:"render",value:function(){return n.createElement(BA,this.props)}}])}();function RA(t){return function(t){if(Array.isArray(t))return zA(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||function(t,e){if(!t)return;if("string"==typeof t)return zA(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);"Object"===r&&t.constructor&&(r=t.constructor.name);if("Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return zA(t,e)}(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function zA(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}DA(LA,"displayName","YAxis"),DA(LA,"defaultProps",{allowDuplicatedCategory:!0,allowDecimals:!0,hide:!1,orientation:"left",width:60,height:0,mirror:!1,yAxisId:0,tickCount:5,type:"number",padding:{top:0,bottom:0},allowDataOverflow:!1,scale:"auto",reversed:!1});var UA=function(t,e,r,n,o){var i=Qn(t,Uk),a=Qn(t,Jk),c=[].concat(RA(i),RA(a)),u=Qn(t,sP),l="".concat(n,"Id"),s=n[0],f=e;if(c.length&&(f=c.reduce(function(t,e){if(e.props[l]===r&&sS(e.props,"extendDomain")&&An(e.props[s])){var n=e.props[s];return[Math.min(t[0],n),Math.max(t[1],n)]}return t},f)),u.length){var p="".concat(s,"1"),h="".concat(s,"2");f=u.reduce(function(t,e){if(e.props[l]===r&&sS(e.props,"extendDomain")&&An(e.props[p])&&An(e.props[h])){var n=e.props[p],o=e.props[h];return[Math.min(t[0],n,o),Math.max(t[1],n,o)]}return t},f)}return o&&o.length&&(f=o.reduce(function(t,e){return An(e)?[Math.min(t[0],e),Math.max(t[1],e)]:t},f)),f},$A={exports:{}};!function(t){var e=Object.prototype.hasOwnProperty,r="~";function n(){}function o(t,e,r){this.fn=t,this.context=e,this.once=r||!1}function i(t,e,n,i,a){if("function"!=typeof n)throw new TypeError("The listener must be a function");var c=new o(n,i||t,a),u=r?r+e:e;return t._events[u]?t._events[u].fn?t._events[u]=[t._events[u],c]:t._events[u].push(c):(t._events[u]=c,t._eventsCount++),t}function a(t,e){0===--t._eventsCount?t._events=new n:delete t._events[e]}function c(){this._events=new n,this._eventsCount=0}Object.create&&(n.prototype=Object.create(null),(new n).__proto__||(r=!1)),c.prototype.eventNames=function(){var t,n,o=[];if(0===this._eventsCount)return o;for(n in t=this._events)e.call(t,n)&&o.push(r?n.slice(1):n);return Object.getOwnPropertySymbols?o.concat(Object.getOwnPropertySymbols(t)):o},c.prototype.listeners=function(t){var e=r?r+t:t,n=this._events[e];if(!n)return[];if(n.fn)return[n.fn];for(var o=0,i=n.length,a=new Array(i);o<i;o++)a[o]=n[o].fn;return a},c.prototype.listenerCount=function(t){var e=r?r+t:t,n=this._events[e];return n?n.fn?1:n.length:0},c.prototype.emit=function(t,e,n,o,i,a){var c=r?r+t:t;if(!this._events[c])return!1;var u,l,s=this._events[c],f=arguments.length;if(s.fn){switch(s.once&&this.removeListener(t,s.fn,void 0,!0),f){case 1:return s.fn.call(s.context),!0;case 2:return s.fn.call(s.context,e),!0;case 3:return s.fn.call(s.context,e,n),!0;case 4:return s.fn.call(s.context,e,n,o),!0;case 5:return s.fn.call(s.context,e,n,o,i),!0;case 6:return s.fn.call(s.context,e,n,o,i,a),!0}for(l=1,u=new Array(f-1);l<f;l++)u[l-1]=arguments[l];s.fn.apply(s.context,u)}else{var p,h=s.length;for(l=0;l<h;l++)switch(s[l].once&&this.removeListener(t,s[l].fn,void 0,!0),f){case 1:s[l].fn.call(s[l].context);break;case 2:s[l].fn.call(s[l].context,e);break;case 3:s[l].fn.call(s[l].context,e,n);break;case 4:s[l].fn.call(s[l].context,e,n,o);break;default:if(!u)for(p=1,u=new Array(f-1);p<f;p++)u[p-1]=arguments[p];s[l].fn.apply(s[l].context,u)}}return!0},c.prototype.on=function(t,e,r){return i(this,t,e,r,!1)},c.prototype.once=function(t,e,r){return i(this,t,e,r,!0)},c.prototype.removeListener=function(t,e,n,o){var i=r?r+t:t;if(!this._events[i])return this;if(!e)return a(this,i),this;var c=this._events[i];if(c.fn)c.fn!==e||o&&!c.once||n&&c.context!==n||a(this,i);else{for(var u=0,l=[],s=c.length;u<s;u++)(c[u].fn!==e||o&&!c[u].once||n&&c[u].context!==n)&&l.push(c[u]);l.length?this._events[i]=1===l.length?l[0]:l:a(this,i)}return this},c.prototype.removeAllListeners=function(t){var e;return t?(e=r?r+t:t,this._events[e]&&a(this,e)):(this._events=new n,this._eventsCount=0),this},c.prototype.off=c.prototype.removeListener,c.prototype.addListener=c.prototype.on,c.prefixed=r,c.EventEmitter=c,t.exports=c}($A);var qA=new(r($A.exports)),FA="recharts.syncMouseEvents";function WA(t){return(WA="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function HA(t,e,r){return e&&function(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,XA(n.key),n)}}(t.prototype,e),Object.defineProperty(t,"prototype",{writable:!1}),t}function VA(t,e,r){return(e=XA(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function XA(t){var e=function(t,e){if("object"!=WA(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e);if("object"!=WA(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(t)}(t,"string");return"symbol"==WA(e)?e:e+""}var GA=function(){return HA(function t(){!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t),VA(this,"activeIndex",0),VA(this,"coordinateList",[]),VA(this,"layout","horizontal")},[{key:"setDetails",value:function(t){var e,r=t.coordinateList,n=void 0===r?null:r,o=t.container,i=void 0===o?null:o,a=t.layout,c=void 0===a?null:a,u=t.offset,l=void 0===u?null:u,s=t.mouseHandlerCallback,f=void 0===s?null:s;this.coordinateList=null!==(e=null!=n?n:this.coordinateList)&&void 0!==e?e:[],this.container=null!=i?i:this.container,this.layout=null!=c?c:this.layout,this.offset=null!=l?l:this.offset,this.mouseHandlerCallback=null!=f?f:this.mouseHandlerCallback,this.activeIndex=Math.min(Math.max(this.activeIndex,0),this.coordinateList.length-1)}},{key:"focus",value:function(){this.spoofMouse()}},{key:"keyboardEvent",value:function(t){if(0!==this.coordinateList.length)switch(t.key){case"ArrowRight":if("horizontal"!==this.layout)return;this.activeIndex=Math.min(this.activeIndex+1,this.coordinateList.length-1),this.spoofMouse();break;case"ArrowLeft":if("horizontal"!==this.layout)return;this.activeIndex=Math.max(this.activeIndex-1,0),this.spoofMouse()}}},{key:"setIndex",value:function(t){this.activeIndex=t}},{key:"spoofMouse",value:function(){var t,e;if("horizontal"===this.layout&&0!==this.coordinateList.length){var r=this.container.getBoundingClientRect(),n=r.x,o=r.y,i=r.height,a=this.coordinateList[this.activeIndex].coordinate,c=(null===(t=window)||void 0===t?void 0:t.scrollX)||0,u=(null===(e=window)||void 0===e?void 0:e.scrollY)||0,l=n+a+c,s=o+this.offset.top+i/2+u;this.mouseHandlerCallback({pageX:l,pageY:s})}}}])}();function YA(t){var e=t.cx,r=t.cy,n=t.radius,o=t.startAngle,i=t.endAngle;return{points:[Qg(e,r,n,o),Qg(e,r,n,i)],cx:e,cy:r,radius:n,startAngle:o,endAngle:i}}function KA(t,e,r){var n,o,i,a;if("horizontal"===t)i=n=e.x,o=r.top,a=r.top+r.height;else if("vertical"===t)a=o=e.y,n=r.left,i=r.left+r.width;else if(null!=e.cx&&null!=e.cy){if("centric"!==t)return YA(e);var c=e.cx,u=e.cy,l=e.innerRadius,s=e.outerRadius,f=e.angle,p=Qg(c,u,l,f),h=Qg(c,u,s,f);n=p.x,o=p.y,i=h.x,a=h.y}return[{x:n,y:o},{x:i,y:a}]}function ZA(t){return(ZA="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function JA(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function QA(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?JA(Object(r),!0).forEach(function(e){tE(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):JA(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function tE(t,e,r){var n;return n=function(t,e){if("object"!=ZA(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=ZA(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(e,"string"),(e="symbol"==ZA(n)?n:n+"")in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function eE(e){var r,n,o,i=e.element,a=e.tooltipEventType,c=e.isActive,u=e.activeCoordinate,l=e.activePayload,s=e.offset,f=e.activeTooltipIndex,p=e.tooltipAxisBandSize,h=e.layout,y=e.chartName,d=null!==(r=i.props.cursor)&&void 0!==r?r:null===(n=i.type.defaultProps)||void 0===n?void 0:n.cursor;if(!i||!d||!c||!u||"ScatterChart"!==y&&"axis"!==a)return null;var v=Xw;if("ScatterChart"===y)o=u,v=WO;else if("BarChart"===y)o=function(t,e,r,n){var o=n/2;return{stroke:"none",fill:"#ccc",x:"horizontal"===t?e.x-o:r.left+.5,y:"horizontal"===t?r.top+.5:e.y-o,width:"horizontal"===t?n:r.width-1,height:"horizontal"===t?r.height-1:n}}(h,u,s,p),v=IO;else if("radial"===h){var m=YA(u),b=m.cx,g=m.cy,w=m.radius;o={cx:b,cy:g,startAngle:m.startAngle,endAngle:m.endAngle,innerRadius:w,outerRadius:w},v=Bw}else o={points:KA(h,u,s)},v=Xw;var x=QA(QA(QA(QA({stroke:"#ccc",pointerEvents:"none"},s),o),no(d,!1)),{},{payload:l,payloadIndex:f,className:St("recharts-tooltip-cursor",d.className)});return t.isValidElement(d)?t.cloneElement(d,x):t.createElement(v,x)}var rE=["item"],nE=["children","className","width","height","style","compact","title","desc"];function oE(t){return(oE="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function iE(){return iE=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},iE.apply(this,arguments)}function aE(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,c=[],u=!0,l=!1;try{if(i=(r=r.call(t)).next,0===e);else for(;!(u=(n=i.call(r)).done)&&(c.push(n.value),c.length!==e);u=!0);}catch(s){l=!0,o=s}finally{try{if(!u&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(l)throw o}}return c}}(t,e)||yE(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function cE(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],e.indexOf(r)>=0||Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}function uE(t,e,r){return e&&function(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,gE(n.key),n)}}(t.prototype,e),Object.defineProperty(t,"prototype",{writable:!1}),t}function lE(t,e,r){return e=fE(e),function(t,e){if(e&&("object"===oE(e)||"function"==typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(t,sE()?Reflect.construct(e,r||[],fE(t).constructor):e.apply(t,r))}function sE(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(e){}return(sE=function(){return!!t})()}function fE(t){return(fE=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function pE(t,e){return(pE=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function hE(t){return function(t){if(Array.isArray(t))return dE(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||yE(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function yE(t,e){if(t){if("string"==typeof t)return dE(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);return"Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r?Array.from(t):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?dE(t,e):void 0}}function dE(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}function vE(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function mE(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?vE(Object(r),!0).forEach(function(e){bE(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):vE(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function bE(t,e,r){return(e=gE(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function gE(t){var e=function(t,e){if("object"!=oE(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=oE(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==oE(e)?e:e+""}var wE={xAxis:["bottom","top"],yAxis:["left","right"]},xE={width:"100%",height:"100%"},OE={x:0,y:0};function jE(t){return t}var SE=function(t,e){var r=e.graphicalItems,n=e.dataStartIndex,o=e.dataEndIndex,i=(null!=r?r:[]).reduce(function(t,e){var r=e.props.data;return r&&r.length?[].concat(hE(t),hE(r)):t},[]);return i.length>0?i:t&&t.length&&An(n)&&An(o)?t.slice(n,o+1):[]};function kE(t){return"number"===t?[0,"auto"]:void 0}var PE=function(t,e,r,n){var o=t.graphicalItems,i=t.tooltipAxis,a=SE(e,t);return r<0||!o||!o.length||r>=a.length?null:o.reduce(function(o,c){var u,l,s=null!==(u=c.props.data)&&void 0!==u?u:e;(s&&t.dataStartIndex+t.dataEndIndex!==0&&t.dataEndIndex-t.dataStartIndex>=r&&(s=s.slice(t.dataStartIndex,t.dataEndIndex+1)),i.dataKey&&!i.allowDuplicatedCategory)?l=In(void 0===s?a:s,i.dataKey,n):l=s&&s[r]||a[r];return l?[].concat(hE(o),[Vg(c,l)]):o},[])},AE=function(t,e,r,n){var o=n||{x:t.chartX,y:t.chartY},i=function(t,e){return"horizontal"===e?t.x:"vertical"===e?t.y:"centric"===e?t.angle:t.radius}(o,r),a=t.orderedTooltipTicks,c=t.tooltipAxis,u=t.tooltipTicks,l=function(t){var e,r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[],n=arguments.length>2?arguments[2]:void 0,o=arguments.length>3?arguments[3]:void 0,i=-1,a=null!==(e=null==r?void 0:r.length)&&void 0!==e?e:0;if(a<=1)return 0;if(o&&"angleAxis"===o.axisType&&Math.abs(Math.abs(o.range[1]-o.range[0])-360)<=1e-6)for(var c=o.range,u=0;u<a;u++){var l=u>0?n[u-1].coordinate:n[a-1].coordinate,s=n[u].coordinate,f=u>=a-1?n[0].coordinate:n[u+1].coordinate,p=void 0;if(kn(s-l)!==kn(f-s)){var h=[];if(kn(f-s)===kn(c[1]-c[0])){p=f;var y=s+c[1]-c[0];h[0]=Math.min(y,(y+l)/2),h[1]=Math.max(y,(y+l)/2)}else{p=l;var d=f+c[1]-c[0];h[0]=Math.min(s,(d+s)/2),h[1]=Math.max(s,(d+s)/2)}var v=[Math.min(s,(p+s)/2),Math.max(s,(p+s)/2)];if(t>v[0]&&t<=v[1]||t>=h[0]&&t<=h[1]){i=n[u].index;break}}else{var m=Math.min(l,f),b=Math.max(l,f);if(t>(m+s)/2&&t<=(b+s)/2){i=n[u].index;break}}}else for(var g=0;g<a;g++)if(0===g&&t<=(r[g].coordinate+r[g+1].coordinate)/2||g>0&&g<a-1&&t>(r[g].coordinate+r[g-1].coordinate)/2&&t<=(r[g].coordinate+r[g+1].coordinate)/2||g===a-1&&t>(r[g].coordinate+r[g-1].coordinate)/2){i=r[g].index;break}return i}(i,a,u,c);if(l>=0&&u){var s=u[l]&&u[l].value,f=PE(t,e,l,s),p=function(t,e,r,n){var o=e.find(function(t){return t&&t.index===r});if(o){if("horizontal"===t)return{x:o.coordinate,y:n.y};if("vertical"===t)return{x:n.x,y:o.coordinate};if("centric"===t){var i=o.coordinate,a=n.radius;return mE(mE(mE({},n),Qg(n.cx,n.cy,a,i)),{},{angle:i,radius:a})}var c=o.coordinate,u=n.angle;return mE(mE(mE({},n),Qg(n.cx,n.cy,c,u)),{},{angle:u,radius:c})}return OE}(r,a,l,o);return{activeTooltipIndex:l,activeLabel:s,activePayload:f,activeCoordinate:p}}return null},EE=function(t,e){var r=e.axes,n=e.graphicalItems,o=e.axisType,i=e.axisIdKey,a=e.stackGroups,c=e.dataStartIndex,u=e.dataEndIndex,l=t.layout,s=t.children,f=t.stackOffset,p=_g(l,o);return r.reduce(function(e,r){var h,y=void 0!==r.type.defaultProps?mE(mE({},r.type.defaultProps),r.props):r.props,d=y.type,v=y.dataKey,m=y.allowDataOverflow,b=y.allowDuplicatedCategory,g=y.scale,w=y.ticks,x=y.includeHidden,O=y[i];if(e[O])return e;var j,S,k,P=SE(t.data,{graphicalItems:n.filter(function(t){var e;return(i in t.props?t.props[i]:null===(e=t.type.defaultProps)||void 0===e?void 0:e[i])===O}),dataStartIndex:c,dataEndIndex:u}),A=P.length;(function(t,e,r){if("number"===r&&!0===e&&Array.isArray(t)){var n=null==t?void 0:t[0],o=null==t?void 0:t[1];if(n&&o&&An(n)&&An(o))return!0}return!1})(y.domain,m,d)&&(j=Fg(y.domain,null,m),!p||"number"!==d&&"auto"===g||(k=Pg(P,v,"category")));var E=kE(d);if(!j||0===j.length){var M,_=null!==(M=y.domain)&&void 0!==M?M:E;if(v){if(j=Pg(P,v,d),"category"===d&&p){var T=function(t){if(!Array.isArray(t))return!1;for(var e=t.length,r={},n=0;n<e;n++){if(r[t[n]])return!0;r[t[n]]=!0}return!1}(j);b&&T?(S=j,j=Rj(0,A)):b||(j=Hg(_,j,r).reduce(function(t,e){return t.indexOf(e)>=0?t:[].concat(hE(t),[e])},[]))}else if("category"===d)j=b?j.filter(function(t){return""!==t&&!Gr(t)}):Hg(_,j,r).reduce(function(t,e){return t.indexOf(e)>=0||""===e||Gr(e)?t:[].concat(hE(t),[e])},[]);else if("number"===d){var C=function(t,e,r,n,o){var i=e.map(function(e){return Eg(t,e,r,o,n)}).filter(function(t){return!Gr(t)});return i&&i.length?i.reduce(function(t,e){return[Math.min(t[0],e[0]),Math.max(t[1],e[1])]},[1/0,-1/0]):null}(P,n.filter(function(t){var e,r,n=i in t.props?t.props[i]:null===(e=t.type.defaultProps)||void 0===e?void 0:e[i],o="hide"in t.props?t.props.hide:null===(r=t.type.defaultProps)||void 0===r?void 0:r.hide;return n===O&&(x||!o)}),v,o,l);C&&(j=C)}!p||"number"!==d&&"auto"===g||(k=Pg(P,v,"category"))}else j=p?Rj(0,A):a&&a[O]&&a[O].hasStack&&"number"===d?"expand"===f?[0,1]:Ug(a[O].stackGroups,c,u):Mg(P,n.filter(function(t){var e=i in t.props?t.props[i]:t.type.defaultProps[i],r="hide"in t.props?t.props.hide:t.type.defaultProps.hide;return e===O&&(x||!r)}),d,l,!0);if("number"===d)j=UA(s,j,O,o,w),_&&(j=Fg(_,j,m));else if("category"===d&&_){var D=_;j.every(function(t){return D.indexOf(t)>=0})&&(j=D)}}return mE(mE({},e),{},bE({},O,mE(mE({},y),{},{axisType:o,domain:j,categoricalDomain:k,duplicateDomain:S,originalDomain:null!==(h=y.domain)&&void 0!==h?h:E,isCategorical:p,layout:l})))},{})},ME=function(t,e){var r=e.axisType,n=void 0===r?"xAxis":r,o=e.AxisComp,i=e.graphicalItems,a=e.stackGroups,c=e.dataStartIndex,u=e.dataEndIndex,l=t.children,s="".concat(n,"Id"),f=Qn(l,o),p={};return f&&f.length?p=EE(t,{axes:f,graphicalItems:i,axisType:n,axisIdKey:s,stackGroups:a,dataStartIndex:c,dataEndIndex:u}):i&&i.length&&(p=function(t,e){var r=e.graphicalItems,n=e.Axis,o=e.axisType,i=e.axisIdKey,a=e.stackGroups,c=e.dataStartIndex,u=e.dataEndIndex,l=t.layout,s=t.children,f=SE(t.data,{graphicalItems:r,dataStartIndex:c,dataEndIndex:u}),p=f.length,h=_g(l,o),y=-1;return r.reduce(function(t,e){var d,v=(void 0!==e.type.defaultProps?mE(mE({},e.type.defaultProps),e.props):e.props)[i],m=kE("number");return t[v]?t:(y++,h?d=Rj(0,p):a&&a[v]&&a[v].hasStack?(d=Ug(a[v].stackGroups,c,u),d=UA(s,d,v,o)):(d=Fg(m,Mg(f,r.filter(function(t){var e,r,n=i in t.props?t.props[i]:null===(e=t.type.defaultProps)||void 0===e?void 0:e[i],o="hide"in t.props?t.props.hide:null===(r=t.type.defaultProps)||void 0===r?void 0:r.hide;return n===v&&!o}),"number",l),n.defaultProps.allowDataOverflow),d=UA(s,d,v,o)),mE(mE({},t),{},bE({},v,mE(mE({axisType:o},n.defaultProps),{},{hide:!0,orientation:Xr(wE,"".concat(o,".").concat(y%2),null),domain:d,originalDomain:m,isCategorical:h,layout:l}))))},{})}(t,{Axis:o,graphicalItems:i,axisType:n,axisIdKey:s,stackGroups:a,dataStartIndex:c,dataEndIndex:u})),p},_E=function(t){var e=t.children,r=t.defaultShowTooltip,n=to(e,rS),o=0,i=0;return t.data&&0!==t.data.length&&(i=t.data.length-1),n&&n.props&&(n.props.startIndex>=0&&(o=n.props.startIndex),n.props.endIndex>=0&&(i=n.props.endIndex)),{chartX:0,chartY:0,dataStartIndex:o,dataEndIndex:i,activeTooltipIndex:-1,isTooltipActive:Boolean(r)}},TE=function(t){return"horizontal"===t?{numericAxisName:"yAxis",cateAxisName:"xAxis"}:"vertical"===t?{numericAxisName:"xAxis",cateAxisName:"yAxis"}:"centric"===t?{numericAxisName:"radiusAxis",cateAxisName:"angleAxis"}:{numericAxisName:"angleAxis",cateAxisName:"radiusAxis"}},CE=function(t,e){var r=t.props,n=(t.graphicalItems,t.xAxisMap),o=void 0===n?{}:n,i=t.yAxisMap,a=void 0===i?{}:i,c=r.width,u=r.height,l=r.children,s=r.margin||{},f=to(l,rS),p=to(l,cs),h=Object.keys(a).reduce(function(t,e){var r=a[e],n=r.orientation;return r.mirror||r.hide?t:mE(mE({},t),{},bE({},n,t[n]+r.width))},{left:s.left||0,right:s.right||0}),y=Object.keys(o).reduce(function(t,e){var r=o[e],n=r.orientation;return r.mirror||r.hide?t:mE(mE({},t),{},bE({},n,Xr(t,"".concat(n))+r.height))},{top:s.top||0,bottom:s.bottom||0}),d=mE(mE({},y),h),v=d.bottom;f&&(d.bottom+=f.props.height||rS.defaultProps.height),p&&e&&(d=function(t,e,r,n){var o=r.children,i=r.width,a=r.margin,c=i-(a.left||0)-(a.right||0),u=bg({children:o,legendWidth:c});if(u){var l=n||{},s=l.width,f=l.height,p=u.align,h=u.verticalAlign,y=u.layout;if(("vertical"===y||"horizontal"===y&&"middle"===h)&&"center"!==p&&An(t[p]))return jg(jg({},t),{},Sg({},p,t[p]+(s||0)));if(("horizontal"===y||"vertical"===y&&"center"===p)&&"middle"!==h&&An(t[h]))return jg(jg({},t),{},Sg({},h,t[h]+(f||0)))}return t}(d,0,r,e));var m=c-d.left-d.right,b=u-d.top-d.bottom;return mE(mE({brushBottom:v},d),{},{width:Math.max(m,0),height:Math.max(b,0)})},DE=function(t,e){return"xAxis"===e?t[e].width:"yAxis"===e?t[e].height:void 0},IE=function(e){var r=e.chartName,o=e.GraphicalChild,i=e.defaultTooltipEventType,a=void 0===i?"axis":i,c=e.validateTooltipEventTypes,u=void 0===c?["axis"]:c,l=e.axisComponents,s=e.legendContent,f=e.formatAxisMap,p=e.defaultProps,h=function(t,e){var r=e.graphicalItems,n=e.stackGroups,o=e.offset,i=e.updateId,a=e.dataStartIndex,c=e.dataEndIndex,u=t.barSize,s=t.layout,f=t.barGap,p=t.barCategoryGap,h=t.maxBarSize,y=TE(s),d=y.numericAxisName,v=y.cateAxisName,m=function(t){return!(!t||!t.length)&&t.some(function(t){var e=Yn(t&&t.type);return e&&e.indexOf("Bar")>=0})}(r),b=[];return r.forEach(function(r,y){var g=SE(t.data,{graphicalItems:[r],dataStartIndex:a,dataEndIndex:c}),w=void 0!==r.type.defaultProps?mE(mE({},r.type.defaultProps),r.props):r.props,x=w.dataKey,O=w.maxBarSize,j=w["".concat(d,"Id")],S=w["".concat(v,"Id")],k=l.reduce(function(t,r){var n=e["".concat(r.axisType,"Map")],o=w["".concat(r.axisType,"Id")];n&&n[o]||"zAxis"===r.axisType||Qb();var i=n[o];return mE(mE({},t),{},bE(bE({},r.axisType,i),"".concat(r.axisType,"Ticks"),Cg(i)))},{}),P=k[v],A=k["".concat(v,"Ticks")],E=n&&n[j]&&n[j].hasStack&&function(t,e){var r,n=(null!==(r=t.type)&&void 0!==r&&r.defaultProps?jg(jg({},t.type.defaultProps),t.props):t.props).stackId;if(En(n)){var o=e[n];if(o){var i=o.items.indexOf(t);return i>=0?o.stackedData[i]:null}}return null}(r,n[j].stackGroups),M=Yn(r.type).indexOf("Bar")>=0,_=Wg(P,A),T=[],C=m&&function(t){var e=t.barSize,r=t.totalSize,n=t.stackGroups,o=void 0===n?{}:n;if(!o)return{};for(var i={},a=Object.keys(o),c=0,u=a.length;c<u;c++)for(var l=o[a[c]].stackGroups,s=Object.keys(l),f=0,p=s.length;f<p;f++){var h=l[s[f]],y=h.items,d=h.cateAxisId,v=y.filter(function(t){return Yn(t.type).indexOf("Bar")>=0});if(v&&v.length){var m=v[0].type.defaultProps,b=void 0!==m?jg(jg({},m),v[0].props):v[0].props,g=b.barSize,w=b[d];i[w]||(i[w]=[]);var x=Gr(g)?e:g;i[w].push({item:v[0],stackList:v.slice(1),barSize:Gr(x)?void 0:Tn(x,r,0)})}}return i}({barSize:u,stackGroups:n,totalSize:DE(k,v)});if(M){var D,I,N=Gr(O)?h:O,B=null!==(D=null!==(I=Wg(P,A,!0))&&void 0!==I?I:N)&&void 0!==D?D:0;T=function(t){var e=t.barGap,r=t.barCategoryGap,n=t.bandSize,o=t.sizeList,i=void 0===o?[]:o,a=t.maxBarSize,c=i.length;if(c<1)return null;var u,l=Tn(e,n,0,!0),s=[];if(i[0].barSize===+i[0].barSize){var f=!1,p=n/c,h=i.reduce(function(t,e){return t+e.barSize||0},0);(h+=(c-1)*l)>=n&&(h-=(c-1)*l,l=0),h>=n&&p>0&&(f=!0,h=c*(p*=.9));var y={offset:((n-h)/2|0)-l,size:0};u=i.reduce(function(t,e){var r={item:e.item,position:{offset:y.offset+y.size+l,size:f?p:e.barSize}},n=[].concat(wg(t),[r]);return y=n[n.length-1].position,e.stackList&&e.stackList.length&&e.stackList.forEach(function(t){n.push({item:t,position:y})}),n},s)}else{var d=Tn(r,n,0,!0);n-2*d-(c-1)*l<=0&&(l=0);var v=(n-2*d-(c-1)*l)/c;v>1&&(v>>=0);var m=a===+a?Math.min(v,a):v;u=i.reduce(function(t,e,r){var n=[].concat(wg(t),[{item:e.item,position:{offset:d+(v+l)*r+(v-m)/2,size:m}}]);return e.stackList&&e.stackList.length&&e.stackList.forEach(function(t){n.push({item:t,position:n[n.length-1].position})}),n},s)}return u}({barGap:f,barCategoryGap:p,bandSize:B!==_?B:_,sizeList:C[S],maxBarSize:N}),B!==_&&(T=T.map(function(t){return mE(mE({},t),{},{position:mE(mE({},t.position),{},{offset:t.position.offset-B/2})})}))}var L,R,z=r&&r.type&&r.type.getComposedData;z&&b.push({props:mE(mE({},z(mE(mE({},k),{},{displayedData:g,props:t,dataKey:x,item:r,bandSize:_,barPosition:T,offset:o,stackedData:E,layout:s,dataStartIndex:a,dataEndIndex:c}))),{},bE(bE(bE({key:r.key||"item-".concat(y)},d,k[d]),v,k[v]),"animationId",i)),childIndex:(L=r,R=t.children,Jn(R).indexOf(L)),item:r})}),b},y=function(t,e){var n=t.props,i=t.dataStartIndex,a=t.dataEndIndex,c=t.updateId;if(!eo({props:n}))return null;var u=n.children,s=n.layout,p=n.stackOffset,y=n.data,d=n.reverseStackOrder,v=TE(s),m=v.numericAxisName,b=v.cateAxisName,g=Qn(u,o),w=function(t,e,r,n,o,i){if(!t)return null;var a=(i?e.reverse():e).reduce(function(t,e){var o,i=null!==(o=e.type)&&void 0!==o&&o.defaultProps?jg(jg({},e.type.defaultProps),e.props):e.props,a=i.stackId;if(i.hide)return t;var c=i[r],u=t[c]||{hasStack:!1,stackGroups:{}};if(En(a)){var l=u.stackGroups[a]||{numericAxisId:r,cateAxisId:n,items:[]};l.items.push(e),u.hasStack=!0,u.stackGroups[a]=l}else u.stackGroups[_n("_stackId_")]={numericAxisId:r,cateAxisId:n,items:[e]};return jg(jg({},t),{},Sg({},c,u))},{});return Object.keys(a).reduce(function(e,i){var c=a[i];return c.hasStack&&(c.stackGroups=Object.keys(c.stackGroups).reduce(function(e,i){var a=c.stackGroups[i];return jg(jg({},e),{},Sg({},i,{numericAxisId:r,cateAxisId:n,items:a.items,stackedData:Lg(t,a.items,o)}))},{})),jg(jg({},e),{},Sg({},i,c))},{})}(y,g,"".concat(m,"Id"),"".concat(b,"Id"),p,d),x=l.reduce(function(t,e){var r="".concat(e.axisType,"Map");return mE(mE({},t),{},bE({},r,ME(n,mE(mE({},e),{},{graphicalItems:g,stackGroups:e.axisType===m&&w,dataStartIndex:i,dataEndIndex:a}))))},{}),O=CE(mE(mE({},x),{},{props:n,graphicalItems:g}),null==e?void 0:e.legendBBox);Object.keys(x).forEach(function(t){x[t]=f(n,x[t],O,t.replace("Map",""),r)});var j,S,k=x["".concat(b,"Map")],P=(j=Cn(k),{tooltipTicks:S=Cg(j,!1,!0),orderedTooltipTicks:rf(S,function(t){return t.coordinate}),tooltipAxis:j,tooltipAxisBandSize:Wg(j,S)}),A=h(n,mE(mE({},x),{},{dataStartIndex:i,dataEndIndex:a,updateId:c,graphicalItems:g,stackGroups:w,offset:O}));return mE(mE({formattedGraphicalItems:A,graphicalItems:g,offset:O,stackGroups:w},P),x)},d=function(){function e(o){var i,a,c;return function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,e),bE(c=lE(this,e,[o]),"eventEmitterSymbol",Symbol("rechartsEventEmitter")),bE(c,"accessibilityManager",new GA),bE(c,"handleLegendBBoxUpdate",function(t){if(t){var e=c.state,r=e.dataStartIndex,n=e.dataEndIndex,o=e.updateId;c.setState(mE({legendBBox:t},y({props:c.props,dataStartIndex:r,dataEndIndex:n,updateId:o},mE(mE({},c.state),{},{legendBBox:t}))))}}),bE(c,"handleReceiveSyncEvent",function(t,e,r){if(c.props.syncId===t){if(r===c.eventEmitterSymbol&&"function"!=typeof c.props.syncMethod)return;c.applySyncEvent(e)}}),bE(c,"handleBrushChange",function(t){var e=t.startIndex,r=t.endIndex;if(e!==c.state.dataStartIndex||r!==c.state.dataEndIndex){var n=c.state.updateId;c.setState(function(){return mE({dataStartIndex:e,dataEndIndex:r},y({props:c.props,dataStartIndex:e,dataEndIndex:r,updateId:n},c.state))}),c.triggerSyncEvent({dataStartIndex:e,dataEndIndex:r})}}),bE(c,"handleMouseEnter",function(t){var e=c.getMouseInfo(t);if(e){var r=mE(mE({},e),{},{isTooltipActive:!0});c.setState(r),c.triggerSyncEvent(r);var n=c.props.onMouseEnter;re(n)&&n(r,t)}}),bE(c,"triggeredAfterMouseMove",function(t){var e=c.getMouseInfo(t),r=e?mE(mE({},e),{},{isTooltipActive:!0}):{isTooltipActive:!1};c.setState(r),c.triggerSyncEvent(r);var n=c.props.onMouseMove;re(n)&&n(r,t)}),bE(c,"handleItemMouseEnter",function(t){c.setState(function(){return{isTooltipActive:!0,activeItem:t,activePayload:t.tooltipPayload,activeCoordinate:t.tooltipPosition||{x:t.cx,y:t.cy}}})}),bE(c,"handleItemMouseLeave",function(){c.setState(function(){return{isTooltipActive:!1}})}),bE(c,"handleMouseMove",function(t){t.persist(),c.throttleTriggeredAfterMouseMove(t)}),bE(c,"handleMouseLeave",function(t){c.throttleTriggeredAfterMouseMove.cancel();var e={isTooltipActive:!1};c.setState(e),c.triggerSyncEvent(e);var r=c.props.onMouseLeave;re(r)&&r(e,t)}),bE(c,"handleOuterEvent",function(t){var e,r=function(t){var e=t&&t.type;return e&&Gn[e]?Gn[e]:null}(t),n=Xr(c.props,"".concat(r));r&&re(n)&&n(null!==(e=/.*touch.*/i.test(r)?c.getMouseInfo(t.changedTouches[0]):c.getMouseInfo(t))&&void 0!==e?e:{},t)}),bE(c,"handleClick",function(t){var e=c.getMouseInfo(t);if(e){var r=mE(mE({},e),{},{isTooltipActive:!0});c.setState(r),c.triggerSyncEvent(r);var n=c.props.onClick;re(n)&&n(r,t)}}),bE(c,"handleMouseDown",function(t){var e=c.props.onMouseDown;re(e)&&e(c.getMouseInfo(t),t)}),bE(c,"handleMouseUp",function(t){var e=c.props.onMouseUp;re(e)&&e(c.getMouseInfo(t),t)}),bE(c,"handleTouchMove",function(t){null!=t.changedTouches&&t.changedTouches.length>0&&c.throttleTriggeredAfterMouseMove(t.changedTouches[0])}),bE(c,"handleTouchStart",function(t){null!=t.changedTouches&&t.changedTouches.length>0&&c.handleMouseDown(t.changedTouches[0])}),bE(c,"handleTouchEnd",function(t){null!=t.changedTouches&&t.changedTouches.length>0&&c.handleMouseUp(t.changedTouches[0])}),bE(c,"handleDoubleClick",function(t){var e=c.props.onDoubleClick;re(e)&&e(c.getMouseInfo(t),t)}),bE(c,"handleContextMenu",function(t){var e=c.props.onContextMenu;re(e)&&e(c.getMouseInfo(t),t)}),bE(c,"triggerSyncEvent",function(t){void 0!==c.props.syncId&&qA.emit(FA,c.props.syncId,t,c.eventEmitterSymbol)}),bE(c,"applySyncEvent",function(t){var e=c.props,r=e.layout,n=e.syncMethod,o=c.state.updateId,i=t.dataStartIndex,a=t.dataEndIndex;if(void 0!==t.dataStartIndex||void 0!==t.dataEndIndex)c.setState(mE({dataStartIndex:i,dataEndIndex:a},y({props:c.props,dataStartIndex:i,dataEndIndex:a,updateId:o},c.state)));else if(void 0!==t.activeTooltipIndex){var u=t.chartX,l=t.chartY,s=t.activeTooltipIndex,f=c.state,p=f.offset,h=f.tooltipTicks;if(!p)return;if("function"==typeof n)s=n(h,t);else if("value"===n){s=-1;for(var d=0;d<h.length;d++)if(h[d].value===t.activeLabel){s=d;break}}var v=mE(mE({},p),{},{x:p.left,y:p.top}),m=Math.min(u,v.x+v.width),b=Math.min(l,v.y+v.height),g=h[s]&&h[s].value,w=PE(c.state,c.props.data,s),x=h[s]?{x:"horizontal"===r?h[s].coordinate:m,y:"horizontal"===r?b:h[s].coordinate}:OE;c.setState(mE(mE({},t),{},{activeLabel:g,activeCoordinate:x,activePayload:w,activeTooltipIndex:s}))}else c.setState(t)}),bE(c,"renderCursor",function(t){var e,o=c.state,i=o.isTooltipActive,a=o.activeCoordinate,u=o.activePayload,l=o.offset,s=o.activeTooltipIndex,f=o.tooltipAxisBandSize,p=c.getTooltipEventType(),h=null!==(e=t.props.active)&&void 0!==e?e:i,y=c.props.layout,d=t.key||"_recharts-cursor";return n.createElement(eE,{key:d,activeCoordinate:a,activePayload:u,activeTooltipIndex:s,chartName:r,element:t,isActive:h,layout:y,offset:l,tooltipAxisBandSize:f,tooltipEventType:p})}),bE(c,"renderPolarAxis",function(e,r,n){var o=Xr(e,"type.axisType"),i=Xr(c.state,"".concat(o,"Map")),a=e.type.defaultProps,u=void 0!==a?mE(mE({},a),e.props):e.props,l=i&&i[u["".concat(o,"Id")]];return t.cloneElement(e,mE(mE({},l),{},{className:St(o,l.className),key:e.key||"".concat(r,"-").concat(n),ticks:Cg(l,!0)}))}),bE(c,"renderPolarGrid",function(e){var r=e.props,n=r.radialLines,o=r.polarAngles,i=r.polarRadius,a=c.state,u=a.radiusAxisMap,l=a.angleAxisMap,s=Cn(u),f=Cn(l),p=f.cx,h=f.cy,y=f.innerRadius,d=f.outerRadius;return t.cloneElement(e,{polarAngles:Array.isArray(o)?o:Cg(f,!0).map(function(t){return t.coordinate}),polarRadius:Array.isArray(i)?i:Cg(s,!0).map(function(t){return t.coordinate}),cx:p,cy:h,innerRadius:y,outerRadius:d,key:e.key||"polar-grid",radialLines:n})}),bE(c,"renderLegend",function(){var e=c.state.formattedGraphicalItems,r=c.props,n=r.children,o=r.width,i=r.height,a=c.props.margin||{},u=o-(a.left||0)-(a.right||0),l=bg({children:n,formattedGraphicalItems:e,legendWidth:u,legendContent:s});if(!l)return null;var f=l.item,p=cE(l,rE);return t.cloneElement(f,mE(mE({},p),{},{chartWidth:o,chartHeight:i,margin:a,onBBoxUpdate:c.handleLegendBBoxUpdate}))}),bE(c,"renderTooltip",function(){var e,r=c.props,n=r.children,o=r.accessibilityLayer,i=to(n,qf);if(!i)return null;var a=c.state,u=a.isTooltipActive,l=a.activeCoordinate,s=a.activePayload,f=a.activeLabel,p=a.offset,h=null!==(e=i.props.active)&&void 0!==e?e:u;return t.cloneElement(i,{viewBox:mE(mE({},p),{},{x:p.left,y:p.top}),active:h,label:f,payload:h?s:[],coordinate:l,accessibilityLayer:o})}),bE(c,"renderBrush",function(e){var r=c.props,n=r.margin,o=r.data,i=c.state,a=i.offset,u=i.dataStartIndex,l=i.dataEndIndex,s=i.updateId;return t.cloneElement(e,{key:e.key||"_recharts-brush",onChange:Ig(c.handleBrushChange,e.props.onChange),data:o,x:An(e.props.x)?e.props.x:a.left,y:An(e.props.y)?e.props.y:a.top+a.height+a.brushBottom-(n.bottom||0),width:An(e.props.width)?e.props.width:a.width,startIndex:u,endIndex:l,updateId:"brush-".concat(s)})}),bE(c,"renderReferenceElement",function(e,r,n){if(!e)return null;var o=c.clipPathId,i=c.state,a=i.xAxisMap,u=i.yAxisMap,l=i.offset,s=e.type.defaultProps||{},f=e.props,p=f.xAxisId,h=void 0===p?s.xAxisId:p,y=f.yAxisId,d=void 0===y?s.yAxisId:y;return t.cloneElement(e,{key:e.key||"".concat(r,"-").concat(n),xAxis:a[h],yAxis:u[d],viewBox:{x:l.left,y:l.top,width:l.width,height:l.height},clipPathId:o})}),bE(c,"renderActivePoints",function(t){var r=t.item,n=t.activePoint,o=t.basePoint,i=t.childIndex,a=t.isRange,c=[],u=r.props.key,l=void 0!==r.item.type.defaultProps?mE(mE({},r.item.type.defaultProps),r.item.props):r.item.props,s=l.activeDot,f=mE(mE({index:i,dataKey:l.dataKey,cx:n.x,cy:n.y,r:4,fill:Ag(r.item),strokeWidth:2,stroke:"#fff",payload:n.payload,value:n.value},no(s,!1)),qn(s));return c.push(e.renderActiveDot(s,f,"".concat(u,"-activePoint-").concat(i))),o?c.push(e.renderActiveDot(s,mE(mE({},f),{},{cx:o.x,cy:o.y}),"".concat(u,"-basePoint-").concat(i))):a&&c.push(null),c}),bE(c,"renderGraphicChild",function(e,r,n){var o=c.filterFormatItem(e,r,n);if(!o)return null;var i=c.getTooltipEventType(),a=c.state,u=a.isTooltipActive,l=a.tooltipAxis,s=a.activeTooltipIndex,f=a.activeLabel,p=to(c.props.children,qf),h=o.props,y=h.points,d=h.isRange,v=h.baseLine,m=void 0!==o.item.type.defaultProps?mE(mE({},o.item.type.defaultProps),o.item.props):o.item.props,b=m.activeDot,g=m.hide,w=m.activeBar,x=m.activeShape,O=Boolean(!g&&u&&p&&(b||w||x)),j={};"axis"!==i&&p&&"click"===p.props.trigger?j={onClick:Ig(c.handleItemMouseEnter,e.props.onClick)}:"axis"!==i&&(j={onMouseLeave:Ig(c.handleItemMouseLeave,e.props.onMouseLeave),onMouseEnter:Ig(c.handleItemMouseEnter,e.props.onMouseEnter)});var S=t.cloneElement(e,mE(mE({},o.props),j));if(O){if(!(s>=0)){var k,P=(null!==(k=c.getItemByXY(c.state.activeCoordinate))&&void 0!==k?k:{graphicalItem:S}).graphicalItem,A=P.item,E=void 0===A?e:A,M=P.childIndex,_=mE(mE(mE({},o.props),j),{},{activeIndex:M});return[t.cloneElement(E,_),null,null]}var T,C;if(l.dataKey&&!l.allowDuplicatedCategory){var D="function"==typeof l.dataKey?function(t){return"function"==typeof l.dataKey?l.dataKey(t.payload):null}:"payload.".concat(l.dataKey.toString());T=In(y,D,f),C=d&&v&&In(v,D,f)}else T=null==y?void 0:y[s],C=d&&v&&v[s];if(x||w){var I=void 0!==e.props.activeIndex?e.props.activeIndex:s;return[t.cloneElement(e,mE(mE(mE({},o.props),j),{},{activeIndex:I})),null,null]}if(!Gr(T))return[S].concat(hE(c.renderActivePoints({item:o,activePoint:T,basePoint:C,childIndex:s,isRange:d})))}return d?[S,null,null]:[S,null]}),bE(c,"renderCustomized",function(e,r,n){return t.cloneElement(e,mE(mE({key:"recharts-customized-".concat(n)},c.props),c.state))}),bE(c,"renderMap",{CartesianGrid:{handler:jE,once:!0},ReferenceArea:{handler:c.renderReferenceElement},ReferenceLine:{handler:jE},ReferenceDot:{handler:c.renderReferenceElement},XAxis:{handler:jE},YAxis:{handler:jE},Brush:{handler:c.renderBrush,once:!0},Bar:{handler:c.renderGraphicChild},Line:{handler:c.renderGraphicChild},Area:{handler:c.renderGraphicChild},Radar:{handler:c.renderGraphicChild},RadialBar:{handler:c.renderGraphicChild},Scatter:{handler:c.renderGraphicChild},Pie:{handler:c.renderGraphicChild},Funnel:{handler:c.renderGraphicChild},Tooltip:{handler:c.renderCursor,once:!0},PolarGrid:{handler:c.renderPolarGrid,once:!0},PolarAngleAxis:{handler:c.renderPolarAxis},PolarRadiusAxis:{handler:c.renderPolarAxis},Customized:{handler:c.renderCustomized}}),c.clipPathId="".concat(null!==(i=o.id)&&void 0!==i?i:_n("recharts"),"-clip"),c.throttleTriggeredAfterMouseMove=up(c.triggeredAfterMouseMove,null!==(a=o.throttleDelay)&&void 0!==a?a:1e3/60),c.state={},c}return function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&pE(t,e)}(e,t.Component),uE(e,[{key:"componentDidMount",value:function(){var t,e;this.addListener(),this.accessibilityManager.setDetails({container:this.container,offset:{left:null!==(t=this.props.margin.left)&&void 0!==t?t:0,top:null!==(e=this.props.margin.top)&&void 0!==e?e:0},coordinateList:this.state.tooltipTicks,mouseHandlerCallback:this.triggeredAfterMouseMove,layout:this.props.layout}),this.displayDefaultTooltip()}},{key:"displayDefaultTooltip",value:function(){var t=this.props,e=t.children,r=t.data,n=t.height,o=t.layout,i=to(e,qf);if(i){var a=i.props.defaultIndex;if(!("number"!=typeof a||a<0||a>this.state.tooltipTicks.length-1)){var c=this.state.tooltipTicks[a]&&this.state.tooltipTicks[a].value,u=PE(this.state,r,a,c),l=this.state.tooltipTicks[a].coordinate,s=(this.state.offset.top+n)/2,f="horizontal"===o?{x:l,y:s}:{y:l,x:s},p=this.state.formattedGraphicalItems.find(function(t){return"Scatter"===t.item.type.name});p&&(f=mE(mE({},f),p.props.points[a].tooltipPosition),u=p.props.points[a].tooltipPayload);var h={activeTooltipIndex:a,isTooltipActive:!0,activeLabel:c,activePayload:u,activeCoordinate:f};this.setState(h),this.renderCursor(i),this.accessibilityManager.setIndex(a)}}}},{key:"getSnapshotBeforeUpdate",value:function(t,e){if(!this.props.accessibilityLayer)return null;var r,n;(this.state.tooltipTicks!==e.tooltipTicks&&this.accessibilityManager.setDetails({coordinateList:this.state.tooltipTicks}),this.props.layout!==t.layout&&this.accessibilityManager.setDetails({layout:this.props.layout}),this.props.margin!==t.margin)&&this.accessibilityManager.setDetails({offset:{left:null!==(r=this.props.margin.left)&&void 0!==r?r:0,top:null!==(n=this.props.margin.top)&&void 0!==n?n:0}});return null}},{key:"componentDidUpdate",value:function(t){oo([to(t.children,qf)],[to(this.props.children,qf)])||this.displayDefaultTooltip()}},{key:"componentWillUnmount",value:function(){this.removeListener(),this.throttleTriggeredAfterMouseMove.cancel()}},{key:"getTooltipEventType",value:function(){var t=to(this.props.children,qf);if(t&&"boolean"==typeof t.props.shared){var e=t.props.shared?"axis":"item";return u.indexOf(e)>=0?e:a}return a}},{key:"getMouseInfo",value:function(t){if(!this.container)return null;var e,r=this.container,n=r.getBoundingClientRect(),o={top:(e=n).top+window.scrollY-document.documentElement.clientTop,left:e.left+window.scrollX-document.documentElement.clientLeft},i={chartX:Math.round(t.pageX-o.left),chartY:Math.round(t.pageY-o.top)},a=n.width/r.offsetWidth||1,c=this.inRange(i.chartX,i.chartY,a);if(!c)return null;var u=this.state,l=u.xAxisMap,s=u.yAxisMap,f=this.getTooltipEventType(),p=AE(this.state,this.props.data,this.props.layout,c);if("axis"!==f&&l&&s){var h=Cn(l).scale,y=Cn(s).scale,d=h&&h.invert?h.invert(i.chartX):null,v=y&&y.invert?y.invert(i.chartY):null;return mE(mE({},i),{},{xValue:d,yValue:v},p)}return p?mE(mE({},i),p):null}},{key:"inRange",value:function(t,e){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:1,n=this.props.layout,o=t/r,i=e/r;if("horizontal"===n||"vertical"===n){var a=this.state.offset;return o>=a.left&&o<=a.left+a.width&&i>=a.top&&i<=a.top+a.height?{x:o,y:i}:null}var c=this.state,u=c.angleAxisMap,l=c.radiusAxisMap;if(u&&l){var s=Cn(u);return rw({x:o,y:i},s)}return null}},{key:"parseEventsOfWrapper",value:function(){var t=this.props.children,e=this.getTooltipEventType(),r=to(t,qf),n={};return r&&"axis"===e&&(n="click"===r.props.trigger?{onClick:this.handleClick}:{onMouseEnter:this.handleMouseEnter,onDoubleClick:this.handleDoubleClick,onMouseMove:this.handleMouseMove,onMouseLeave:this.handleMouseLeave,onTouchMove:this.handleTouchMove,onTouchStart:this.handleTouchStart,onTouchEnd:this.handleTouchEnd,onContextMenu:this.handleContextMenu}),mE(mE({},qn(this.props,this.handleOuterEvent)),n)}},{key:"addListener",value:function(){qA.on(FA,this.handleReceiveSyncEvent)}},{key:"removeListener",value:function(){qA.removeListener(FA,this.handleReceiveSyncEvent)}},{key:"filterFormatItem",value:function(t,e,r){for(var n=this.state.formattedGraphicalItems,o=0,i=n.length;o<i;o++){var a=n[o];if(a.item===t||a.props.key===t.key||e===Yn(a.item.type)&&r===a.childIndex)return a}return null}},{key:"renderClipPath",value:function(){var t=this.clipPathId,e=this.state.offset,r=e.left,o=e.top,i=e.height,a=e.width;return n.createElement("defs",null,n.createElement("clipPath",{id:t},n.createElement("rect",{x:r,y:o,height:i,width:a})))}},{key:"getXScales",value:function(){var t=this.state.xAxisMap;return t?Object.entries(t).reduce(function(t,e){var r=aE(e,2),n=r[0],o=r[1];return mE(mE({},t),{},bE({},n,o.scale))},{}):null}},{key:"getYScales",value:function(){var t=this.state.yAxisMap;return t?Object.entries(t).reduce(function(t,e){var r=aE(e,2),n=r[0],o=r[1];return mE(mE({},t),{},bE({},n,o.scale))},{}):null}},{key:"getXScaleByAxisId",value:function(t){var e;return null===(e=this.state.xAxisMap)||void 0===e||null===(e=e[t])||void 0===e?void 0:e.scale}},{key:"getYScaleByAxisId",value:function(t){var e;return null===(e=this.state.yAxisMap)||void 0===e||null===(e=e[t])||void 0===e?void 0:e.scale}},{key:"getItemByXY",value:function(t){var e=this.state,r=e.formattedGraphicalItems,n=e.activeItem;if(r&&r.length)for(var o=0,i=r.length;o<i;o++){var a=r[o],c=a.props,u=a.item,l=void 0!==u.type.defaultProps?mE(mE({},u.type.defaultProps),u.props):u.props,s=Yn(u.type);if("Bar"===s){var f=(c.data||[]).find(function(e){return CO(t,e)});if(f)return{graphicalItem:a,payload:f}}else if("RadialBar"===s){var p=(c.data||[]).find(function(e){return rw(t,e)});if(p)return{graphicalItem:a,payload:p}}else if(jj(a,n)||Sj(a,n)||kj(a,n)){var h=Mj({graphicalItem:a,activeTooltipItem:n,itemData:l.data}),y=void 0===l.activeIndex?h:l.activeIndex;return{graphicalItem:mE(mE({},a),{},{childIndex:y}),payload:kj(a,n)?l.data[h]:a.props.data[h]}}}return null}},{key:"render",value:function(){var t=this;if(!eo(this))return null;var e,r,o=this.props,i=o.children,a=o.className,c=o.width,u=o.height,l=o.style,s=o.compact,f=o.title,p=o.desc,h=cE(o,nE),y=no(h,!1);if(s)return n.createElement(xk,{state:this.state,width:this.props.width,height:this.props.height,clipPathId:this.clipPathId},n.createElement(so,iE({},y,{width:c,height:u,title:f,desc:p}),this.renderClipPath(),ao(i,this.renderMap)));this.props.accessibilityLayer&&(y.tabIndex=null!==(e=this.props.tabIndex)&&void 0!==e?e:0,y.role=null!==(r=this.props.role)&&void 0!==r?r:"application",y.onKeyDown=function(e){t.accessibilityManager.keyboardEvent(e)},y.onFocus=function(){t.accessibilityManager.focus()});var d=this.parseEventsOfWrapper();return n.createElement(xk,{state:this.state,width:this.props.width,height:this.props.height,clipPathId:this.clipPathId},n.createElement("div",iE({className:St("recharts-wrapper",a),style:mE({position:"relative",cursor:"default",width:c,height:u},l)},d,{ref:function(e){t.container=e}}),n.createElement(so,iE({},y,{width:c,height:u,title:f,desc:p,style:xE}),this.renderClipPath(),ao(i,this.renderMap)),this.renderLegend(),this.renderTooltip()))}}])}();bE(d,"displayName",r),bE(d,"defaultProps",mE({layout:"horizontal",stackOffset:"none",barCategoryGap:"10%",barGap:4,margin:{top:5,right:5,bottom:5,left:5},reverseStackOrder:!1,syncMethod:"index"},p)),bE(d,"getDerivedStateFromProps",function(t,e){var r=t.dataKey,n=t.data,o=t.children,i=t.width,a=t.height,c=t.layout,u=t.stackOffset,l=t.margin,s=e.dataStartIndex,f=e.dataEndIndex;if(void 0===e.updateId){var p=_E(t);return mE(mE(mE({},p),{},{updateId:0},y(mE(mE({props:t},p),{},{updateId:0}),e)),{},{prevDataKey:r,prevData:n,prevWidth:i,prevHeight:a,prevLayout:c,prevStackOffset:u,prevMargin:l,prevChildren:o})}if(r!==e.prevDataKey||n!==e.prevData||i!==e.prevWidth||a!==e.prevHeight||c!==e.prevLayout||u!==e.prevStackOffset||!Bn(l,e.prevMargin)){var h=_E(t),d={chartX:e.chartX,chartY:e.chartY,isTooltipActive:e.isTooltipActive},v=mE(mE({},AE(e,n,c)),{},{updateId:e.updateId+1}),m=mE(mE(mE({},h),d),v);return mE(mE(mE({},m),y(mE({props:t},m),e)),{},{prevDataKey:r,prevData:n,prevWidth:i,prevHeight:a,prevLayout:c,prevStackOffset:u,prevMargin:l,prevChildren:o})}if(!oo(o,e.prevChildren)){var b,g,w,x,O=to(o,rS),j=O&&null!==(b=null===(g=O.props)||void 0===g?void 0:g.startIndex)&&void 0!==b?b:s,S=O&&null!==(w=null===(x=O.props)||void 0===x?void 0:x.endIndex)&&void 0!==w?w:f,k=j!==s||S!==f,P=!Gr(n)&&!k?e.updateId:e.updateId+1;return mE(mE({updateId:P},y(mE(mE({props:t},e),{},{updateId:P,dataStartIndex:j,dataEndIndex:S}),e)),{},{prevChildren:o,dataStartIndex:j,dataEndIndex:S})}return null}),bE(d,"renderActiveDot",function(e,r,o){var i;return i=t.isValidElement(e)?t.cloneElement(e,r):re(e)?e(r):n.createElement(BO,r),n.createElement(yo,{className:"recharts-active-dot",key:o},i)});var v=t.forwardRef(function(t,e){return n.createElement(d,iE({},t,{ref:e}))});return v.displayName=d.displayName,v},NE=IE({chartName:"LineChart",GraphicalChild:dA,axisComponents:[{axisType:"xAxis",AxisComp:PA},{axisType:"yAxis",AxisComp:LA}],formatAxisMap:QS}),BE=IE({chartName:"BarChart",GraphicalChild:VS,defaultTooltipEventType:"axis",validateTooltipEventTypes:["axis","item"],axisComponents:[{axisType:"xAxis",AxisComp:PA},{axisType:"yAxis",AxisComp:LA}],formatAxisMap:QS});export{tt as $,a as A,p as B,v as C,s as D,A as E,M as F,D as G,B as H,R as I,at as J,j as K,F as L,G as M,N,V as O,J as P,T as Q,dp as R,nt as S,dt as T,mt as U,ct as V,d as W,xt as X,LA as Y,wt as Z,k as _,c as a,rt as a0,lt as a1,H as a2,BE as a3,VS as a4,L as a5,ht as a6,vt as a7,Z as a8,m as a9,I as aa,gt as ab,et as ac,q as ad,O as ae,C as af,x as ag,b as ah,ft as ai,P as aj,z as ak,Ot as al,S as am,Q as an,K as ao,X as ap,it as b,y as c,h as d,ut as e,f,bt as g,g as h,l as i,w as j,st as k,ot as l,Y as m,W as n,yt as o,U as p,u as q,$ as r,E as s,pt as t,NE as u,JP as v,PA as w,qf as x,dA as y,_ as z};
