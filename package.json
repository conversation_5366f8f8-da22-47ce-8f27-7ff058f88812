{"name": "upsc-saas-platform", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite --mode development", "build": "vite build --mode production", "build:staging": "vite build --mode staging", "lint": "eslint .", "preview": "vite preview", "json-server": "json-server --watch db.json --port 3001", "api-server": "cd api-server && node server.mjs", "dev:full": "concurrently \"npm run api-server\" \"npm run dev\"", "deploy:frontend": "gcloud app deploy app.yaml", "deploy:api": "gcloud app deploy api-service.yaml", "deploy:all": "npm run build && npm run deploy:api && npm run deploy:frontend"}, "dependencies": {"concurrently": "^9.2.0", "date-fns": "^3.3.1", "firebase": "^11.10.0", "jspdf": "^3.0.1", "jspdf-autotable": "^5.0.2", "lucide-react": "^0.344.0", "moment-timezone": "^0.6.0", "papaparse": "^5.5.3", "react": "^18.3.1", "react-dom": "^18.3.1", "react-hot-toast": "^2.5.2", "react-router-dom": "^6.22.0", "react-toastify": "^11.0.5", "recharts": "^2.12.0", "terser": "^5.43.1"}, "devDependencies": {"@eslint/js": "^9.9.1", "@types/papaparse": "^5.3.16", "@types/react": "^18.3.5", "@types/react-dom": "^18.3.0", "@vitejs/plugin-react": "^4.3.1", "autoprefixer": "^10.4.18", "eslint": "^9.9.1", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.11", "globals": "^15.9.0", "json-server": "^0.17.4", "postcss": "^8.4.35", "tailwindcss": "^3.4.1", "typescript": "^5.5.3", "typescript-eslint": "^8.3.0", "vite": "^5.4.2"}}