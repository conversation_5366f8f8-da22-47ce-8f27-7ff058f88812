import React, { useState, useRef, useEffect } from 'react';
import { ShoppingCart, X, Trash2, FileText, Star } from 'lucide-react';
import { useCart } from '../contexts/CartContext';
import { useAuth } from '../contexts/AuthContext';
import { PaymentService } from '../services/paymentService';
import { PAYMENT_CONFIG } from '../config/payment';

const paymentService = new PaymentService(PAYMENT_CONFIG);

export const CartDropdown: React.FC = () => {
  const { user } = useAuth();
  const { cartItems, cartCount, cartTotal, removeFromCart, clearCart } = useCart();
  const [isOpen, setIsOpen] = useState(false);
  const [bulkPaymentLoading, setBulkPaymentLoading] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  const handleBulkPurchase = async () => {
    if (!user) {
      alert('Please login to make purchases');
      return;
    }

    if (cartItems.length === 0) {
      alert('Your cart is empty');
      return;
    }

    setBulkPaymentLoading(true);

    try {
      // Create bulk order object
      const bulkOrder = {
        id: `bulk_${Date.now()}`,
        title: `Bulk Purchase (${cartItems.length} items)`,
        price: cartTotal,
        items: cartItems,
        type: 'bulk',
        description: `Bulk purchase of ${cartItems.length} study materials`
      };

      const handleBulkSuccess = (response: any) => {
        console.log('Bulk payment successful:', response);

        // Create individual purchases for each item
        const purchases = cartItems.map(item => ({
          id: `purchase_${Date.now()}_${item.materialId}`,
          userId: user.id,
          materialId: item.materialId,
          amount: item.price,
          paymentId: response.razorpay_payment_id || response.cf_payment_id,
          orderId: response.orderId,
          status: 'completed',
          purchaseDate: new Date().toISOString(),
          paymentMethod: response.paymentMethod || 'razorpay',
          gateway: response.gateway || 'razorpay',
          downloadCount: 0,
          bulkOrderId: bulkOrder.id
        }));

        // Save to localStorage
        const existingPurchases = JSON.parse(localStorage.getItem('userPurchases') || '[]');
        existingPurchases.push(...purchases);
        localStorage.setItem('userPurchases', JSON.stringify(existingPurchases));

        // Clear cart after successful purchase
        clearCart();
        setIsOpen(false);

        const gatewayName = response.gateway === 'cashfree' ? 'Cashfree' : 'Razorpay';
        alert(`Bulk payment successful via ${gatewayName}! You can now download all ${cartItems.length} materials.`);
        setBulkPaymentLoading(false);
      };

      const handleBulkError = (error: any) => {
        console.error('Bulk payment failed:', error);
        alert('Bulk payment failed. Please try again.');
        setBulkPaymentLoading(false);
      };

      // Use existing payment service
      if (typeof (window as any).Razorpay !== 'undefined') {
        await paymentService.initiateRazorpayPayment(
          bulkOrder,
          user,
          handleBulkSuccess,
          handleBulkError
        );
      } else {
        await paymentService.initiateCashfreePayment(
          bulkOrder,
          user,
          handleBulkSuccess,
          handleBulkError
        );
      }
    } catch (error) {
      console.error('Error initiating bulk payment:', error);
      alert('Failed to initiate payment. Please try again.');
      setBulkPaymentLoading(false);
    }
  };

  return (
    <div className="relative" ref={dropdownRef}>
      {/* Cart Icon Button */}
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="relative p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-lg transition-colors"
      >
        <ShoppingCart className="h-6 w-6" />
        {cartCount > 0 && (
          <span className="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center font-medium">
            {cartCount > 99 ? '99+' : cartCount}
          </span>
        )}
      </button>

      {/* Dropdown */}
      {isOpen && (
        <div className="absolute right-0 mt-2 w-96 bg-white rounded-xl shadow-lg border border-gray-200 z-50 max-h-96 overflow-hidden">
          {/* Header */}
          <div className="p-4 border-b border-gray-200">
            <div className="flex items-center justify-between">
              <h3 className="text-lg font-semibold text-gray-900">
                Shopping Cart ({cartCount})
              </h3>
              <button
                onClick={() => setIsOpen(false)}
                className="p-1 hover:bg-gray-100 rounded"
              >
                <X className="h-4 w-4" />
              </button>
            </div>
          </div>

          {/* Cart Items */}
          <div className="max-h-64 overflow-y-auto">
            {cartItems.length === 0 ? (
              <div className="p-8 text-center">
                <ShoppingCart className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <p className="text-gray-500">Your cart is empty</p>
                <p className="text-sm text-gray-400 mt-1">Add some study materials to get started</p>
              </div>
            ) : (
              <div className="p-2">
                {cartItems.map((item) => (
                  <div key={item.id} className="flex items-start space-x-3 p-3 hover:bg-gray-50 rounded-lg">
                    <div className="flex-shrink-0">
                      <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                        <FileText className="h-6 w-6 text-blue-600" />
                      </div>
                    </div>
                    
                    <div className="flex-1 min-w-0">
                      <h4 className="text-sm font-medium text-gray-900 truncate">
                        {item.title}
                      </h4>
                      <p className="text-xs text-gray-500 mt-1">
                        By {item.author} • {item.subject}
                      </p>
                      <div className="flex items-center mt-1">
                        <Star className="h-3 w-3 text-yellow-400 fill-current" />
                        <span className="text-xs text-gray-500 ml-1">{item.rating}</span>
                        <span className="text-xs text-gray-400 mx-1">•</span>
                        <span className="text-xs text-gray-500">{item.pages} pages</span>
                      </div>
                      <div className="flex items-center justify-between mt-2">
                        <span className="text-sm font-semibold text-gray-900">₹{item.price}</span>
                        <button
                          onClick={() => removeFromCart(item.materialId)}
                          className="p-1 text-red-500 hover:bg-red-50 rounded"
                          title="Remove from cart"
                        >
                          <Trash2 className="h-3 w-3" />
                        </button>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>

          {/* Footer */}
          {cartItems.length > 0 && (
            <div className="p-4 border-t border-gray-200 bg-gray-50">
              <div className="flex items-center justify-between mb-3">
                <span className="text-sm font-medium text-gray-700">Total:</span>
                <span className="text-lg font-bold text-gray-900">₹{cartTotal}</span>
              </div>
              
              <div className="flex space-x-2">
                <button
                  onClick={clearCart}
                  className="flex-1 px-3 py-2 text-sm text-gray-600 border border-gray-300 rounded-lg hover:bg-gray-100 transition-colors"
                >
                  Clear Cart
                </button>
                <button
                  onClick={handleBulkPurchase}
                  disabled={bulkPaymentLoading}
                  className="flex-2 px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center space-x-2"
                >
                  {bulkPaymentLoading ? (
                    <>
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                      <span>Processing...</span>
                    </>
                  ) : (
                    <span>Buy All (₹{cartTotal})</span>
                  )}
                </button>
              </div>
            </div>
          )}
        </div>
      )}
    </div>
  );
};