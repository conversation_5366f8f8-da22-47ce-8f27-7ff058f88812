import React, { useState, useEffect } from 'react';
import { Download, Clock, CheckCircle, XCircle, Eye, AlertTriangle } from 'lucide-react';
import { DownloadService } from '../services/downloadService';
import { useAuth } from '../contexts/AuthContext';

export function DownloadHistory() {
  const { user } = useAuth();
  const [downloads, setDownloads] = useState<any[]>([]);
  const [filter, setFilter] = useState<'all' | 'completed' | 'failed'>('all');
  const downloadService = new DownloadService();

  useEffect(() => {
    if (user) {
      const history = downloadService.getDownloadHistory(user.id);
      setDownloads(history);
    }
  }, [user]);

  const filteredDownloads = downloads.filter(download => {
    if (filter === 'all') return true;
    if (filter === 'completed') return download.status === 'completed';
    if (filter === 'failed') return download.status === 'failed';
    return true;
  });

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'failed':
        return <XCircle className="h-4 w-4 text-red-500" />;
      case 'in_progress':
        return <Download className="h-4 w-4 text-blue-500 animate-pulse" />;
      default:
        return <Clock className="h-4 w-4 text-gray-400" />;
    }
  };

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200">
      <div className="p-6 border-b border-gray-200">
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-semibold text-gray-900">Download History</h3>
          <div className="flex space-x-2">
            {['all', 'completed', 'failed'].map((filterType) => (
              <button
                key={filterType}
                onClick={() => setFilter(filterType as any)}
                className={`px-3 py-1 text-sm rounded-full capitalize ${
                  filter === filterType
                    ? 'bg-blue-100 text-blue-700'
                    : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
                }`}
              >
                {filterType}
              </button>
            ))}
          </div>
        </div>
      </div>

      <div className="divide-y divide-gray-200">
        {filteredDownloads.length === 0 ? (
          <div className="p-6 text-center text-gray-500">
            <Download className="h-12 w-12 mx-auto mb-4 text-gray-300" />
            <p>No downloads found</p>
          </div>
        ) : (
          filteredDownloads.map((download) => (
            <div key={download.id} className="p-4 hover:bg-gray-50">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  {getStatusIcon(download.status)}
                  <div>
                    <p className="text-sm font-medium text-gray-900">
                      Material ID: {download.materialId}
                    </p>
                    <p className="text-xs text-gray-500">
                      {new Date(download.attemptedAt).toLocaleString()}
                    </p>
                  </div>
                </div>
                
                <div className="flex items-center space-x-4">
                  {download.progress !== undefined && (
                    <div className="text-xs text-gray-500">
                      {download.progress}%
                    </div>
                  )}
                  
                  <span className={`px-2 py-1 text-xs rounded-full capitalize ${
                    download.status === 'completed' ? 'bg-green-100 text-green-700' :
                    download.status === 'failed' ? 'bg-red-100 text-red-700' :
                    'bg-gray-100 text-gray-700'
                  }`}>
                    {download.status}
                  </span>
                </div>
              </div>
              
              {download.status === 'failed' && (
                <div className="mt-2 p-2 bg-red-50 border border-red-200 rounded text-xs text-red-700">
                  <AlertTriangle className="h-3 w-3 inline mr-1" />
                  Download failed - Contact support if issue persists
                </div>
              )}
            </div>
          ))
        )}
      </div>
    </div>
  );
}