import React, { useState, useEffect } from 'react';
import { Download, CheckCircle, XCircle, Clock, AlertTriangle } from 'lucide-react';

interface DownloadProgressProps {
  downloadId: string;
  fileName: string;
  onComplete?: () => void;
  onError?: (error: string) => void;
}

export function DownloadProgress({ downloadId, fileName, onComplete, onError }: DownloadProgressProps) {
  const [progress, setProgress] = useState(0);
  const [status, setStatus] = useState<'downloading' | 'completed' | 'error' | 'preparing'>('preparing');
  const [error, setError] = useState<string>('');
  const [timeRemaining, setTimeRemaining] = useState<string>('');

  useEffect(() => {
    simulateDownload();
  }, [downloadId]);

  const simulateDownload = async () => {
    try {
      setStatus('downloading');
      const token = localStorage.getItem(`download_token_${downloadId}`);
      if (!token) {
        throw new Error('Download token not found. Please try again.');
      }
      const payload = JSON.parse(atob(token));
      const materialId = payload.materialId;
      const userId = payload.userId;
      for (let i = 0; i <= 90; i += 15) {
        await new Promise(resolve => setTimeout(resolve, 300));
        setProgress(i);
        const remaining = Math.max(0, (100 - i) * 0.3);
        setTimeRemaining(`${remaining.toFixed(0)}s remaining`);
        const { DownloadService } = await import('../services/downloadService');
        const downloadService = new DownloadService();
        await downloadService.updateDownloadProgress(downloadId, i, 'in_progress');
      }
      const { materialDownloadService } = await import('../services/materialDownloadService');
      const result = await materialDownloadService.downloadMaterial(materialId, userId);
      if (!result.success) {
        throw new Error(result.error || 'Please Wait checking your purchase status.');
      }
      setProgress(100);
      setTimeRemaining('');
      const { DownloadService } = await import('../services/downloadService');
      const downloadService = new DownloadService();
      await downloadService.markDownloadCompleted(downloadId);
      setStatus('completed');
      onComplete?.();
    } catch (err) {
      setStatus('error');
      const errorMessage = err.message || 'Download failed. Please try again.';
      setError(errorMessage);
      onError?.(errorMessage);
    }
  };

  const getStatusIcon = () => {
    switch (status) {
      case 'preparing':
        return <Clock className="h-5 w-5 text-blue-500 animate-pulse" />;
      case 'downloading':
        return <Download className="h-5 w-5 text-blue-500 animate-bounce" />;
      case 'completed':
        return <CheckCircle className="h-5 w-5 text-green-500" />;
      case 'error':
        return <XCircle className="h-5 w-5 text-red-500" />;
    }
  };

  const getStatusText = () => {
    switch (status) {
      case 'preparing':
        return 'Preparing secure download...';
      case 'downloading':
        return `Downloading ${fileName}... ${progress}%`;
      case 'completed':
        return 'Download completed successfully!';
      case 'error':
        return error || 'Download failed';
    }
  };

  return (
    <div className="bg-white border border-gray-200 rounded-lg p-4 shadow-sm">
      <div className="flex items-center space-x-3">
        {getStatusIcon()}
        <div className="flex-1">
          <p className="text-sm font-medium text-gray-900">{getStatusText()}</p>
          {timeRemaining && (
            <p className="text-xs text-gray-500">{timeRemaining}</p>
          )}
        </div>
      </div>
      {status === 'downloading' && (
        <div className="mt-3">
          <div className="bg-gray-200 rounded-full h-2">
            <div
              className="bg-blue-500 h-2 rounded-full transition-all duration-300"
              style={{ width: `${progress}%` }}
            />
          </div>
        </div>
      )}
      {status === 'error' && (
        <div className="mt-2 p-2 bg-red-50 border border-red-200 rounded text-sm text-red-700">
          <div className="flex items-center space-x-2">
            <AlertTriangle className="h-4 w-4" />
            <span>{error}</span>
          </div>
        </div>
      )}
    </div>
  );
}