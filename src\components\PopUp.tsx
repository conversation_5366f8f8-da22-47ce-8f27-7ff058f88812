"use client"

import type React from "react"
import { useEffect, useState } from "react"
import { X } from "lucide-react"

interface PopUpProps {
  message: string
  type: "success" | "error" | "info"
  duration?: number
}

function PopUp({ message, type, duration = 5000 }: PopUpProps) {
  const [isVisible, setIsVisible] = useState(true)

  useEffect(() => {
    const timer = setTimeout(() => {
      setIsVisible(false)
    }, duration)

    return () => clearTimeout(timer)
  }, [duration])

  if (!isVisible) return null

  const bgColor = {
    success: "bg-green-50 border-green-200 text-green-600",
    error: "bg-red-50 border-red-200 text-red-600",
    info: "bg-blue-50 border-blue-200 text-blue-600"
  }[type]

  return (
    <div className={`fixed bottom-4 right-4 max-w-sm w-full p-4 rounded-lg border shadow-lg ${bgColor} flex items-center justify-between`}>
      <span>{message}</span>
      <button
        onClick={() => setIsVisible(false)}
        className="ml-4 text-gray-500 hover:text-gray-700"
      >
        <X className="h-5 w-5" />
      </button>
    </div>
  )
}

export default PopUp