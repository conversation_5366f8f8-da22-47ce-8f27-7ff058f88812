import React, { useState } from 'react';
import { Download, FileText, Loader2, X } from 'lucide-react';
import jsPDF from 'jspdf';
import 'jspdf-autotable';

interface Question {
  id: string;
  question: string;
  options: string[];
  correctAnswer: number | string;
  subject?: string;
  difficulty?: string;
  explanation?: string;
  marks?: number;
}

interface DetailedAnswer {
  questionId: string;
  question: string;
  options: string[];
  correctAnswer: number;
  userAnswer: string | null;
  isCorrect: boolean;
  isAttempted: boolean;
  subject: string;
  difficulty: string;
}

interface ExamResult {
  id: string;
  examId: string;
  userId: string;
  examTitle: string;
  score: number;
  totalQuestions: number;
  totalMarks: number;
  correctAnswers: number;
  incorrectAnswers: number;
  unanswered: number;
  timeTakenMinutes: string;
  completedAt: string;
  detailedAnswers?: DetailedAnswer[];
}

interface SubmissionPayload {
  examId: string;
  userId: string;
  timeTakenMinutes: string;
  answers: { [key: string]: string };
  detailedAnswers: DetailedAnswer[];
  examTitle: string;
  examDuration: number;
  antiCheatWarnings: any[];
  tabSwitchCount: number;
}

interface ResultPDFGeneratorProps {
  result: ExamResult;
  questions?: Question[];
  submissionPayload?: SubmissionPayload;
  submissionResponse?: any;
}

declare module 'jspdf' {
  interface jsPDF {
    autoTable: (options: any) => jsPDF;
  }
}

/* ---------------------- Helpers to mirror <QuestionText /> ---------------------- */

type MarkerType = 'bullet' | 'dash' | 'star' | 'none';

const MARKERS: Record<MarkerType, string> = {
  bullet: '•',
  dash: '-',
  star: '*',
  none: '',
};

function normalizeText(raw: string): string {
  return raw
    .replace(/\r\n?/g, '\n')   // windows/mac -> \n
    .replace(/\\n/g, '\n')     // literal "\n" from CSV -> newline
    .replace(/\t/g, '    ');   // tabs -> spaces
}

/**
 * If there are NO real newlines but there are inline “ 1. … 2. … 3. …” chunks,
 * split them safely into lines. We only split patterns like " 1. " (space-1-dot-space)
 * for numbers 1..10 to avoid things like "Article 110(3)".
 */
function splitInlineNumberedToLines(text: string): string[] {
  if (text.includes('\n')) return text.split('\n');

  const hasInline =
    /\s1\.\s/.test(text) && (/\s2\.\s/.test(text) || /\s3\.\s/.test(text));

  if (!hasInline) return [text];

  let processed = text;
  for (let i = 10; i >= 1; i--) {
    const re = new RegExp(`\\s${i}\\.\\s`, 'g');
    processed = processed.replace(re, `\n${i}. `);
  }
  return processed.split('\n');
}

/**
 * If every non-empty line starts with "1." (or "(1)") then renumber sequentially.
 */
function renumberIfAllOnes(lines: string[]): string[] {
  const parsed = lines
    .map((l) => {
      const m = l.match(/^\s*(\(?)(\d+)([\.\)])\s+(.*)$/);
      if (!m) return null;
      const [, left, num, sep, rest] = m;
      return { left, num: parseInt(num, 10), sep, rest };
    })
    .filter(Boolean) as Array<{ left: string; num: number; sep: string; rest: string }>;

  const contentLines = lines.filter((l) => l.trim().length > 0);
  if (contentLines.length === 0) return lines;

  const allEnumerated = parsed.length === contentLines.length;
  const allOnes = allEnumerated && parsed.every((p) => p.num === 1);

  if (!allOnes) return lines;

  // Renumber 1..n using the first line's bracket/sep style
  const first = parsed[0];
  let counter = 1;
  return lines.map((l) => {
    const m = l.match(/^\s*(\(?)(\d+)([\.\)])\s+(.*)$/);
    if (!m) return l;
    const [, left, , sep, rest] = m;
    const brLeft = left || first.left || '';
    const brRight = brLeft === '(' ? ')' : '';
    const finalSep = sep || first.sep || '.';
    const newNum = counter++;
    return `${brLeft}${newNum}${finalSep}${brRight} ${rest}`;
  });
}

/**
 * Add markers like in <QuestionText /> when a line doesn’t already have
 * numbering/letter bullets. Skips empty lines.
 */
function addLineMarkers(lines: string[], markerType: MarkerType = 'bullet'): string[] {
  if (markerType === 'none') return lines;

  return lines.map((line) => {
    const t = line.trim();
    if (!t) return ''; // keep blank line
    // already numbered or lettered? (1. …), 1. …, a) …, (a) …
    if (
      /^(\(?\d+[\.\)]|\(?[a-dA-D][\)\.])\s+/.test(t) ||
      /^[•\-*]\s+/.test(t)
    ) {
      return t;
    }
    return `${MARKERS[markerType]} ${t}`;
  });
}

/**
 * Prepare final printable lines for the question:
 * - normalize text
 * - split inline “1. … 2. …” if needed
 * - renumber if every line was “1.”
 * - add markers like QuestionText (bullet by default)
 */
function buildQuestionLines(
  raw: string,
  { addMarkers = true, markerType = 'bullet' as MarkerType } = {}
): string[] {
  const normalized = normalizeText(raw);
  const split = splitInlineNumberedToLines(normalized);
  const renumbered = renumberIfAllOnes(split);
  return addMarkers ? addLineMarkers(renumbered, markerType) : renumbered;
}

/* --------------------------- PDF drawing helpers --------------------------- */

function ensureSpace(pdf: jsPDF, needed: number, y: number, margin: number) {
  const pageHeight = pdf.internal.pageSize.getHeight();
  if (y + needed > pageHeight - margin) {
    pdf.addPage();
    return margin;
  }
  return y;
}

function drawWrappedLines(
  pdf: jsPDF,
  textLines: string[],
  x: number,
  y: number,
  maxWidth: number,
  lineHeight = 6,
  margin = 15
) {
  const pageHeight = pdf.internal.pageSize.getHeight();
  let cursorY = y;

  for (const line of textLines) {
    const wrapped = pdf.splitTextToSize(line, maxWidth);
    for (const wl of wrapped) {
      if (cursorY > pageHeight - margin) {
        pdf.addPage();
        cursorY = margin;
      }
      pdf.text(wl, x, cursorY);
      cursorY += lineHeight;
    }
  }
  return cursorY;
}

/* -------------------------------- Component -------------------------------- */

const ResultPDFGenerator: React.FC<ResultPDFGeneratorProps> = ({
  result,
  questions = [],
  submissionPayload,
}) => {
  const [isGenerating, setIsGenerating] = useState(false);
  const [showPreview, setShowPreview] = useState(false);
  const [pdfBlob, setPdfBlob] = useState<Blob | null>(null);

  const generatePDF = async () => {
    setIsGenerating(true);

    try {
      const pdf = new jsPDF();
      const pageWidth = pdf.internal.pageSize.getWidth();
      const pageHeight = pdf.internal.pageSize.getHeight();
      const margin = 15;
      const contentWidth = pageWidth - margin * 2;
      let y = margin + 5;

      // Header
      pdf.setFontSize(24);
      pdf.setFont('helvetica', 'bold');
      pdf.setTextColor(59, 130, 246);
      pdf.text('COMPREHENSIVE EXAM REPORT', pageWidth / 2, y, { align: 'center' });

      y += 12;
      pdf.setFontSize(18);
      pdf.setFont('helvetica', 'normal');
      pdf.setTextColor(0, 0, 0);
      pdf.text(result.examTitle, pageWidth / 2, y, { align: 'center' });

      y += 15;

      // Summary box
      pdf.setDrawColor(59, 130, 246);
      pdf.setFillColor(239, 246, 255);
      pdf.roundedRect(margin, y, contentWidth, 50, 5, 5, 'FD');

      y += 12;
      pdf.setFontSize(14);
      pdf.setFont('helvetica', 'bold');
      pdf.setTextColor(59, 130, 246);
      pdf.text('EXAM SUMMARY', margin + 5, y);

      y += 10;
      pdf.setFont('helvetica', 'normal');
      pdf.setTextColor(0, 0, 0);
      pdf.setFontSize(11);

      const summaryData = [
        [`Total Questions: ${result.totalQuestions}`, `Total Marks: ${result.totalMarks}`],
        [`Your Score: ${result.score}`, `Time Taken: ${result.timeTakenMinutes} minutes`],
        [`Correct Answers: ${result.correctAnswers}`, `Incorrect Answers: ${result.incorrectAnswers}`],
        [`Unanswered: ${result.unanswered}`, `Completed: ${new Date(result.completedAt).toLocaleDateString()}`],
      ];

      summaryData.forEach((row) => {
        pdf.text(row[0], margin + 5, y);
        pdf.text(row[1], pageWidth / 2 + 10, y);
        y += 7;
      });

      y += 10;

      // Grade band
      const percentage = result.totalMarks > 0 ? (result.score / result.totalMarks) * 100 : 0;
      let grade = 'F';
      let gradeColor: [number, number, number] = [220, 38, 38];
      if (percentage >= 90) { grade = 'A+'; gradeColor = [34, 197, 94]; }
      else if (percentage >= 80) { grade = 'A'; gradeColor = [34, 197, 94]; }
      else if (percentage >= 70) { grade = 'B+'; gradeColor = [59, 130, 246]; }
      else if (percentage >= 60) { grade = 'B'; gradeColor = [59, 130, 246]; }
      else if (percentage >= 50) { grade = 'C'; gradeColor = [234, 179, 8]; }

      pdf.setFillColor(...gradeColor);
      pdf.roundedRect(margin, y, contentWidth, 30, 5, 5, 'F');

      pdf.setTextColor(255, 255, 255);
      pdf.setFontSize(20);
      pdf.setFont('helvetica', 'bold');
      pdf.text(`GRADE: ${grade} (${percentage.toFixed(1)}%)`, pageWidth / 2, y + 20, { align: 'center' });

      y += 45;
      pdf.setTextColor(0, 0, 0);

      // Detailed section
      if (questions.length > 0) {
        pdf.addPage();
        y = margin + 5;

        pdf.setFontSize(16);
        pdf.setFont('helvetica', 'bold');
        pdf.setTextColor(59, 130, 246);
        pdf.text('DETAILED QUESTION ANALYSIS', margin, y);
        y += 15;

        const optionIndent = 10;
        const questionIndent = 10;
        const lineHeight = 6;

        questions.forEach((q, idx) => {
          // Page break if header won't fit
          y = ensureSpace(pdf, 30, y, margin);

          // Header row: Question N + status
          pdf.setFontSize(12);
          pdf.setFont('helvetica', 'bold');
          pdf.setTextColor(0, 0, 0);

          const userAnswer = submissionPayload?.answers?.[q.id] ?? null;
          const correctAnswerText =
            typeof q.correctAnswer === 'number'
              ? q.options[q.correctAnswer]
              : q.correctAnswer;

          const isAttempted = userAnswer !== null && userAnswer !== undefined && `${userAnswer}`.length > 0;
          const isCorrect = isAttempted && `${userAnswer}` === `${correctAnswerText}`;

          const leftTitle = `Question ${idx + 1}`;
          let statusText = 'UNANSWERED';
          let statusColor: [number, number, number] = [156, 163, 175];
          if (isCorrect) { statusText = 'CORRECT'; statusColor = [34, 197, 94]; }
          else if (isAttempted) { statusText = 'INCORRECT'; statusColor = [220, 38, 38]; }

          pdf.text(leftTitle, margin, y);
          pdf.setTextColor(...statusColor);
          pdf.text(statusText, pageWidth - margin - 35, y);

          y += 8;

          // Subject + difficulty row
          pdf.setTextColor(100, 100, 100);
          pdf.setFont('helvetica', 'italic');
          pdf.setFontSize(9);
          pdf.text(
            `Subject: ${q.subject || 'General'} | Difficulty: ${q.difficulty || 'Medium'}`,
            margin,
            y
          );
          y += 8;

          // QUESTION TEXT — mirror <QuestionText /> behavior
          pdf.setTextColor(0, 0, 0);
          pdf.setFont('helvetica', 'normal');
          pdf.setFontSize(11);

          const questionLines = buildQuestionLines(q.question, {
            addMarkers: true,
            markerType: 'none',
          });

          // Draw question lines with wrapping & page breaks
          y = drawWrappedLines(
            pdf,
            questionLines,
            margin + questionIndent,
            y,
            contentWidth - questionIndent,
            lineHeight,
            margin
          );

          y += 4;

          // OPTIONS
          q.options.forEach((opt, optIdx) => {
            const label = String.fromCharCode(65 + optIdx); // A B C D
            const isCorrectOption =
              (typeof q.correctAnswer === 'number' && optIdx === q.correctAnswer) ||
              (typeof q.correctAnswer === 'string' && opt === q.correctAnswer);
            const isUserSelection = `${userAnswer}` === `${opt}`;

            let color: [number, number, number] = [0, 0, 0];
            let fontStyle: 'normal' | 'bold' = 'normal';
            if (isCorrectOption && isUserSelection) { color = [34, 197, 94]; fontStyle = 'bold'; }
            else if (isCorrectOption) { color = [34, 197, 94]; fontStyle = 'bold'; }
            else if (isUserSelection) { color = [220, 38, 38]; fontStyle = 'bold'; }

            pdf.setTextColor(...color);
            pdf.setFont('helvetica', fontStyle);
            pdf.setFontSize(10);

            const optionText = `${label}) ${normalizeText(opt)}`; // support \n inside options too
            const optionLinesRaw = optionText.split('\n');
            // Wrap each option line
            const wrapped: string[] = [];
            optionLinesRaw.forEach((ln) => {
              const w = pdf.splitTextToSize(ln, contentWidth - questionIndent - optionIndent);
              wrapped.push(...w);
            });

            // draw with page breaks
            wrapped.forEach((wl) => {
              if (y > pageHeight - margin) {
                pdf.addPage();
                y = margin;
              }
              pdf.text(wl, margin + questionIndent + optionIndent, y);
              y += lineHeight;
            });

            y += 2;
          });

          // EXPLANATION (for all questions with an explanation, using the same pattern as questions)
          if (q.explanation) {
            y += 4;
            pdf.setTextColor(0, 0, 0);
            pdf.setFont('helvetica', 'bold');
            pdf.setFontSize(9);
            y = ensureSpace(pdf, 18, y, margin);
            pdf.text('Explanation:', margin + questionIndent, y);

            pdf.setFont('helvetica', 'normal');
            pdf.setTextColor(0, 0, 0);
            const explanationLines = buildQuestionLines(q.explanation, {
              addMarkers: true,
              markerType: 'none',
            });
            y = drawWrappedLines(
              pdf,
              explanationLines,
              margin + questionIndent,
              y + 6,
              contentWidth - questionIndent,
              5,
              margin
            );
            y += 2;
          }

          // separator
          y += 6;
          pdf.setDrawColor(229, 231, 235);
          pdf.setLineWidth(0.5);
          pdf.line(margin, y, pageWidth - margin, y);
          y += 10;
          // reset colors/fonts
          pdf.setTextColor(0, 0, 0);
          pdf.setFont('helvetica', 'normal');
        });
      }

      // Footer page numbers
      const totalPages = pdf.internal.pages.length - 1;
      for (let i = 1; i <= totalPages; i++) {
        pdf.setPage(i);
        pdf.setFontSize(8);
        pdf.setTextColor(107, 114, 128);
        pdf.text(`Page ${i} of ${totalPages}`, pageWidth - 30, pageHeight - 10);
        pdf.text(`Generated on ${new Date().toLocaleDateString()}`, 15, pageHeight - 10);
      }

      const blob = pdf.output('blob');
      setPdfBlob(blob);
      setShowPreview(true);
    } catch (error) {
      console.error('Error generating PDF:', error);
      alert('Error generating PDF. Please try again.');
    } finally {
      setIsGenerating(false);
    }
  };

  const downloadPDF = () => {
    if (pdfBlob) {
      const url = URL.createObjectURL(pdfBlob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `${result.examTitle.replace(/[^a-z0-9]/gi, '_')}_Comprehensive_Report.pdf`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(url);
    }
  };

  return (
    <>
      <button
        onClick={generatePDF}
        disabled={isGenerating}
        className="bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center space-x-2 transition-colors"
      >
        {isGenerating ? (
          <>
            <Loader2 className="h-5 w-5 animate-spin" />
            <span>Generating Comprehensive Report...</span>
          </>
        ) : (
          <>
            <FileText className="h-5 w-5" />
            <span>Download Comprehensive Report</span>
          </>
        )}
      </button>

      {showPreview && pdfBlob && (
        <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-xl shadow-2xl max-w-6xl w-full h-[90vh] flex flex-col">
            <div className="flex items-center justify-between p-6 border-b bg-gray-50 rounded-t-xl">
              <div className="flex items-center space-x-3">
                <FileText className="h-6 w-6 text-blue-600" />
                <div>
                  <h3 className="text-xl font-semibold text-gray-900">Comprehensive Exam Report</h3>
                  <p className="text-sm text-gray-600">{result.examTitle}</p>
                </div>
              </div>
              <button
                onClick={() => setShowPreview(false)}
                className="text-gray-400 hover:text-gray-600 transition-colors p-2 hover:bg-gray-200 rounded-lg"
              >
                <X className="h-6 w-6" />
              </button>
            </div>

            <div className="flex-1 p-6 overflow-hidden">
              <div className="h-full border rounded-lg overflow-hidden shadow-inner">
                <iframe
                  src={URL.createObjectURL(pdfBlob)}
                  className="w-full h-full"
                  title="PDF Preview"
                  style={{ minHeight: '600px' }}
                />
              </div>
            </div>

            <div className="flex items-center justify-between p-6 border-t bg-gray-50 rounded-b-xl">
              <div className="text-sm text-gray-600">
                <span className="font-medium">Report includes:</span> Summary, Performance Analysis, All {questions.length} Questions with Answers & Explanations
              </div>
              <div className="flex items-center space-x-3">
                <button
                  onClick={() => setShowPreview(false)}
                  className="px-4 py-2 text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors font-medium"
                >
                  Close Preview
                </button>
                <button
                  onClick={downloadPDF}
                  className="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors flex items-center space-x-2 font-medium"
                >
                  <Download className="h-4 w-4" />
                  <span>Download PDF</span>
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </>
  );
};

export default ResultPDFGenerator;