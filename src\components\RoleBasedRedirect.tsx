"use client"

import type React from "react"
import { useEffect } from "react"
import { useNavigate, useLocation } from "react-router-dom"
import { useAuth } from "../contexts/AuthContext"

interface RoleBasedRedirectProps {
  children: React.ReactNode
}

function RoleBasedRedirect({ children }: RoleBasedRedirectProps) {
  const { user } = useAuth()
  const navigate = useNavigate()
  const location = useLocation()

  useEffect(() => {
    if (user && location.pathname === "/app") {
      // If user is admin and on base /app route, redirect to admin dashboard
      if (user.role === "admin") {
        navigate("/app/admin", { replace: true })
      }
      // Students stay on /app (dashboard)
    }
  }, [user, location.pathname, navigate])

  return <>{children}</>
}

export default RoleBasedRedirect
