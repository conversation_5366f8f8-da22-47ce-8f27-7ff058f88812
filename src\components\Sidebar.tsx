"use client"
import { NavLink, useNavigate } from "react-router-dom"
import {
  Home,
  FileText,
  BookOpen,
  User,
  Users,
  Bell,
  BarChart3,
  Calendar,
  X,
  ShoppingCart,
  ChevronRight,
  LogOut,
} from "lucide-react"
import { useAuth } from "../contexts/AuthContext"
import { useCart } from "../contexts/CartContext"

interface SidebarProps {
  isOpen?: boolean
  onClose?: () => void
}

function Sidebar({ isOpen = false, onClose }: SidebarProps) {
  const { user, logout } = useAuth()
  const { cartCount } = useCart()
  const navigate = useNavigate()

  const studentLinks = [
    { to: "/app", icon: Home, label: "Dashboard", exact: true },
    { to: "/app/daily-quiz", icon: Calendar, label: "Daily Quiz" },
    { to: "/app/exams", icon: FileText, label: "Exams" },
    { to: "/app/materials", icon: BookOpen, label: "Study Materials" },
    {
      to: "/app/cart",
      icon: ShoppingCart,
      label: "Cart",
      badge: cartCount > 0 ? cartCount : undefined,
    },
    { to: "/app/profile", icon: User, label: "Profile" },
  ]

  const adminLinks = [
    { to: "/app/admin", icon: BarChart3, label: "Admin Dashboard", exact: true },
    { to: "/app/admin/users", icon: Users, label: "Users" },
    { to: "/app/admin/exams", icon: FileText, label: "Exams" },
    { to: "/app/admin/materials", icon: BookOpen, label: "Materials" },
    { to: "/app/admin/notifications", icon: Bell, label: "Notifications" },
  ]

  const links = user?.role === "admin" ? adminLinks : studentLinks

  const handleLogout = async () => {
    await logout()
    navigate("/")
  }

  const renderLink = (link: any) => (
    <NavLink
      key={link.to}
      to={link.to}
      end={link.exact}
      className={({ isActive }) =>
        `group flex items-center justify-between px-4 py-3 rounded-xl text-sm font-medium transition-all duration-200 ${
          isActive
            ? "bg-gradient-to-r from-blue-500 to-indigo-600 text-white shadow-lg shadow-blue-500/25"
            : "text-gray-700 hover:bg-gray-50 hover:text-gray-900 hover:shadow-sm"
        }`
      }
      onClick={onClose}
    >
      <div className="flex items-center space-x-3">
        <link.icon className="h-5 w-5 transition-transform group-hover:scale-110" />
        <span className="font-medium">{link.label}</span>
      </div>
      <div className="flex items-center space-x-2">
        {link.badge && (
          <span className="bg-red-500 text-white text-xs rounded-full h-6 w-6 flex items-center justify-center font-semibold shadow-sm">
            {link.badge > 99 ? "99+" : link.badge}
          </span>
        )}
        <ChevronRight className="h-4 w-4 opacity-0 group-hover:opacity-100 transition-opacity" />
      </div>
    </NavLink>
  )

  return (
    <>
      {/* Mobile Overlay */}
      {isOpen && <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-30 lg:hidden" onClick={onClose} />}

      {/* Sidebar */}
      <aside
        className={`fixed top-16 bottom-0 left-0 z-40 w-72 bg-white/95 backdrop-blur-xl shadow-2xl border-r border-gray-200/50 transform transition-all duration-300 ease-out ${
          isOpen ? "translate-x-0" : "-translate-x-full"
        } lg:translate-x-0`}
      >
        <div className="flex flex-col h-full">
          {/* Navigation */}
          <nav className="flex-1 px-4 py-6 space-y-2 overflow-y-auto">
            <div className="mb-6">
              <p className="text-xs font-semibold text-gray-400 uppercase tracking-wider mb-3 px-2">Navigation</p>
              <div className="space-y-1">{links.map(renderLink)}</div>
            </div>
          </nav>

          {/* User Profile Section */}
          <div className="p-4 border-t border-gray-200/50 bg-gray-50/50">
            <div className="flex items-center justify-between p-3 rounded-xl bg-white shadow-sm border border-gray-200/50">
              <div className="flex items-center space-x-3">
                <div className="w-10 h-10 bg-gradient-to-r from-blue-100 to-indigo-100 rounded-full flex items-center justify-center">
                  <User className="h-5 w-5 text-blue-600" />
                </div>
                <div className="flex-1 min-w-0">
                  <p className="text-sm font-semibold text-gray-900 truncate">{user?.name}</p>
                  <p className="text-xs text-gray-500 capitalize">{user?.role}</p>
                </div>
              </div>
              <button
                onClick={handleLogout}
                className="p-2 text-gray-400 hover:text-red-500 hover:bg-red-50 rounded-lg transition-colors"
                title="Sign Out"
              >
                <LogOut className="h-4 w-4" />
              </button>
            </div>
          </div>
        </div>
      </aside>
    </>
  )
}

export default Sidebar
