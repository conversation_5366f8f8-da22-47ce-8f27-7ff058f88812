import React, { useEffect, useState } from 'react';
import { useAuth } from '../contexts/AuthContext';
import { getAuth } from 'firebase/auth';

/**
 * Debug component to verify Firebase UID usage
 * Add this component temporarily to any page to debug user ID issues
 */
const UserIdDebugger: React.FC = () => {
  const { user } = useAuth();
  const [firebaseUser, setFirebaseUser] = useState<any>(null);
  const [debugInfo, setDebugInfo] = useState<any>({});

  useEffect(() => {
    const auth = getAuth();
    const currentFirebaseUser = auth.currentUser;
    setFirebaseUser(currentFirebaseUser);

    // Collect debug information
    const info = {
      authContextUser: user,
      firebaseCurrentUser: currentFirebaseUser ? {
        uid: currentFirebaseUser.uid,
        email: currentFirebaseUser.email,
        displayName: currentFirebaseUser.displayName
      } : null,
      localStorage: {
        hasUserData: !!localStorage.getItem('user'),
        userData: localStorage.getItem('user')
      },
      sessionStorage: {
        hasUserData: !!sessionStorage.getItem('user'),
        userData: sessionStorage.getItem('user')
      }
    };
    
    setDebugInfo(info);
    console.log('🔍 User ID Debug Info:', info);
  }, [user]);

  if (!user && !firebaseUser) {
    return (
      <div style={{ 
        position: 'fixed', 
        top: '10px', 
        right: '10px', 
        background: '#ff6b6b', 
        color: 'white', 
        padding: '10px', 
        borderRadius: '5px',
        fontSize: '12px',
        zIndex: 9999,
        maxWidth: '300px'
      }}>
        <strong>🚨 No User Found</strong>
        <div>Neither AuthContext nor Firebase has user data</div>
      </div>
    );
  }

  const isFirebaseUid = (id: string) => {
    // Firebase UIDs are typically 28 characters long and alphanumeric
    return id && id.length >= 20 && /^[a-zA-Z0-9]+$/.test(id);
  };

  const userIdStatus = user?.id ? (
    isFirebaseUid(user.id) ? '✅ Valid Firebase UID' : '❌ Not Firebase UID'
  ) : '❌ No user.id';

  return (
    <div style={{ 
      position: 'fixed', 
      top: '10px', 
      right: '10px', 
      background: '#2c3e50', 
      color: 'white', 
      padding: '15px', 
      borderRadius: '8px',
      fontSize: '11px',
      zIndex: 9999,
      maxWidth: '350px',
      fontFamily: 'monospace'
    }}>
      <div style={{ marginBottom: '10px', fontWeight: 'bold', color: '#3498db' }}>
        🔍 User ID Debugger
      </div>
      
      <div style={{ marginBottom: '8px' }}>
        <strong>AuthContext user.id:</strong>
        <div style={{ color: isFirebaseUid(user?.id || '') ? '#2ecc71' : '#e74c3c' }}>
          {user?.id || 'undefined'} {userIdStatus}
        </div>
      </div>

      <div style={{ marginBottom: '8px' }}>
        <strong>Firebase currentUser.uid:</strong>
        <div style={{ color: firebaseUser?.uid ? '#2ecc71' : '#e74c3c' }}>
          {firebaseUser?.uid || 'undefined'}
        </div>
      </div>

      <div style={{ marginBottom: '8px' }}>
        <strong>User Email:</strong>
        <div>{user?.email || firebaseUser?.email || 'undefined'}</div>
      </div>

      <div style={{ marginBottom: '8px' }}>
        <strong>User Name:</strong>
        <div>{user?.name || firebaseUser?.displayName || 'undefined'}</div>
      </div>

      <div style={{ marginBottom: '8px' }}>
        <strong>Expected API Call:</strong>
        <div style={{ color: '#f39c12', wordBreak: 'break-all' }}>
          /api/userProfiles?userId={user?.id || 'MISSING'}
        </div>
      </div>

      <div style={{ fontSize: '10px', color: '#95a5a6', marginTop: '10px' }}>
        Check browser console for full debug info
      </div>
    </div>
  );
};

export default UserIdDebugger;
