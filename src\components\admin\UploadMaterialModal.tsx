"use client"

import type React from "react"
import { useState, useRef } from "react"
import { X, Upload, FileText, CheckCircle, XCircle, AlertTriangle } from "lucide-react"

interface UploadMaterialModalProps {
  isOpen: boolean
  onClose: () => void
  onUpload: (file: File, data: any) => Promise<void>
  uploading: boolean
}

export function UploadMaterialModal({ isOpen, onClose, onUpload, uploading }: UploadMaterialModalProps) {
  const [file, setFile] = useState<File | null>(null)
  const [dragActive, setDragActive] = useState(false)
  const [uploadProgress, setUploadProgress] = useState(0)
  const [uploadStatus, setUploadStatus] = useState<"idle" | "uploading" | "processing" | "completed" | "error">("idle")
  const [uploadError, setUploadError] = useState<string>("")
  const fileInputRef = useRef<HTMLInputElement>(null)

  const [formData, setFormData] = useState({
    title: "",
    description: "",
    type: "PDF",
    subject: "",
    price: "",
    originalPrice: "",
    author: "",
    isPremium: false,
    thumbnailUrl: "",
    status: "published",
    pages: "",
    tags: "",
  })

  const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFile = e.target.files?.[0]
    if (selectedFile) {
      if (selectedFile.type === "application/pdf") {
        setFile(selectedFile)
      } else {
        alert("Please select a PDF file only")
        e.target.value = ""
      }
    }
  }

  const handleDrag = (e: React.DragEvent) => {
    e.preventDefault()
    e.stopPropagation()
    if (e.type === "dragenter" || e.type === "dragover") {
      setDragActive(true)
    } else if (e.type === "dragleave") {
      setDragActive(false)
    }
  }

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault()
    e.stopPropagation()
    setDragActive(false)
    const droppedFile = e.dataTransfer.files?.[0]
    if (droppedFile) {
      if (droppedFile.type === "application/pdf") {
        setFile(droppedFile)
      } else {
        alert("Please select a PDF file only")
      }
    }
  }

  const triggerFileInput = () => {
    fileInputRef.current?.click()
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!file || !formData.title.trim()) {
      alert("Please select a file and enter a title")
      return
    }

    try {
      setUploadStatus("uploading")
      setUploadProgress(0)
      setUploadError("")

      // Call the parent's upload handler
      await onUpload(file, {
        ...formData,
        isPremium: formData.isPremium ? "1" : "0",
      })

      setUploadStatus("completed")
      setUploadProgress(100)

      // Reset form after successful upload
      setTimeout(() => {
        setFile(null)
        setUploadProgress(0)
        setUploadStatus("idle")
        setFormData({
          title: "",
          description: "",
          type: "PDF",
          subject: "",
          price: "",
          originalPrice: "",
          author: "",
          isPremium: false,
          thumbnailUrl: "",
          status: "published",
          pages: "",
          tags: "",
        })
        onClose()
      }, 1500)
    } catch (error: any) {
      setUploadStatus("error")
      setUploadError(error.message || "Upload failed")
      setUploadProgress(0)
    }
  }

  const getUploadStatusText = () => {
    switch (uploadStatus) {
      case "uploading":
        return `Uploading ${file?.name}...`
      case "processing":
        return "Processing and saving metadata..."
      case "completed":
        return "Upload completed successfully!"
      case "error":
        return uploadError || "Upload failed"
      default:
        return ""
    }
  }

  const getUploadStatusIcon = () => {
    switch (uploadStatus) {
      case "uploading":
        return <Upload className="h-5 w-5 text-blue-500 animate-bounce" />
      case "processing":
        return <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-blue-500"></div>
      case "completed":
        return <CheckCircle className="h-5 w-5 text-green-500" />
      case "error":
        return <XCircle className="h-5 w-5 text-red-500" />
      default:
        return null
    }
  }

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 w-full max-w-2xl max-h-[90vh] overflow-y-auto">
        <div className="flex justify-between items-center mb-6">
          <h2 className="text-xl font-bold text-gray-900">Upload Study Material</h2>
          <button onClick={onClose} className="text-gray-400 hover:text-gray-600" disabled={uploading}>
            <X className="h-6 w-6" />
          </button>
        </div>

        <form onSubmit={handleSubmit} className="space-y-4">
          {/* File Upload Section */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">PDF File *</label>
            <input
              ref={fileInputRef}
              type="file"
              accept=".pdf,application/pdf"
              onChange={handleFileSelect}
              className="hidden"
            />
            <div
              className={`border-2 border-dashed rounded-lg p-8 text-center transition-all cursor-pointer ${
                dragActive
                  ? "border-blue-500 bg-blue-50"
                  : file
                    ? "border-green-500 bg-green-50"
                    : "border-gray-300 hover:border-blue-400 hover:bg-gray-50"
              }`}
              onDragEnter={handleDrag}
              onDragLeave={handleDrag}
              onDragOver={handleDrag}
              onDrop={handleDrop}
              onClick={triggerFileInput}
            >
              <FileText className={`h-16 w-16 mx-auto mb-4 ${file ? "text-green-500" : "text-gray-400"}`} />
              {file ? (
                <div>
                  <p className="text-lg font-medium text-green-600 mb-2">{file.name}</p>
                  <p className="text-sm text-gray-500 mb-4">Size: {(file.size / 1024 / 1024).toFixed(2)} MB</p>
                  <button
                    type="button"
                    onClick={(e) => {
                      e.stopPropagation()
                      triggerFileInput()
                    }}
                    className="bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors font-medium"
                  >
                    Change File
                  </button>
                </div>
              ) : (
                <div>
                  <p className="text-lg font-medium text-gray-700 mb-2">Click to upload PDF</p>
                  <p className="text-sm text-gray-500 mb-4">Or drag and drop your file here</p>
                  <button
                    type="button"
                    onClick={(e) => {
                      e.stopPropagation()
                      triggerFileInput()
                    }}
                    className="bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors inline-flex items-center font-medium"
                  >
                    <Upload className="h-4 w-4 mr-2" />
                    Choose File
                  </button>
                </div>
              )}
            </div>
          </div>

          {/* Form Fields */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="md:col-span-2">
              <label className="block text-sm font-medium text-gray-700 mb-1">Title *</label>
              <input
                type="text"
                value={formData.title}
                onChange={(e) => setFormData((prev) => ({ ...prev, title: e.target.value }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                placeholder="Enter material title"
                required
              />
            </div>

            <div className="md:col-span-2">
              <label className="block text-sm font-medium text-gray-700 mb-1">Description</label>
              <textarea
                value={formData.description}
                onChange={(e) => setFormData((prev) => ({ ...prev, description: e.target.value }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                placeholder="Enter material description"
                rows={3}
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Subject *</label>
              <select
                value={formData.subject}
                onChange={(e) => setFormData((prev) => ({ ...prev, subject: e.target.value }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                required
              >
                <option value="">Select Subject</option>
                <option value="Current Affairs">Current Affairs</option>
                <option value="Polity">Polity</option>
                <option value="Economics">Economics</option>
                <option value="Geography">Geography</option>
                <option value="History">History</option>
                <option value="Science & Technology">Science & Technology</option>
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Author *</label>
              <input
                type="text"
                value={formData.author}
                onChange={(e) => setFormData((prev) => ({ ...prev, author: e.target.value }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                placeholder="Dr. Rajesh Kumar"
                required
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Price (₹) *</label>
              <input
                type="number"
                value={formData.price}
                onChange={(e) => setFormData((prev) => ({ ...prev, price: e.target.value }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                placeholder="299"
                required
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Original Price (₹)</label>
              <input
                type="number"
                value={formData.originalPrice}
                onChange={(e) => setFormData((prev) => ({ ...prev, originalPrice: e.target.value }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                placeholder="399"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Number of Pages</label>
              <input
                type="number"
                value={formData.pages}
                onChange={(e) => setFormData((prev) => ({ ...prev, pages: e.target.value }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                placeholder="150"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Status</label>
              <select
                value={formData.status}
                onChange={(e) => setFormData((prev) => ({ ...prev, status: e.target.value }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
              >
                <option value="published">Published</option>
                <option value="draft">Draft</option>
              </select>
            </div>

            <div className="md:col-span-2">
              <label className="block text-sm font-medium text-gray-700 mb-1">Tags (comma separated)</label>
              <input
                type="text"
                value={formData.tags}
                onChange={(e) => setFormData((prev) => ({ ...prev, tags: e.target.value }))}
                placeholder="e.g. UPSC, Prelims, Important"
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                required
              />
            </div>

            <div className="md:col-span-2">
              <label className="block text-sm font-medium text-gray-700 mb-1">Thumbnail URL</label>
              <input
                type="url"
                value={formData.thumbnailUrl}
                onChange={(e) => setFormData((prev) => ({ ...prev, thumbnailUrl: e.target.value }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                placeholder="https://images.pexels.com/..."
              />
            </div>

            <div className="md:col-span-2">
              <div className="flex items-center">
                <input
                  type="checkbox"
                  id="isPremium"
                  checked={formData.isPremium}
                  onChange={(e) => setFormData((prev) => ({ ...prev, isPremium: e.target.checked }))}
                  className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                />
                <label htmlFor="isPremium" className="ml-2 text-sm text-gray-700">
                  Premium Content (requires subscription)
                </label>
              </div>
            </div>
          </div>

          {/* Upload Status */}
          {uploadStatus !== "idle" && (
            <div className="bg-gray-50 border border-gray-200 rounded-lg p-4 mb-4">
              <div className="flex items-center space-x-3 mb-3">
                {getUploadStatusIcon()}
                <div className="flex-1">
                  <p className="text-sm font-medium text-gray-900">{getUploadStatusText()}</p>
                </div>
              </div>
              {uploadStatus === "error" && (
                <div className="mt-2 p-2 bg-red-50 border border-red-200 rounded text-sm text-red-700">
                  <div className="flex items-center space-x-2">
                    <AlertTriangle className="h-4 w-4" />
                    <span>{uploadError}</span>
                  </div>
                </div>
              )}
            </div>
          )}

          {/* Submit buttons */}
          <div className="flex justify-end space-x-3 pt-4">
            <button
              type="button"
              onClick={onClose}
              disabled={uploadStatus === "uploading" || uploadStatus === "processing"}
              className="px-4 py-2 text-gray-700 bg-gray-200 rounded-lg hover:bg-gray-300 disabled:opacity-50"
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={uploadStatus === "uploading" || uploadStatus === "processing" || !file}
              className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 flex items-center space-x-2"
            >
              {uploadStatus === "uploading" || uploadStatus === "processing" ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                  <span>{uploadStatus === "uploading" ? "Uploading..." : "Processing..."}</span>
                </>
              ) : (
                <>
                  <Upload className="h-4 w-4" />
                  <span>Upload Material</span>
                </>
              )}
            </button>
          </div>
        </form>
      </div>
    </div>
  )
}
