import { initializeApp } from 'firebase/app';
import { getAuth } from 'firebase/auth';

const firebaseConfig = {
  apiKey: import.meta.env.VITE_FIREBASE_API_KEY || "AIzaSyDgHZQm2OoU8BpD1r9Zj8Nijz3my3dRv-E",
  authDomain: import.meta.env.VITE_FIREBASE_AUTH_DOMAIN || "brainstorm-upsc.firebaseapp.com",
  projectId: import.meta.env.VITE_FIREBASE_PROJECT_ID || "brainstorm-upsc",
  storageBucket: import.meta.env.VITE_FIREBASE_STORAGE_BUCKET || "brainstorm-upsc.firebasestorage.app",
  messagingSenderId: import.meta.env.VITE_FIREBASE_MESSAGING_SENDER_ID || "677811681568",
  appId: import.meta.env.VITE_FIREBASE_APP_ID || "1:677811681568:web:363994c7a855f88636ff45",
  measurementId: import.meta.env.VITE_FIREBASE_MEASUREMENT_ID || "G-MFC0XV9JF0"
};

const app = initializeApp(firebaseConfig);
export const auth = getAuth(app);
export default app;
