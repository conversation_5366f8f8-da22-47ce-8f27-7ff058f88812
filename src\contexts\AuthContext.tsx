import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import {
  signInWithEmailAndPassword,
  createUserWithEmailAndPassword,
  signInWithPopup,
  GoogleAuthProvider,
  signInWithPhoneNumber,
  RecaptchaVerifier,
  ConfirmationResult,
  signOut,
  onAuthStateChanged,
  User as FirebaseUser,
  getAdditionalUserInfo // **** IMPORT THIS ****
} from 'firebase/auth';
import { auth } from '../config/firebase';
import { API_ENDPOINTS, apiUtils } from '../config/api';

interface User {
  id: string;
  firebaseUid?: string;
  name: string;
  email: string;
  role: 'student' | 'admin';
  avatar?: string;
  subscription?: 'free' | 'premium';
  joinedAt: string;
  phoneNumber?: string;
  profileCompleted?: boolean;
}

interface AuthContextType {
  user: User | null;
  login: (email: string, password: string) => Promise<boolean>;
  register: (name: string, email: string, password: string) => Promise<boolean>;
  loginWithGoogle: () => Promise<boolean>;
  sendOTP: (phoneNumber: string) => Promise<ConfirmationResult | null>;
  verifyOTP: (confirmationResult: ConfirmationResult, code: string) => Promise<boolean>;
  logout: () => Promise<void>;
  loading: boolean;
  refreshProfileStatus: () => Promise<void>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

const googleProvider = new GoogleAuthProvider();

export function AuthProvider({ children }: { children: ReactNode }) {
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);

  const checkProfileCompletion = async (firebaseUid: string): Promise<boolean> => {
  try {
    console.log('Checking profile for Firebase UID:', firebaseUid);
    const response = await apiUtils.get(API_ENDPOINTS.CURRENT_USER_PROFILE); // Use /userProfiles/me
    if (!response.ok) {
      console.error('Failed to fetch user profile:', response.status, response.statusText);
      return false;
    }
    const profile = await response.json();
    console.log('Profile check result:', profile);
    return !!profile && !!profile.userId;
  } catch (error) {
    console.error('Error checking profile completion:', error);
    return false;
  }
};

  const refreshProfileStatus = async () => {
    if (user) {
      console.log('Refreshing profile status for user:', user.id);

      try {
        // Fetch current user data using Firebase token
        const userResponse = await apiUtils.get(API_ENDPOINTS.CURRENT_USER);

        let updatedUserData = { ...user };

        if (userResponse.ok) {
          const userData = await userResponse.json();
          console.log('Refreshed user data from /users/me:', userData);
          updatedUserData = {
            ...updatedUserData,
            role: userData.role || 'student',
            isProfileComplete: userData.isProfileComplete || false,
            ...userData
          };
        }

        // Then, refresh profile data using Firebase token
        const profileResponse = await apiUtils.get(API_ENDPOINTS.CURRENT_USER_PROFILE);

        if (profileResponse.ok) {
          const profileData = await profileResponse.json();
          console.log('Refreshed profile data from /userProfiles/me:', profileData);
          updatedUserData = {
            ...updatedUserData,
            name: profileData.name || updatedUserData.name,
            avatar: profileData.avatar || updatedUserData.avatar,
            phoneNumber: profileData.phone || updatedUserData.phoneNumber,
            profileCompleted: true,
            role: updatedUserData.role // Keep role from users collection
          };
        }

        // Update user with refreshed data
        setUser(updatedUserData);
        console.log('Profile refreshed with role:', updatedUserData.role);

      } catch (error) {
        console.error('Error refreshing profile:', error);
      }
    }
  };

  useEffect(() => {
    const unsubscribe = onAuthStateChanged(auth, async (firebaseUser) => {
      if (firebaseUser) {
        console.log('Firebase user found:', firebaseUser.uid);

        // Create basic user data first (fallback)
        // For Google users, consider profile completed if they have name and email
        const hasBasicInfo = !!(firebaseUser.displayName && firebaseUser.email);

        const basicUserData: User = {
          id: firebaseUser.uid,
          name: firebaseUser.displayName || '',
          email: firebaseUser.email || '',
          role: 'student',
          avatar: firebaseUser.photoURL || undefined,
          subscription: 'free',
          joinedAt: firebaseUser.metadata.creationTime || new Date().toISOString(),
          phoneNumber: firebaseUser.phoneNumber || '',
          profileCompleted: hasBasicInfo // Google users with name/email are considered complete
        };

        // Try to fetch user data from users collection (for role) and user profile (for additional info)
        try {
          console.log('Fetching user data from users collection for role...');

          // Fetch current user data using Firebase token from /users/me
          const userResponse = await apiUtils.get(API_ENDPOINTS.CURRENT_USER);

          let userData = basicUserData;
          let userFromDB = null;

          if (userResponse.ok) {
            userFromDB = await userResponse.json();
            console.log('User data from /users/me:', userFromDB);

            if (userFromDB) {
              console.log('Found user in database with role:', userFromDB.role);
              userData = {
                id: firebaseUser.uid,
                name: firebaseUser.displayName || userFromDB.name || 'User',
                email: firebaseUser.email || userFromDB.email || '',
                role: userFromDB.role || 'student', // ✅ Get role from users collection
                avatar: firebaseUser.photoURL || userFromDB.avatar || undefined,
                subscription: 'free', // Default subscription
                joinedAt: firebaseUser.metadata.creationTime || new Date().toISOString(),
                phoneNumber: firebaseUser.phoneNumber || userFromDB.phoneNumber || '',
                profileCompleted: hasBasicInfo,
                isProfileComplete: userFromDB.isProfileComplete || false,
                ...userFromDB // Include any additional fields from backend
              };
            } else {
              console.log('User not found in database, will create new user');
            }
          }

          // Now try to fetch additional profile information from /userProfiles/me
          console.log('Fetching additional profile info from /userProfiles/me...');
          const profileResponse = await apiUtils.get(API_ENDPOINTS.CURRENT_USER_PROFILE);

          if (profileResponse.ok) {
            const userProfile = await profileResponse.json();
            console.log('Profile data from /userProfiles/me:', userProfile);

            if (userProfile) {
              console.log('Found user profile, merging data');
              // Merge profile data with user data (keeping role from users collection)
              userData = {
                ...userData,
                name: userProfile.name || userData.name,
                avatar: userProfile.avatar || userData.avatar,
                phoneNumber: userProfile.phone || userData.phoneNumber,
                profileCompleted: true,
                ...userProfile // Include any additional profile fields
              };
            }
          }

          // If user doesn't exist in database, create them
          if (!userFromDB && hasBasicInfo) {
            console.log('Creating new user in database...');
            try {
              const createUserResponse = await apiUtils.post(API_ENDPOINTS.USERS, {
                firebaseUid: firebaseUser.uid, // Link to Firebase user
                email: firebaseUser.email || '',
                role: 'student', // Default role for new users
                active: true,
                createdAt: new Date().toISOString(),
                lastLoginAt: new Date().toISOString()
              });

              if (createUserResponse.ok) {
                console.log('New user created successfully in database');
                // Also create a basic profile
                try {
                  await apiUtils.post(API_ENDPOINTS.USER_PROFILES, {
                    userId: firebaseUser.uid,
                    name: firebaseUser.displayName || '',
                    email: firebaseUser.email || '',
                    phone: firebaseUser.phoneNumber || '',
                    avatar: firebaseUser.photoURL || null,
                    createdAt: new Date().toISOString().split('T')[0],
                    updatedAt: new Date().toISOString().split('T')[0]
                  });
                  console.log('Basic profile created for new user');
                } catch (profileError) {
                  console.error('Error creating profile for new user:', profileError);
                }
              } else {
                console.log('Failed to create new user in database:', createUserResponse.status);
              }
            } catch (createError) {
              console.error('Error creating new user in database:', createError);
            }
          }

          console.log('Setting final user data:', userData);
          setUser(userData);

        } catch (error) {
          console.error('Error fetching user data:', error);
          // Always set basic user data on error to prevent auth blocking
          setUser(basicUserData);
        }
      } else {
        console.log('No Firebase user found');
        setUser(null);
      }
      setLoading(false);
    });

    return unsubscribe;
  }, []);

  const mapFirebaseUserToUser = (firebaseUser: FirebaseUser): User => {
    return {
      id: firebaseUser.uid,
      name: firebaseUser.displayName || 'User',
      email: firebaseUser.email || '',
      role: 'student', // Default role, you can implement role management
      avatar: firebaseUser.photoURL || undefined,
      subscription: 'free',
      joinedAt: firebaseUser.metadata.creationTime || new Date().toISOString(),
      phoneNumber: firebaseUser.phoneNumber || undefined
    };
  };

  const login = async (email: string, password: string): Promise<boolean> => {
    try {
      await signInWithEmailAndPassword(auth, email, password);
      return true;
    } catch (error) {
      console.error('Login error:', error);
      return false;
    }
  };

  const register = async (name: string, email: string, password: string): Promise<boolean> => {
    try {
      const result = await createUserWithEmailAndPassword(auth, email, password);
      // New user - profile completion will be handled by the auth state listener
      return true;
    } catch (error) {
      console.error('Registration error:', error);
      return false;
    }
  };

  // **** MODIFIED THIS FUNCTION ONLY ****
  const loginWithGoogle = async (): Promise<boolean> => {
    try {
      const result = await signInWithPopup(auth, googleProvider);
      const additionalInfo = getAdditionalUserInfo(result);

      // Check if the user is new.
      if (additionalInfo?.isNewUser) {
        // If they are new, they haven't registered. We must not allow them to log in.
        // 1. Get the newly created user object.
        const userToDelete = result.user;
        
        // 2. Delete the user from Firebase Authentication to prevent them from being saved.
        console.log('New user detected. Deleting user from Firebase Auth.');
        await userToDelete.delete();

        // 3. Sign out to clear any lingering auth state.
        await signOut(auth);
        
        // 4. Return false to indicate the login failed. Your UI can show "User not found".
        console.log('Login failed: User is not registered.');
        // Set an error in localStorage so the login page can display it.
        localStorage.setItem('authError', 'Account not found. Please register first.');
        return false;
      }

      // If we reach here, the user already existed in Firebase. The login is successful.
      console.log('Existing user logged in with Google successfully.');
      return true;

    } catch (error: any) {
      console.error('Google login error:', error);
      if (error?.code === 'auth/popup-closed-by-user') {
        localStorage.setItem('authError', 'Popup closed before sign-in completed.');
      } else {
        localStorage.setItem('authError', 'An error occurred during Google sign-in.');
      }
      return false;
    }
  };

  const sendOTP = async (phoneNumber: string): Promise<ConfirmationResult | null> => {
    try {
      // Format phone number with +91 if not already present
      const formattedPhone = phoneNumber.startsWith('+91')
        ? phoneNumber
        : `+91${phoneNumber.replace(/\D/g, '')}`;

      console.log('Sending OTP to:', formattedPhone);

      // Clear any existing reCAPTCHA
      const existingRecaptcha = document.getElementById('recaptcha-container');
      if (existingRecaptcha) {
        existingRecaptcha.innerHTML = '';
      }

      const recaptchaVerifier = new RecaptchaVerifier(auth, 'recaptcha-container', {
        size: 'invisible',
        callback: () => {
          console.log('reCAPTCHA solved');
        },
        'expired-callback': () => {
          console.log('reCAPTCHA expired');
        }
      });

      const confirmationResult = await signInWithPhoneNumber(auth, formattedPhone, recaptchaVerifier);
      console.log('OTP sent successfully');
      return confirmationResult;
    } catch (error: any) {
      console.error('OTP send error:', error);

      // Clear reCAPTCHA on error
      const existingRecaptcha = document.getElementById('recaptcha-container');
      if (existingRecaptcha) {
        existingRecaptcha.innerHTML = '';
      }

      return null;
    }
  };

  const verifyOTP = async (confirmationResult: ConfirmationResult, code: string): Promise<boolean> => {
    try {
      const result = await confirmationResult.confirm(code);
      // New phone user - profile completion will be handled by the auth state listener
      return true;
    } catch (error) {
      console.error('OTP verification error:', error);
      return false;
    }
  };

  const logout = async (): Promise<void> => {
    try {
      await signOut(auth);
    } catch (error) {
      console.error('Logout error:', error);
    }
  };

  return (
    <AuthContext.Provider value={{
      user,
      login,
      register,
      loginWithGoogle,
      sendOTP,
      verifyOTP,
      logout,
      loading,
      refreshProfileStatus
    }}>
      {children}
    </AuthContext.Provider>
  );
}

export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}









