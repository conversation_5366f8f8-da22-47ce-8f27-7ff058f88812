import React, { createContext, useContext, useState, useEffect } from 'react';
import { useAuth } from './AuthContext';

interface CartItem {
  id: string;
  materialId: string;
  title: string;
  price: number;
  author: string;
  subject: string;
  type: string;
  pages: number;
  rating: number;
  addedAt: string;
}

interface CartContextType {
  cartItems: CartItem[];
  cartCount: number;
  cartTotal: number;
  addToCart: (material: any) => void;
  removeFromCart: (materialId: string) => void;
  clearCart: () => void;
  isInCart: (materialId: string) => boolean;
  updateCartItem: (materialId: string, updates: Partial<CartItem>) => void;
}

const CartContext = createContext<CartContextType | undefined>(undefined);

export const useCart = () => {
  const context = useContext(CartContext);
  if (!context) {
    throw new Error('useCart must be used within a CartProvider');
  }
  return context;
};

export const CartProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const { user } = useAuth();
  const [cartItems, setCartItems] = useState<CartItem[]>([]);

  // Load cart from localStorage on mount
  useEffect(() => {
    if (user) {
      const savedCart = localStorage.getItem(`cart_${user.id}`);
      if (savedCart) {
        setCartItems(JSON.parse(savedCart));
      }
    } else {
      setCartItems([]);
    }
  }, [user]);

  // Save cart to localStorage whenever it changes
  useEffect(() => {
    if (user) {
      localStorage.setItem(`cart_${user.id}`, JSON.stringify(cartItems));
    }
  }, [cartItems, user]);

  const addToCart = (material: any) => {
    if (!user) {
      alert('Please login to add items to cart');
      return;
    }

    // Check if item already in cart
    if (isInCart(material.id)) {
      alert('Item is already in your cart');
      return;
    }

    const cartItem: CartItem = {
      id: `cart_${Date.now()}`,
      materialId: material.id.toString(),
      title: material.title,
      price: material.price,
      author: material.author,
      subject: material.subject,
      type: material.type,
      pages: material.pages,
      rating: material.rating,
      addedAt: new Date().toISOString()
    };

    setCartItems(prev => [...prev, cartItem]);
  };

  const removeFromCart = (materialId: string) => {
    setCartItems(prev => prev.filter(item => item.materialId !== materialId));
  };

  const clearCart = () => {
    setCartItems([]);
  };

  const isInCart = (materialId: string): boolean => {
    return cartItems.some(item => item.materialId === materialId.toString());
  };

  const updateCartItem = (materialId: string, updates: Partial<CartItem>) => {
    setCartItems(prev => prev.map(item => 
      item.materialId === materialId ? { ...item, ...updates } : item
    ));
  };

  const cartCount = cartItems.length;
  const cartTotal = cartItems.reduce((total, item) => total + item.price, 0);

  return (
    <CartContext.Provider value={{
      cartItems,
      cartCount,
      cartTotal,
      addToCart,
      removeFromCart,
      clearCart,
      isInCart,
      updateCartItem
    }}>
      {children}
    </CartContext.Provider>
  );
};