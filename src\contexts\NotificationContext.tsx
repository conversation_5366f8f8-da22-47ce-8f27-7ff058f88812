import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { API_ENDPOINTS, apiUtils } from '../config/api';

interface Notification {
  id: string;
  title: string;
  message: string;
  type: 'info' | 'success' | 'warning' | 'error';
  timestamp: string;
  read: boolean;
}

interface NotificationContextType {
  notifications: Notification[];
  addNotification: (notification: Omit<Notification, 'id' | 'timestamp' | 'read'>) => void;
  markAsRead: (id: string) => void;
  clearNotifications: () => void;
  unreadCount: number;
}

const NotificationContext = createContext<NotificationContextType | undefined>(undefined);

export function NotificationProvider({ children }: { children: ReactNode }) {
  const [notifications, setNotifications] = useState<Notification[]>([]);

  useEffect(() => {
    fetchNotifications();
  }, []);

  const fetchNotifications = async () => {
    try {
      const response = await apiUtils.get(API_ENDPOINTS.NOTIFICATIONS);
      if (response.ok) {
        const contentType = response.headers.get('content-type');
        if (contentType && contentType.includes('application/json')) {
          const text = await response.text();
          if (text.trim()) {
            const data = JSON.parse(text);
            // Transform API data to match our interface
            const transformedNotifications = (Array.isArray(data) ? data : []).map((notification: any) => ({
              id: notification.id?.toString() || '',
              title: notification.title || '',
              message: notification.message || '',
              type: notification.type || 'info',
              timestamp: notification.timestamp || new Date().toISOString(),
              read: notification.read || false
            }));
            setNotifications(transformedNotifications);
          } else {
            console.log('Empty response for notifications');
            setNotifications([]);
          }
        } else {
          console.log('Non-JSON response for notifications');
          setNotifications([]);
        }
      } else {
        console.error('Failed to fetch notifications:', response.status);
        setNotifications([]);
      }
    } catch (err) {
      console.error('Error fetching notifications:', err);
      // Keep empty array on error instead of hardcoded data
      setNotifications([]);
    }
  };

  const addNotification = (notification: Omit<Notification, 'id' | 'timestamp' | 'read'>) => {
    const newNotification: Notification = {
      ...notification,
      id: Date.now().toString(),
      timestamp: new Date().toISOString(),
      read: false
    };
    setNotifications(prev => [newNotification, ...prev]);

    // Optionally persist to API
    persistNotification(newNotification);
  };

  const persistNotification = async (notification: Notification) => {
    try {
      const response = await fetch(API_ENDPOINTS.NOTIFICATIONS, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        },
        body: JSON.stringify({
          title: notification.title,
          message: notification.message,
          type: notification.type,
          audience: 'all',
          status: 'sent',
          scheduledAt: null,
          createdAt: new Date().toISOString().split('T')[0], // yyyy-MM-dd format
          sentAt: new Date().toISOString().split('T')[0],
          recipients: 1,
          openRate: 0
        })
      });

      if (!response.ok) {
        console.error('Failed to persist notification:', response.status);
      }
    } catch (err) {
      console.error('Error persisting notification:', err);
    }
  };

  const markAsRead = async (id: string) => {
    // Update local state immediately for better UX
    setNotifications(prev =>
      prev.map(n => n.id === id ? { ...n, read: true } : n)
    );

    // Persist to backend
    try {
      const response = await fetch(`${API_ENDPOINTS.NOTIFICATIONS}/${id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        },
        body: JSON.stringify({ read: true })
      });

      if (!response.ok) {
        console.error('Failed to mark notification as read:', response.status);
        // Revert local state if backend update failed
        setNotifications(prev =>
          prev.map(n => n.id === id ? { ...n, read: false } : n)
        );
      }
    } catch (err) {
      console.error('Error marking notification as read:', err);
      // Revert local state if backend update failed
      setNotifications(prev =>
        prev.map(n => n.id === id ? { ...n, read: false } : n)
      );
    }
  };

  const clearNotifications = () => {
    setNotifications([]);
  };

  const unreadCount = notifications.filter(n => !n.read).length;

  return (
    <NotificationContext.Provider value={{
      notifications,
      addNotification,
      markAsRead,
      clearNotifications,
      unreadCount
    }}>
      {children}
    </NotificationContext.Provider>
  );
}

export function useNotifications() {
  const context = useContext(NotificationContext);
  if (context === undefined) {
    throw new Error('useNotifications must be used within a NotificationProvider');
  }
  return context;
}
