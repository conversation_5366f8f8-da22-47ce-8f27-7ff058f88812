import React from 'react';
import { <PERSON> } from 'react-router-dom';
import { ArrowLeft, Users, Target, Award, BookOpen, Phone, Mail, MessageCircle, Send } from 'lucide-react';

function AboutUs() {
  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b border-gray-200">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <Link
              to="/"
              className="flex items-center space-x-2 text-gray-600 hover:text-gray-900"
            >
              <ArrowLeft className="h-5 w-5" />
              <span>Back to Home</span>
            </Link>
            <div className="flex items-center space-x-2">
              <Users className="h-6 w-6 text-blue-600" />
              <h1 className="text-xl font-bold text-gray-900">About Us</h1>
            </div>
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="bg-white rounded-lg shadow-sm p-8">
          {/* Header */}
          <div className="text-center mb-12">
            <div className="w-20 h-20 bg-gradient-to-r from-blue-600 to-indigo-600 rounded-xl flex items-center justify-center mx-auto mb-6">
              <span className="text-white font-bold text-3xl">B</span>
            </div>
            <h1 className="text-3xl font-bold text-gray-900 mb-4">About Brainstorm UPSC</h1>
            <p className="text-xl text-gray-600 max-w-2xl mx-auto">
              Empowering UPSC aspirants with comprehensive preparation resources and expert guidance
            </p>
          </div>

          {/* Mission & Vision */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-12">
            <div className="bg-blue-50 rounded-lg p-6">
              <div className="flex items-center space-x-2 mb-4">
                <Target className="h-6 w-6 text-blue-600" />
                <h2 className="text-xl font-semibold text-gray-900">Our Mission</h2>
              </div>
              <p className="text-gray-700">
                To democratize UPSC preparation by providing high-quality, accessible, and affordable
                educational resources that help aspirants achieve their civil services dreams.
              </p>
            </div>

            <div className="bg-indigo-50 rounded-lg p-6">
              <div className="flex items-center space-x-2 mb-4">
                <Award className="h-6 w-6 text-indigo-600" />
                <h2 className="text-xl font-semibold text-gray-900">Our Vision</h2>
              </div>
              <p className="text-gray-700">
                To become India's most trusted and comprehensive UPSC preparation platform,
                fostering a community of successful civil servants who serve the nation with integrity.
              </p>
            </div>
          </div>

          {/* What We Offer */}
          <section className="mb-12">
            <div className="flex items-center space-x-2 mb-6">
              <BookOpen className="h-6 w-6 text-blue-600" />
              <h2 className="text-2xl font-semibold text-gray-900">What We Offer</h2>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-4">
                <div className="flex items-start space-x-3">
                  <div className="w-2 h-2 bg-blue-600 rounded-full mt-2"></div>
                  <div>
                    <h3 className="font-semibold text-gray-900">Comprehensive Study Materials</h3>
                    <p className="text-gray-600">Curated notes, PDFs, and resources covering the entire UPSC syllabus</p>
                  </div>
                </div>
                <div className="flex items-start space-x-3">
                  <div className="w-2 h-2 bg-blue-600 rounded-full mt-2"></div>
                  <div>
                    <h3 className="font-semibold text-gray-900">Daily Quiz Challenges</h3>
                    <p className="text-gray-600">Regular practice questions to test and improve your knowledge</p>
                  </div>
                </div>
                <div className="flex items-start space-x-3">
                  <div className="w-2 h-2 bg-blue-600 rounded-full mt-2"></div>
                  <div>
                    <h3 className="font-semibold text-gray-900">Mock Tests & Assessments</h3>
                    <p className="text-gray-600">Simulate real exam conditions with our comprehensive test series</p>
                  </div>
                </div>
              </div>
              <div className="space-y-4">
                <div className="flex items-start space-x-3">
                  <div className="w-2 h-2 bg-blue-600 rounded-full mt-2"></div>
                  <div>
                    <h3 className="font-semibold text-gray-900">Expert Guidance</h3>
                    <p className="text-gray-600">Learn from experienced educators and successful candidates</p>
                  </div>
                </div>
                <div className="flex items-start space-x-3">
                  <div className="w-2 h-2 bg-blue-600 rounded-full mt-2"></div>
                  <div>
                    <h3 className="font-semibold text-gray-900">Current Affairs Updates</h3>
                    <p className="text-gray-600">Stay updated with the latest developments relevant to UPSC</p>
                  </div>
                </div>
                <div className="flex items-start space-x-3">
                  <div className="w-2 h-2 bg-blue-600 rounded-full mt-2"></div>
                  <div>
                    <h3 className="font-semibold text-gray-900">Community Support</h3>
                    <p className="text-gray-600">Connect with fellow aspirants and share your preparation journey</p>
                  </div>
                </div>
              </div>
            </div>
          </section>

          {/* Why Choose Us */}
          <section className="mb-12">
            <h2 className="text-2xl font-semibold text-gray-900 mb-6">Why Choose Brainstorm UPSC?</h2>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="text-center p-6 bg-gray-50 rounded-lg">
                <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                  <Award className="h-6 w-6 text-blue-600" />
                </div>
                <h3 className="font-semibold text-gray-900 mb-2">Quality Content</h3>
                <p className="text-gray-600 text-sm">Meticulously crafted study materials by subject matter experts</p>
              </div>
              <div className="text-center p-6 bg-gray-50 rounded-lg">
                <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                  <Users className="h-6 w-6 text-green-600" />
                </div>
                <h3 className="font-semibold text-gray-900 mb-2">Community Driven</h3>
                <p className="text-gray-600 text-sm">Join thousands of aspirants in our supportive learning community</p>
              </div>
              <div className="text-center p-6 bg-gray-50 rounded-lg">
                <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                  <Target className="h-6 w-6 text-purple-600" />
                </div>
                <h3 className="font-semibold text-gray-900 mb-2">Result Oriented</h3>
                <p className="text-gray-600 text-sm">Focused approach designed to maximize your success rate</p>
              </div>
            </div>
          </section>

          {/* Connect With Us */}
          <section className="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg p-8">
            <div className="text-center mb-8">
              <h2 className="text-2xl font-semibold text-gray-900 mb-4">Connect With Us</h2>
              <p className="text-gray-600">
                Join our community and stay updated with the latest UPSC preparation resources
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">


              {/* Telegram Channel */}
              <a
                href="https://t.me/letscrackupsc"
                target="_blank"
                rel="noopener noreferrer"
                className="bg-white p-6 rounded-lg shadow-sm hover:shadow-md transition-shadow text-center group"
              >
                <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mx-auto mb-4 group-hover:bg-blue-200 transition-colors">
                  <Send className="h-6 w-6 text-blue-600" />
                </div>
                <h3 className="font-semibold text-gray-900 mb-2">Telegram Channel</h3>
                <p className="text-gray-600 text-sm">Join our active community discussions</p>
              </a>

              {/* WhatsApp Channel */}
              <a
                href="https://whatsapp.com/channel/0029Vb6fu778PgsCE0AbJY1R"
                target="_blank"
                rel="noopener noreferrer"
                className="bg-white p-6 rounded-lg shadow-sm hover:shadow-md transition-shadow text-center group"
              >
                <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mx-auto mb-4 group-hover:bg-green-200 transition-colors">
                  <MessageCircle className="h-6 w-6 text-green-600" />
                </div>
                <h3 className="font-semibold text-gray-900 mb-2">WhatsApp Channel</h3>
                <p className="text-gray-600 text-sm">Get daily updates and study materials</p>
              </a>

              {/* Phone Contact */}
              <a
                href="tel:8855965237"
                className="bg-white p-6 rounded-lg shadow-sm hover:shadow-md transition-shadow text-center group"
              >
                <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mx-auto mb-4 group-hover:bg-purple-200 transition-colors">
                  <Phone className="h-6 w-6 text-purple-600" />
                </div>
                <h3 className="font-semibold text-gray-900 mb-2">Call Us</h3>
                <p className="text-gray-600 text-sm">8855965237</p>
              </a>

              {/* Email Contact */}
              <a
                href="mailto:<EMAIL>"
                className="bg-white p-6 rounded-lg shadow-sm hover:shadow-md transition-shadow text-center group"
              >
                <div className="w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center mx-auto mb-4 group-hover:bg-red-200 transition-colors">
                  <Mail className="h-6 w-6 text-red-600" />
                </div>
                <h3 className="font-semibold text-gray-900 mb-2">Email Us</h3>
                <p className="text-gray-600 text-sm">Get personalized guidance</p>
              </a>
            </div>
          </section>

          {/* Call to Action */}
          <div className="text-center mt-12 pt-8 border-t border-gray-200">
            <h3 className="text-xl font-semibold text-gray-900 mb-4">Ready to Start Your UPSC Journey?</h3>
            <p className="text-gray-600 mb-6">
              Join thousands of successful candidates who chose Brainstorm for their preparation
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link
                to="/register"
                className="bg-blue-600 text-white px-8 py-3 rounded-lg font-semibold hover:bg-blue-700 transition-colors"
              >
                Start Free Trial
              </Link>
              <Link
                to="/daily-quiz"
                className="border-2 border-blue-600 text-blue-600 px-8 py-3 rounded-lg font-semibold hover:bg-blue-600 hover:text-white transition-colors"
              >
                Take Daily Quiz
              </Link>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

export default AboutUs;