"use client";

import { useState, useEffect } from "react";
import { Trash2, ShoppingCart, CheckCircle, AlertCircle } from "lucide-react";
import { useAuth } from "../contexts/AuthContext";
import { useCart } from "../contexts/CartContext";
import { PaymentService } from "../services/paymentService";
import { PAYMENT_CONFIG } from "../config/payment";
import { API_CONFIG, apiUtils } from "../config/api";
import { getAuth } from "firebase/auth";

// Initialize payment service
const paymentService = new PaymentService(PAYMENT_CONFIG);

interface Purchase {
  id: string;
  userId: string;
  materialId: string;
  amount: number;
  paymentId: string;
  orderId: string;
  status: string;
  purchaseDate: string;
  paymentMethod: string;
  gateway?: string;
  downloadCount?: number;
  lastDownloadAt?: string;
  bulkOrderId?: string;
}

function Cart() {
  const { user } = useAuth();
  const { cartItems, cartCount, cartTotal, removeFromCart, clearCart } = useCart();
  const [materials, setMaterials] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [paymentLoading, setPaymentLoading] = useState(false);
  const [paymentError, setPaymentError] = useState("");
  const [userPurchases, setUserPurchases] = useState<Purchase[]>([]);

  useEffect(() => {
    fetchMaterials();
    if (user) {
      fetchUserPurchases();
    }
  }, [user]);

  const fetchMaterials = async () => {
    try {
      setLoading(true);
      const response = await apiUtils.get(API_CONFIG.API_BASE_URL + "/studyMaterials");
      if (response.ok) {
        const data = await response.json();
        setMaterials(data || []);
      } else {
        setPaymentError("Failed to fetch materials");
      }
    } catch (err) {
      console.error("Error fetching materials:", err);
      setPaymentError("Network error. Please try again.");
    } finally {
      setLoading(false);
    }
  };

  const fetchUserPurchases = async () => {
    if (!user) return;

    try {
      const localPurchases = JSON.parse(localStorage.getItem("userPurchases") || "[]");
      const userSpecificPurchases = localPurchases.filter((p: Purchase) => p.userId.toString() === user.id.toString());
      setUserPurchases(userSpecificPurchases);

      try {
        const response = await apiUtils.get(`${API_CONFIG.API_BASE_URL}/purchases?userId=${user.id}`);
        if (response.ok) {
          const contentType = response.headers.get("content-type");
          if (contentType && contentType.includes("application/json")) {
            const text = await response.text();
            if (text.trim()) {
              const apiPurchases = JSON.parse(text);
              const userPurchasesFromAPI = Array.isArray(apiPurchases)
                ? apiPurchases.filter((p: any) => p.userId === user.id)
                : apiPurchases.userId === user.id
                ? [apiPurchases]
                : [];

              if (userPurchasesFromAPI.length > 0) {
                const mergedPurchases = [...userSpecificPurchases];
                userPurchasesFromAPI.forEach((apiPurchase: any) => {
                  if (!mergedPurchases.find((p) => p.id === apiPurchase.id)) {
                    mergedPurchases.push(apiPurchase);
                  }
                });
                setUserPurchases(mergedPurchases);
                localStorage.setItem("userPurchases", JSON.stringify(mergedPurchases));
              }
            }
          }
        }
      } catch (apiError) {
        console.log("API fetch failed, using localStorage only:", apiError);
      }
    } catch (error) {
      console.error("Error loading purchases:", error);
    }
  };

  const isPurchased = (materialId: number | string): boolean => {
    return userPurchases.some(
      (purchase) => purchase.materialId.toString() === materialId.toString() && purchase.status === "completed"
    );
  };

  const handleBulkPurchase = async () => {
    if (!user) {
      alert("Please login to proceed with the purchase");
      return;
    }

    if (cartCount === 0) {
      alert("Your cart is empty");
      return;
    }

    setPaymentLoading(true);
    setPaymentError("");

    try {
      if (typeof (window as any).Razorpay !== "undefined") {
        console.log("Using Razorpay for bulk payment via backend");
        await paymentService.initiateBulkRazorpayPayment(
          cartItems,
          user,
          cartTotal,
          handleBulkPaymentSuccess,
          handleBulkPaymentError
        );
      } else {
        console.log("Razorpay not available, using Cashfree as fallback");
        await paymentService.initiateCashfreePayment(
          { id: "bulk", price: cartTotal, title: "Bulk Purchase" },
          user,
          handleBulkPaymentSuccess,
          handleBulkPaymentError
        );
      }
    } catch (error) {
      handleBulkPaymentError(error);
    }
  };

  const handleBulkPaymentSuccess = async (response: any) => {
    console.log("Bulk payment success response:", response);

    const bulkOrderId = `bulk_${Date.now()}`;
    const purchases: Purchase[] = response.materialIds.map((materialId: string) => ({
      id: `purchase_${Date.now()}_${materialId}`,
      userId: response.userId.toString(),
      materialId: materialId.toString(),
      amount: response.amount / response.materialIds.length, // Approximate per-item amount
      paymentId: response.paymentId,
      orderId: response.orderId,
      status: "completed",
      purchaseDate: new Date().toISOString(),
      paymentMethod: response.paymentMethod || "razorpay",
      gateway: response.gateway || "razorpay",
      bulkOrderId,
    }));

    // Save to localStorage
    const existingPurchases = JSON.parse(localStorage.getItem("userPurchases") || "[]");
    const updatedPurchases = [...existingPurchases, ...purchases];
    localStorage.setItem("userPurchases", JSON.stringify(updatedPurchases));
    console.log("Updated localStorage purchases:", updatedPurchases);

    // Save to backend
    try {
      const auth = getAuth();
      const user = auth.currentUser;
      const token = (await user?.getIdToken()) || "";
      for (const purchase of purchases) {
        const purchaseResponse = await fetch(`${API_CONFIG.API_BASE_URL}/purchases`, {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${token}`,
          },
          body: JSON.stringify(purchase),
        });

        if (!purchaseResponse.ok) {
          console.error("Failed to save purchase to backend:", await purchaseResponse.text());
        } else {
          console.log("Purchase saved to backend successfully:", purchase.id);
        }
      }
    } catch (error) {
      console.error("Error saving purchases to backend:", error);
    }

    // Update local state
    setUserPurchases((prev) => [...prev, ...purchases]);
    clearCart(); // Clear cart after successful purchase
    alert("Bulk payment successful! You can now download your materials.");
    setPaymentLoading(false);
  };

  const handleBulkPaymentError = (error: any) => {
    console.error("Bulk payment failed:", error);
    setPaymentError("Bulk payment failed. Please try again.");
    setPaymentLoading(false);
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading cart...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Your Cart</h1>
          <p className="text-gray-600 mt-1">Review and purchase your study materials</p>
        </div>
        <div className="flex items-center space-x-2 mt-4 sm:mt-0">
          <ShoppingCart className="h-5 w-5 text-gray-600" />
          <span className="text-sm text-gray-600">{cartCount} items</span>
        </div>
      </div>

      {cartCount === 0 ? (
        <div className="text-center py-12">
          <ShoppingCart className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">Your cart is empty</h3>
          <p className="text-gray-600">Add some study materials to get started.</p>
        </div>
      ) : (
        <>
          <div className="bg-white p-6 rounded-xl shadow-sm border border-gray-200">
            <div className="space-y-4">
              {cartItems.map((item) => (
                <div key={item.id} className="flex items-center justify-between border-b pb-4">
                  <div className="flex items-center space-x-4">
                    <div className="bg-gray-100 p-2 rounded-lg">
                      <ShoppingCart className="h-6 w-6 text-gray-600" />
                    </div>
                    <div>
                      <h3 className="text-lg font-medium text-gray-900">{item.title || "Unknown Material"}</h3>
                      <p className="text-sm text-gray-600">{item.subject || "N/A"}</p>
                      <p className="text-sm font-medium text-gray-900">₹{item.price || 0}</p>
                    </div>
                  </div>
                  <button
                    onClick={() => removeFromCart(item.materialId)}
                    className="text-red-600 hover:text-red-800 transition-colors"
                  >
                    <Trash2 className="h-5 w-5" />
                  </button>
                </div>
              ))}
            </div>
            <div className="mt-6 flex justify-between items-center">
              <div className="text-lg font-medium text-gray-900">
                Total: ₹{cartTotal}
              </div>
              <button
                onClick={handleBulkPurchase}
                disabled={paymentLoading}
                className="bg-blue-600 text-white px-6 py-2 rounded-lg text-sm font-medium hover:bg-blue-700 transition-colors flex items-center justify-center space-x-2 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {paymentLoading ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                    <span>Processing...</span>
                  </>
                ) : (
                  <>
                    <ShoppingCart className="h-4 w-4" />
                    <span>Checkout</span>
                  </>
                )}
              </button>
            </div>
          </div>
        </>
      )}

      {paymentError && (
        <div className="fixed bottom-4 right-4 bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded-lg">
          <div className="flex items-center space-x-2">
            <AlertCircle className="h-4 w-4" />
            <span>{paymentError}</span>
          </div>
        </div>
      )}
    </div>
  );
}

export default Cart;