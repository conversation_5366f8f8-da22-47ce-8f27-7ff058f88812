"use client"

import { useState, useEffect } from "react"
import { <PERSON>, Clock, Trophy, User, ArrowLeft, CheckCircle, FileText, TrendingUp } from "lucide-react"
import { <PERSON> } from "react-router-dom"
import PublicQuiz from "../public/PublicQuiz"
import { API_ENDPOINTS, apiUtils } from "../config/api"
import { useAuth } from "../contexts/AuthContext"

interface Question {
  id: string
  question: string
  options: string[]
  correctAnswer: string
  explanation: string
  subject: string
}

interface QuizDay {
  date: string
  day: string
  month: string
  year: string
  status: "completed" | "available" | "locked"
  score?: number
  totalQuestions?: number
  completedAt?: string
}

interface DailyQuizProps {
  onLoginPrompt?: () => void
}

interface UserProgress {
  id?: string
  userId?: string
  totalQuizzesTaken: number
  averageScore: number
  currentStreak: number
  bestStreak: number
  weeklyProgress: { date: string; score: number }[]
}

interface UserSubmission {
  id: string
  userId: string
  dailyQuizId: string
  date: string
  score: number
  totalQuestions: number
  answers: { [key: string]: number }
  submittedAt: string
}

function DailyQuiz({ onLoginPrompt }: DailyQuizProps) {
  const { user } = useAuth()

  // State Management
  const [quizDays, setQuizDays] = useState<QuizDay[]>([])
  const [dailyQuestions, setDailyQuestions] = useState<Question[]>([])
  const [userProgress, setUserProgress] = useState<UserProgress | null>(null)
  const [userSubmissions, setUserSubmissions] = useState<UserSubmission[]>([])
  const [showQuiz, setShowQuiz] = useState(false)
  const [loading, setLoading] = useState(true)
  const [progressLoading, setProgressLoading] = useState(false)
  const [selectedDate, setSelectedDate] = useState<string>("")
  const [dailyQuizId, setDailyQuizId] = useState<string>("")

  // Initialize with today's date and fetch data
  useEffect(() => {
    const initializeData = async () => {
      setLoading(true)

      // Set today's date as selected by default
      const today = new Date().toISOString().split("T")[0]
      setSelectedDate(today)

      try {
        // Fetch today's quiz questions
        await fetchQuestionsForDate(today)

        if (user) {
          // Fetch user-specific data
          await Promise.all([fetchUserProgress(), fetchUserSubmissions()])
        } else {
          // Generate calendar for non-authenticated users
          generateQuizDays([])
        }
      } catch (error) {
        console.error("Error initializing data:", error)
        generateQuizDays([])
      } finally {
        setLoading(false)
      }
    }

    initializeData()
  }, [user])

  // Generate calendar when submissions change
  useEffect(() => {
    if (userSubmissions.length >= 0) {
      generateQuizDays(userSubmissions)
    }
  }, [userSubmissions])

  const fetchQuestionsForDate = async (date: string) => {
    try {
      const url = API_ENDPOINTS.GET_DAILY_QUIZ_QUESTIONS_BY_DATE(date)
      console.log("Fetching questions for date:", date, "from:", url)

      const response = await apiUtils.get(url)

      if (response.ok) {
        // Read JSON directly to avoid multiple body reads
        const data = await response.json()
        const questions = Array.isArray(data) ? data : []
        setDailyQuestions(questions)
        setDailyQuizId(date)
        console.log("Questions fetched successfully:", questions.length)
      } else {
        console.error("Failed to fetch questions:", response.status)
        setDailyQuestions([])
      }
    } catch (error) {
      console.error("Error fetching daily questions:", error)
      setDailyQuestions([])
    }
  }

  const fetchUserProgress = async (): Promise<UserProgress | null> => {
    if (!user) return null

    setProgressLoading(true)
    try {
      // Use endpoint without userId, rely on token; add cache-busting
      const url = `${API_ENDPOINTS.GET_USER_DAILY_QUIZ_PROGRESS}?t=${Date.now()}`
      console.log("Fetching user progress from:", url)

      const response = await apiUtils.get(url)

      if (response.ok) {
        // Read JSON directly to avoid multiple body reads
        const data = await response.json()

        let progressData: UserProgress | null = null

        if (Array.isArray(data) && data.length > 0) {
          progressData = data[0]
        } else if (data && typeof data === "object" && !Array.isArray(data)) {
          progressData = data
        }

        if (progressData) {
          setUserProgress(progressData)
          console.log("User progress set:", progressData)
          return progressData
        }
      }

      // If no data found, create default progress
      const defaultProgress: UserProgress = {
        id: Date.now().toString(),
        userId: user.id,
        totalQuizzesTaken: 0,
        averageScore: 0,
        currentStreak: 0,
        bestStreak: 0,
        weeklyProgress: [],
      }

      setUserProgress(defaultProgress)
      console.log("Set default progress for new user")
      return defaultProgress
    } catch (error) {
      console.error("Error fetching user progress:", error)

      // Set default progress on error
      const defaultProgress: UserProgress = {
        id: Date.now().toString(),
        userId: user.id,
        totalQuizzesTaken: 0,
        averageScore: 0,
        currentStreak: 0,
        bestStreak: 0,
        weeklyProgress: [],
      }

      setUserProgress(defaultProgress)
      return defaultProgress
    } finally {
      setProgressLoading(false)
    }
  }

  const fetchUserSubmissions = async (): Promise<UserSubmission[]> => {
    if (!user) return []

    try {
      // Use endpoint without userId, rely on token; add cache-busting
      const url = `${API_ENDPOINTS.GET_USER_DAILY_QUIZ_SUBMISSIONS()}?t=${Date.now()}`
      console.log("Fetching user submissions from:", url)

      const response = await apiUtils.get(url)

      if (response.ok) {
        // Read JSON directly to avoid multiple body reads
        const data = await response.json()
        const submissions = Array.isArray(data) ? data : []
        setUserSubmissions(submissions)
        console.log("User submissions data received:", submissions)
        return submissions
      }

      console.log("No submissions found or empty response")
      setUserSubmissions([])
      return []
    } catch (error) {
      console.error("Error fetching user submissions:", error)
      setUserSubmissions([])
      return []
    }
  }

  const generateQuizDays = (submissions: UserSubmission[] = userSubmissions) => {
    const today = new Date()
    const startDate = new Date(today)
    startDate.setDate(today.getDate() - 30)
    const days: QuizDay[] = []

    console.log("Generating calendar with submissions:", submissions.length)

    for (let i = 0; i <= 30; i++) {
      const currentDate = new Date(startDate)
      currentDate.setDate(startDate.getDate() + i)
      const dateString = currentDate.toISOString().split("T")[0]
      const isToday = dateString === today.toISOString().split("T")[0]
      const isFuture = currentDate > today

      // Check if there's a matching submission
      const submission = submissions.find((sub) => sub.date === dateString)

      days.push({
        date: dateString,
        day: currentDate.getDate().toString().padStart(2, "0"),
        month: currentDate.toLocaleDateString("en-US", { month: "short" }),
        year: currentDate.getFullYear().toString(),
        status: isFuture ? "locked" : submission ? "completed" : "available",
        score: submission?.score,
        totalQuestions: submission?.totalQuestions || 10,
        completedAt: submission?.submittedAt,
      })
    }

    setQuizDays(days.reverse())
    console.log("Generated quiz days:", days.length)
  }

  const handleDateSelect = async (date: string, status: string) => {
    if (status === "locked") return

    setSelectedDate(date)
    setShowQuiz(false)
    await fetchQuestionsForDate(date)
  }

  const handleStartQuiz = () => {
    setShowQuiz(true)
  }

  const handleBackToCalendar = () => {
    setShowQuiz(false)
  }

  const handleQuizSubmit = async (result: { dailyQuizId: string; answers: { [key: string]: number } }) => {
    if (!user) return

    try {
      const submissionPayload = {
        dailyQuizId: dailyQuizId,
        answers: result.answers,
      }

      console.log("Submitting quiz:", submissionPayload)

      const response = await apiUtils.post(API_ENDPOINTS.SUBMIT_DAILY_QUIZ, submissionPayload)

      if (response.ok) {
        console.log("Quiz submitted successfully")
        // Re-fetch user data to update UI with fresh data
        await Promise.all([fetchUserProgress(), fetchUserSubmissions()])
      } else {
        console.error("Failed to submit quiz:", response.status)
      }
    } catch (error) {
      console.error("Error submitting quiz:", error)
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case "completed":
        return "bg-green-100 text-green-800 border-green-200"
      case "available":
        return "bg-blue-100 text-blue-800 border-blue-200"
      case "locked":
        return "bg-gray-100 text-gray-400 border-gray-200"
      default:
        return "bg-gray-100 text-gray-600 border-gray-200"
    }
  }

  const selectedQuizDay = quizDays.find((day) => day.date === selectedDate)
  const isLoggedIn = !!user

  // Show quiz interface
  if (showQuiz) {
    return (
      <PublicQuiz
        questions={dailyQuestions}
        onQuizSubmit={handleQuizSubmit}
        onLoginPrompt={onLoginPrompt || (() => {})}
        isLoggedIn={isLoggedIn}
        onBackToCalendar={handleBackToCalendar}
      />
    )
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <Link to="/" className="flex items-center space-x-2 text-gray-600 hover:text-gray-900">
              <ArrowLeft className="h-5 w-5" />
              <span>Back to Home</span>
            </Link>
            <h1 className="text-xl font-bold text-gray-900">Daily Quiz Challenge</h1>
            <div className="flex items-center space-x-4">
              {isLoggedIn ? (
                <Link
                  to="/app"
                  className="bg-blue-600 text-white px-4 py-2 rounded-lg text-sm font-medium hover:bg-blue-700 transition-colors"
                >
                  Dashboard
                </Link>
              ) : (
                <div className="flex items-center space-x-2">
                  <Link to="/login" className="text-gray-600 hover:text-gray-900 px-3 py-2 text-sm font-medium">
                    Login
                  </Link>
                  <Link
                    to="/register"
                    className="bg-blue-600 text-white px-4 py-2 rounded-lg text-sm font-medium hover:bg-blue-700 transition-colors"
                  >
                    Sign Up
                  </Link>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Progress Stats for Logged In Users */}
        {isLoggedIn && (
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
            {progressLoading ? (
              // Loading skeleton for progress cards
              Array.from({ length: 4 }).map((_, index) => (
                <div key={index} className="bg-white p-6 rounded-xl shadow-sm border border-gray-200">
                  <div className="animate-pulse">
                    <div className="flex items-center justify-between">
                      <div>
                        <div className="h-4 bg-gray-200 rounded w-20 mb-2"></div>
                        <div className="h-8 bg-gray-200 rounded w-12"></div>
                      </div>
                      <div className="bg-gray-200 p-3 rounded-lg">
                        <div className="h-6 w-6 bg-gray-300 rounded"></div>
                      </div>
                    </div>
                  </div>
                </div>
              ))
            ) : userProgress ? (
              <>
                <div className="bg-white p-6 rounded-xl shadow-sm border border-gray-200">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-gray-600">Quizzes Taken</p>
                      <p className="text-2xl font-bold text-gray-900">{userProgress.totalQuizzesTaken}</p>
                    </div>
                    <div className="bg-blue-100 p-3 rounded-lg">
                      <FileText className="h-6 w-6 text-blue-600" />
                    </div>
                  </div>
                </div>
                <div className="bg-white p-6 rounded-xl shadow-sm border border-gray-200">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-gray-600">Average Score</p>
                      <p className="text-2xl font-bold text-gray-900">{userProgress.averageScore.toFixed(1)}%</p>
                    </div>
                    <div className="bg-green-100 p-3 rounded-lg">
                      <Trophy className="h-6 w-6 text-green-600" />
                    </div>
                  </div>
                </div>
                <div className="bg-white p-6 rounded-xl shadow-sm border border-gray-200">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-gray-600">Current Streak</p>
                      <p className="text-2xl font-bold text-gray-900">{userProgress.currentStreak}</p>
                    </div>
                    <div className="bg-orange-100 p-3 rounded-lg">
                      <TrendingUp className="h-6 w-6 text-orange-600" />
                    </div>
                  </div>
                </div>
                <div className="bg-white p-6 rounded-xl shadow-sm border border-gray-200">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-gray-600">Best Streak</p>
                      <p className="text-2xl font-bold text-gray-900">{userProgress.bestStreak}</p>
                    </div>
                    <div className="bg-purple-100 p-3 rounded-lg">
                      <Trophy className="h-6 w-6 text-purple-600" />
                    </div>
                  </div>
                </div>
              </>
            ) : (
              // Show default cards when no progress data
              <>
                <div className="bg-white p-6 rounded-xl shadow-sm border border-gray-200">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-gray-600">Quizzes Taken</p>
                      <p className="text-2xl font-bold text-gray-900">0</p>
                    </div>
                    <div className="bg-blue-100 p-3 rounded-lg">
                      <FileText className="h-6 w-6 text-blue-600" />
                    </div>
                  </div>
                </div>
                <div className="bg-white p-6 rounded-xl shadow-sm border border-gray-200">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-gray-600">Average Score</p>
                      <p className="text-2xl font-bold text-gray-900">0%</p>
                    </div>
                    <div className="bg-green-100 p-3 rounded-lg">
                      <Trophy className="h-6 w-6 text-green-600" />
                    </div>
                  </div>
                </div>
                <div className="bg-white p-6 rounded-xl shadow-sm border border-gray-200">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-gray-600">Current Streak</p>
                      <p className="text-2xl font-bold text-gray-900">0</p>
                    </div>
                    <div className="bg-orange-100 p-3 rounded-lg">
                      <TrendingUp className="h-6 w-6 text-orange-600" />
                    </div>
                  </div>
                </div>
                <div className="bg-white p-6 rounded-xl shadow-sm border border-gray-200">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-gray-600">Best Streak</p>
                      <p className="text-2xl font-bold text-gray-900">0</p>
                    </div>
                    <div className="bg-purple-100 p-3 rounded-lg">
                      <Trophy className="h-6 w-6 text-purple-600" />
                    </div>
                  </div>
                </div>
              </>
            )}
          </div>
        )}

        <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
          {/* Left Sidebar - Quiz Calendar */}
          <div className="lg:col-span-1">
            <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6 sticky top-24">
              <div className="flex items-center space-x-2 mb-6">
                <Calendar className="h-5 w-5 text-blue-600" />
                <h2 className="text-lg font-semibold text-gray-900">Quiz Calendar</h2>
              </div>
              <div className="space-y-2 max-h-96 overflow-y-auto">
                {quizDays.map((day) => (
                  <button
                    key={day.date}
                    onClick={() => handleDateSelect(day.date, day.status)}
                    disabled={day.status === "locked"}
                    className={`w-full p-3 rounded-lg border-2 transition-all text-left ${
                      selectedDate === day.date
                        ? "border-blue-500 bg-blue-50"
                        : `border-gray-200 hover:border-gray-300 ${
                            day.status === "locked" ? "cursor-not-allowed opacity-50" : "hover:bg-gray-50"
                          }`
                    }`}
                  >
                    <div className="flex items-center justify-between">
                      <div>
                        <div className="font-semibold text-gray-900">
                          {day.day} {day.month}
                        </div>
                        <div className="text-xs text-gray-500">{day.year}</div>
                        {isLoggedIn && day.completedAt && (
                          <div className="text-xs text-green-600 mt-1">
                            {new Date(day.completedAt).toLocaleTimeString([], {
                              hour: "2-digit",
                              minute: "2-digit",
                            })}
                          </div>
                        )}
                      </div>
                      <div className="flex items-center space-x-2">
                        {day.status === "completed" && day.score !== undefined && (
                          <div className="flex items-center space-x-1">
                            <Trophy className="h-4 w-4 text-yellow-500" />
                            <span className="text-sm font-medium">
                              {day.score}/{day.totalQuestions}
                            </span>
                          </div>
                        )}
                        <div
                          className={`px-2 py-1 rounded-full text-xs font-medium border ${getStatusColor(day.status)}`}
                        >
                          {day.status === "completed" ? "Done" : day.status === "available" ? "Available" : "Locked"}
                        </div>
                      </div>
                    </div>
                  </button>
                ))}
              </div>
            </div>
          </div>

          {/* Main Content */}
          <div className="lg:col-span-3">
            {selectedQuizDay && (
              <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-8">
                <div className="text-center mb-8">
                  <h1 className="text-3xl font-bold text-gray-900 mb-2">
                    Daily Quiz - {selectedQuizDay.day} {selectedQuizDay.month} {selectedQuizDay.year}
                  </h1>
                  <p className="text-gray-600">
                    {selectedQuizDay.status === "completed"
                      ? "You have already completed this quiz"
                      : "Test your UPSC knowledge with this quiz"}
                  </p>
                </div>

                {selectedQuizDay.status === "completed" && selectedQuizDay.score !== undefined ? (
                  <div className="bg-green-50 border border-green-200 rounded-lg p-6 mb-8">
                    <div className="flex items-center justify-center space-x-3 mb-4">
                      <CheckCircle className="h-8 w-8 text-green-600" />
                      <h3 className="text-xl font-semibold text-green-800">Quiz Completed!</h3>
                    </div>
                    <div className="text-center">
                      <div className="text-3xl font-bold text-green-600 mb-2">
                        {selectedQuizDay.score}/{selectedQuizDay.totalQuestions}
                      </div>
                      <p className="text-green-700">
                        Score: {Math.round((selectedQuizDay.score / selectedQuizDay.totalQuestions!) * 100)}%
                      </p>
                    </div>
                  </div>
                ) : (
                  <div className="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg p-6 mb-8">
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-6 text-center">
                      <div>
                        <div className="bg-white p-4 rounded-lg shadow-sm">
                          <Clock className="h-8 w-8 text-blue-600 mx-auto mb-2" />
                          <p className="font-semibold text-gray-900">Duration</p>
                          <p className="text-gray-600">30 minutes</p>
                        </div>
                      </div>
                      <div>
                        <div className="bg-white p-4 rounded-lg shadow-sm">
                          <FileText className="h-8 w-8 text-green-600 mx-auto mb-2" />
                          <p className="font-semibold text-gray-900">Questions</p>
                          <p className="text-gray-600">{dailyQuestions.length} Questions</p>
                        </div>
                      </div>
                      <div>
                        <div className="bg-white p-4 rounded-lg shadow-sm">
                          <Trophy className="h-8 w-8 text-yellow-600 mx-auto mb-2" />
                          <p className="font-semibold text-gray-900">Difficulty</p>
                          <p className="text-gray-600">Mixed</p>
                        </div>
                      </div>
                    </div>
                  </div>
                )}

                <div className="text-center space-y-4">
                  {selectedQuizDay.status === "available" ? (
                    <button
                      onClick={handleStartQuiz}
                      disabled={loading || dailyQuestions.length === 0}
                      className="bg-gradient-to-r from-blue-600 to-indigo-600 text-white px-8 py-4 rounded-xl font-semibold text-lg hover:from-blue-700 hover:to-indigo-700 transition-all transform hover:scale-105 shadow-lg disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      {dailyQuestions.length === 0 ? "No Questions Available" : "Start Quiz"}
                    </button>
                  ) : selectedQuizDay.status === "completed" ? (
                    <button
                      onClick={handleStartQuiz}
                      disabled={loading || dailyQuestions.length === 0}
                      className="bg-gray-600 text-white px-8 py-4 rounded-xl font-semibold text-lg hover:bg-gray-700 transition-all disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      {dailyQuestions.length === 0 ? "No Questions Available" : "Retake Quiz"}
                    </button>
                  ) : null}

                  {!isLoggedIn && (
                    <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                      <p className="text-sm text-blue-800 mb-3">Want to track your progress and access more quizzes?</p>
                      <Link
                        to="/register"
                        className="bg-blue-600 text-white px-6 py-2 rounded-lg text-sm font-medium hover:bg-blue-700 transition-colors inline-flex items-center space-x-2"
                      >
                        <User className="h-4 w-4" />
                        <span>Create Free Account</span>
                      </Link>
                    </div>
                  )}
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}

export default DailyQuiz