import React, { useState, useEffect } from 'react';
import { useNavigate, Link } from 'react-router-dom';
import { Clock, CheckCircle, XCircle, TrendingUp, Users, Award, ArrowRight, Star, Shield, MessageCircle, Send } from 'lucide-react';
import { API_ENDPOINTS, apiUtils } from '../config/api';

interface Question {
  id: number;
  question: string;
  options: string[];
  correctAnswer: number;
  explanation?: string;
}

function DemoExam() {
  const navigate = useNavigate();
  const [currentQuestion, setCurrentQuestion] = useState(0);
  const [selectedAnswers, setSelectedAnswers] = useState<{ [key: number]: number }>({});
  const [timeLeft, setTimeLeft] = useState(900); // 15 minutes for demo
  const [questions, setQuestions] = useState<Question[]>([]);
  const [loading, setLoading] = useState(true);
  const [examCompleted, setExamCompleted] = useState(false);
  const [showResults, setShowResults] = useState(false);
  const [score, setScore] = useState(0);
  const [examStarted, setExamStarted] = useState(false); // New state for start screen

  useEffect(() => {
    fetchDemoQuestions();
  }, []);

  useEffect(() => {
    if (timeLeft > 0 && !examCompleted && examStarted) {
      const timer = setInterval(() => {
        setTimeLeft(prev => prev - 1);
      }, 1000);
      return () => clearInterval(timer);
    } else if (timeLeft === 0 && examStarted) {
      handleSubmit();
    }
  }, [timeLeft, examCompleted, examStarted]);

  const fetchDemoQuestions = async () => {
    try {
      setLoading(true);
      // Try Spring Boot demo questions endpoint first
      const response = await apiUtils.get(API_ENDPOINTS.DEMO_QUESTIONS);
      if (response.ok) {
        const apiQuestions = await response.json();
        setQuestions(apiQuestions.slice(0, 10));
      } else {
        // Fallback to daily quiz questions with demo flag
        const dailyQuizResponse = await apiUtils.get(`${API_ENDPOINTS.DAILY_QUIZ_QUESTIONS}?demo=true&limit=10`);
        if (dailyQuizResponse.ok) {
          const dailyQuizData = await dailyQuizResponse.json();
          setQuestions(dailyQuizData.slice(0, 10));
        } else {
          // Final fallback to hardcoded demo questions
          setQuestions(demoQuestions);
        }
      }
    } catch (err) {
      console.error('Error fetching demo questions:', err);
      setQuestions(demoQuestions);
    } finally {
      setLoading(false);
    }
  };

  const demoQuestions: Question[] = [
    {
      id: 1,
      question: "Which of the following is the largest planet in our solar system?",
      options: ["Earth", "Jupiter", "Saturn", "Neptune"],
      correctAnswer: 1,
      explanation: "Jupiter is the largest planet in our solar system, with a mass greater than all other planets combined."
    },
    {
      id: 2,
      question: "Who was the first Prime Minister of India?",
      options: ["Mahatma Gandhi", "Jawaharlal Nehru", "Sardar Patel", "Dr. Rajendra Prasad"],
      correctAnswer: 1,
      explanation: "Jawaharlal Nehru became India's first Prime Minister on August 15, 1947."
    },
    {
      id: 3,
      question: "The Indian Constitution was adopted on which date?",
      options: ["15th August 1947", "26th January 1950", "26th November 1949", "2nd October 1948"],
      correctAnswer: 2,
      explanation: "The Indian Constitution was adopted on November 26, 1949, and came into effect on January 26, 1950."
    },
    {
      id: 4,
      question: "Which is the highest mountain peak in India?",
      options: ["K2", "Kanchenjunga", "Nanda Devi", "Mount Everest"],
      correctAnswer: 1,
      explanation: "Kanchenjunga is the highest mountain peak entirely within India at 8,586 meters."
    },
    {
      id: 5,
      question: "The Tropic of Cancer passes through how many Indian states?",
      options: ["6", "7", "8", "9"],
      correctAnswer: 2,
      explanation: "The Tropic of Cancer passes through 8 Indian states: Gujarat, Rajasthan, Madhya Pradesh, Chhattisgarh, Jharkhand, West Bengal, Tripura, and Mizoram."
    }
  ];

  const formatTime = (seconds: number) => {
    const minutes = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  const handleAnswerSelect = (optionIndex: number) => {
    setSelectedAnswers(prev => ({
      ...prev,
      [currentQuestion]: optionIndex
    }));
  };

  const handleNext = () => {
    if (currentQuestion < questions.length - 1) {
      setCurrentQuestion(prev => prev + 1);
    }
  };

  const handlePrevious = () => {
    if (currentQuestion > 0) {
      setCurrentQuestion(prev => prev - 1);
    }
  };

  const handleStartExam = () => {
    setExamStarted(true);
  };

  const handleSubmit = () => {
    const correctAnswers = questions.reduce((count, question, index) => {
      return selectedAnswers[index] === question.correctAnswer ? count + 1 : count;
    }, 0);

    setScore(Math.round((correctAnswers / questions.length) * 100));
    setExamCompleted(true);
    setShowResults(true);
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading your free demo exam...</p>
        </div>
      </div>
    );
  }

  // Start screen - show before exam begins
  if (!examStarted) {
    return (
      <div className="min-h-screen bg-gray-50">
        {/* Header */}
        <div className="bg-white shadow-sm border-b border-gray-200">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="flex items-center justify-between h-16">
              <Link to="/" className="text-blue-600 hover:text-blue-700 font-semibold">
                ← Back to Home
              </Link>
              <h1 className="text-lg sm:text-xl font-bold text-gray-900">Free UPSC Demo Test</h1>
              <div></div>
            </div>
          </div>
        </div>

        <div className="max-w-4xl mx-auto px-3 sm:px-6 lg:px-8 py-4 sm:py-8">
          {/* Welcome Card */}
          <div className="bg-white rounded-2xl shadow-xl p-4 sm:p-8 lg:p-12 text-center">
            <div className="mb-8">
              <div className="bg-blue-100 rounded-full w-20 h-20 flex items-center justify-center mx-auto mb-6">
                <Award className="h-10 w-10 text-blue-600" />
              </div>
              <h2 className="text-2xl sm:text-3xl font-bold text-gray-900 mb-4 leading-tight">
                Welcome to Your Free UPSC Demo Test!
              </h2>
              <p className="text-base sm:text-lg text-gray-600 mb-8 leading-relaxed">
                Experience our comprehensive UPSC preparation platform with this sample test.
                Get a taste of what makes our students successful!
              </p>
            </div>

            {/* Test Details */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
              <div className="bg-blue-50 rounded-xl p-6">
                <div className="bg-blue-100 rounded-full w-12 h-12 flex items-center justify-center mx-auto mb-4">
                  <Clock className="h-6 w-6 text-blue-600" />
                </div>
                <h3 className="text-sm sm:text-base font-semibold text-gray-900 mb-2">Duration</h3>
                <p className="text-sm sm:text-base text-gray-600">15 minutes</p>
              </div>
              <div className="bg-green-50 rounded-xl p-6">
                <div className="bg-green-100 rounded-full w-12 h-12 flex items-center justify-center mx-auto mb-4">
                  <CheckCircle className="h-6 w-6 text-green-600" />
                </div>
                <h3 className="text-sm sm:text-base font-semibold text-gray-900 mb-2">Questions</h3>
                <p className="text-sm sm:text-base text-gray-600">{questions.length} MCQs</p>
              </div>
              <div className="bg-purple-50 rounded-xl p-6">
                <div className="bg-purple-100 rounded-full w-12 h-12 flex items-center justify-center mx-auto mb-4">
                  <TrendingUp className="h-6 w-6 text-purple-600" />
                </div>
                <h3 className="text-sm sm:text-base font-semibold text-gray-900 mb-2">Difficulty</h3>
                <p className="text-sm sm:text-base text-gray-600">UPSC Level</p>
              </div>
            </div>

            {/* Instructions */}
            <div className="bg-gray-50 rounded-xl p-4 sm:p-6 mb-8 text-left">
              <h3 className="text-base sm:text-lg font-semibold text-gray-900 mb-4 text-center">📋 Test Instructions</h3>
              <ul className="space-y-3 text-sm sm:text-base text-gray-700">
                <li className="flex items-start space-x-2">
                  <span className="text-blue-600 font-bold">•</span>
                  <span>You have <strong>15 minutes</strong> to complete {questions.length} questions</span>
                </li>
                <li className="flex items-start space-x-2">
                  <span className="text-blue-600 font-bold">•</span>
                  <span>Each question has <strong>4 options</strong> with only one correct answer</span>
                </li>
                <li className="flex items-start space-x-2">
                  <span className="text-blue-600 font-bold">•</span>
                  <span>You can <strong>navigate</strong> between questions and change your answers</span>
                </li>
                <li className="flex items-start space-x-2">
                  <span className="text-blue-600 font-bold">•</span>
                  <span>The test will <strong>auto-submit</strong> when time runs out</span>
                </li>
                <li className="flex items-start space-x-2">
                  <span className="text-blue-600 font-bold">•</span>
                  <span>You'll get <strong>detailed results</strong> and explanations after completion</span>
                </li>
              </ul>
            </div>

            {/* Start Button */}
            <button
              onClick={handleStartExam}
              className="bg-gradient-to-r from-blue-600 to-indigo-600 text-white px-8 sm:px-12 py-3 sm:py-4 rounded-xl font-bold text-base sm:text-lg hover:from-blue-700 hover:to-indigo-700 transition-all transform hover:scale-105 shadow-lg inline-flex items-center space-x-2 sm:space-x-3"
            >
              <span>Start Test Now</span>
              <ArrowRight className="h-5 w-5 sm:h-6 sm:w-6" />
            </button>

            <p className="text-xs sm:text-sm text-gray-500 mt-4 px-4 sm:px-0">
              Ready to begin? Click the button above to start your timer and begin the test.
            </p>
          </div>

          {/* Benefits Preview */}
          <div className="mt-8 bg-gradient-to-r from-green-600 to-emerald-600 rounded-xl p-8 text-white text-center">
            <h3 className="text-xl sm:text-2xl font-bold mb-4">🚀 What You'll Experience</h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="text-center">
                <div className="bg-white bg-opacity-20 rounded-lg p-4 mb-2">
                  <Shield className="h-8 w-8 mx-auto text-green-200" />
                </div>
                <h4 className="text-sm sm:text-base font-semibold mb-1">UPSC-Pattern Questions</h4>
                <p className="text-xs sm:text-sm text-green-100">Authentic exam-style questions</p>
              </div>
              <div className="text-center">
                <div className="bg-white bg-opacity-20 rounded-lg p-4 mb-2">
                  <TrendingUp className="h-8 w-8 mx-auto text-green-200" />
                </div>
                <h4 className="text-sm sm:text-base font-semibold mb-1">Instant Analysis</h4>
                <p className="text-xs sm:text-sm text-green-100">Detailed performance insights</p>
              </div>
              <div className="text-center">
                <div className="bg-white bg-opacity-20 rounded-lg p-4 mb-2">
                  <Star className="h-8 w-8 mx-auto text-green-200" />
                </div>
                <h4 className="text-sm sm:text-base font-semibold mb-1">Expert Explanations</h4>
                <p className="text-xs sm:text-sm text-green-100">Learn from detailed solutions</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (showResults) {
    return (
      <div className="min-h-screen bg-gray-50">
        {/* Header */}
        <div className="bg-white shadow-sm border-b border-gray-200">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="flex items-center justify-between h-16">
              <Link to="/" className="text-blue-600 hover:text-blue-700 font-semibold">
                ← Back to Home
              </Link>
              <h1 className="text-xl font-bold text-gray-900">Demo Results</h1>
              <div></div>
            </div>
          </div>
        </div>

        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          {/* Social Channels CTA */}
          <div className="bg-gradient-to-r from-blue-600 to-indigo-600 rounded-xl p-8 text-white text-center">
            <h3 className="text-2xl font-bold mb-4">📚 Stay Updated with Daily UPSC Content!</h3>
            <p className="text-blue-100 mb-6 text-lg">
              Join our active community for daily current affairs, study tips, and exclusive content
            </p>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">


              {/* Telegram Channel */}
              <a
                href="https://t.me/letscrackupsc"
                target="_blank"
                rel="noopener noreferrer"
                className="bg-white bg-opacity-20 rounded-xl p-6 hover:bg-opacity-30 transition-all group"
              >
                <div className="flex items-center justify-center mb-4">
                  <div className="bg-blue-500 rounded-full p-3 group-hover:scale-110 transition-transform">
                    <Send className="h-8 w-8 text-white" />
                  </div>
                </div>
                <h4 className="font-bold text-xl mb-2">Telegram Channel</h4>
                <p className="text-blue-100 text-sm mb-4">
                  ✅ Community discussions<br />
                  ✅ Doubt clearing<br />
                  ✅ Study groups<br />
                  ✅ Peer support
                </p>
                <div className="bg-blue-500 text-white px-4 py-2 rounded-lg font-semibold inline-block">
                  Join Channel →
                </div>
              </a>

              {/* WhatsApp Channel */}
              <a
                href="https://whatsapp.com/channel/0029Vb6fu778PgsCE0AbJY1R"
                target="_blank"
                rel="noopener noreferrer"
                className="bg-white bg-opacity-20 rounded-xl p-6 hover:bg-opacity-30 transition-all group"
              >
                <div className="flex items-center justify-center mb-4">
                  <div className="bg-green-500 rounded-full p-3 group-hover:scale-110 transition-transform">
                    <MessageCircle className="h-8 w-8 text-white" />
                  </div>
                </div>
                <h4 className="font-bold text-xl mb-2">WhatsApp Channel</h4>
                <p className="text-blue-100 text-sm mb-4">
                  ✅ Daily current affairs<br />
                  ✅ Study materials<br />
                  ✅ Exam notifications<br />
                  ✅ Quick updates
                </p>
                <div className="bg-green-500 text-white px-4 py-2 rounded-lg font-semibold inline-block">
                  Join Channel →
                </div>
              </a>
            </div>

            <p className="text-blue-200 text-sm">
              💡 <strong>Pro Tip:</strong> Join both channels to never miss important updates and connect with fellow aspirants!
            </p>
          </div>
          {/* Trust Elements */}
          <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-8">
            <div className="text-center mb-6">
              <h3 className="text-xl font-semibold text-gray-900 mb-2">Join 50,000+ Successful UPSC Aspirants</h3>
              <div className="flex items-center justify-center space-x-1 mb-4">
                {[1, 2, 3, 4, 5].map((star) => (
                  <Star key={star} className="h-5 w-5 text-yellow-400 fill-current" />
                ))}
                <span className="ml-2 text-gray-600">4.9/5 from 2,500+ reviews</span>
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-6 text-center">
              <div className="flex items-center justify-center space-x-2">
                <Shield className="h-5 w-5 text-green-500" />
                <span className="text-gray-700">100% Free Trial</span>
              </div>
              <div className="flex items-center justify-center space-x-2">
                <CheckCircle className="h-5 w-5 text-green-500" />
                <span className="text-gray-700">No Credit Card Required</span>
              </div>
              <div className="flex items-center justify-center space-x-2">
                <Users className="h-5 w-5 text-green-500" />
                <span className="text-gray-700">Expert Support</span>
              </div>
            </div>
          </div>
          {/* Results Card */}
          <div className="bg-white rounded-xl shadow-lg border border-gray-200 overflow-hidden mb-8">
            <div className="bg-gradient-to-r from-blue-600 to-indigo-600 p-8 text-white text-center">
              <div className="mb-4">
                <Award className="h-16 w-16 mx-auto mb-4 text-yellow-300" />
                <h2 className="text-3xl font-bold mb-2">Demo Complete!</h2>
                <p className="text-blue-100">Here's how you performed</p>
              </div>

              <div className="grid grid-cols-3 gap-6 mt-8">
                <div className="text-center">
                  <div className="text-3xl font-bold">{score}%</div>
                  <div className="text-blue-100">Score</div>
                </div>
                <div className="text-center">
                  <div className="text-3xl font-bold">{Object.keys(selectedAnswers).length}</div>
                  <div className="text-blue-100">Attempted</div>
                </div>
                <div className="text-center">
                  <div className="text-3xl font-bold">{questions.length}</div>
                  <div className="text-blue-100">Total Questions</div>
                </div>
              </div>
            </div>

            {/* Detailed Analysis Preview */}
            <div className="p-8">
              <h3 className="text-xl font-semibold text-gray-900 mb-6">Question Analysis</h3>
              <div className="space-y-4">
                {questions.slice(0, 3).map((question, index) => {
                  const userAnswer = selectedAnswers[index];
                  const isCorrect = userAnswer === question.correctAnswer;

                  return (
                    <div key={index} className="border border-gray-200 rounded-lg p-4">
                      <div className="flex items-start space-x-3">
                        {isCorrect ? (
                          <CheckCircle className="h-6 w-6 text-green-500 mt-1" />
                        ) : (
                          <XCircle className="h-6 w-6 text-red-500 mt-1" />
                        )}
                        <div className="flex-1">
                          <p className="font-medium text-gray-900 mb-2">Q{index + 1}: {question.question}</p>
                          <div className="text-sm text-gray-600">
                            <p>Your answer: <span className={isCorrect ? 'text-green-600' : 'text-red-600'}>{question.options[userAnswer] || 'Not answered'}</span></p>
                            <p>Correct answer: <span className="text-green-600">{question.options[question.correctAnswer]}</span></p>
                            {question.explanation && (
                              <p className="mt-2 text-gray-700 bg-gray-50 p-2 rounded">{question.explanation}</p>
                            )}
                          </div>
                        </div>
                      </div>
                    </div>
                  );
                })}

                {questions.length > 3 && (
                  <div className="text-center py-4 bg-gray-50 rounded-lg border-2 border-dashed border-gray-300">
                    <p className="text-gray-600 mb-2">Want to see analysis for all {questions.length} questions?</p>
                    <p className="text-sm text-gray-500">Register for detailed explanations, performance trends, and more!</p>
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* Upgrade CTA */}
          <div className="bg-gradient-to-r from-green-600 to-emerald-600 rounded-xl p-8 text-white text-center mb-8">
            <h3 className="text-2xl font-bold mb-4">🎉 Great job on completing the demo!</h3>
            <p className="text-green-100 mb-6 text-lg">
              Ready to unlock your full potential with our complete UPSC preparation platform?
            </p>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
              <div className="text-center">
                <div className="bg-white bg-opacity-20 rounded-lg p-4 mb-2">
                  <TrendingUp className="h-8 w-8 mx-auto text-green-200" />
                </div>
                <h4 className="font-semibold mb-1">50+ Full Tests</h4>
                <p className="text-sm text-green-100">Complete mock exams with detailed analysis</p>
              </div>
              <div className="text-center">
                <div className="bg-white bg-opacity-20 rounded-lg p-4 mb-2">
                  <Users className="h-8 w-8 mx-auto text-green-200" />
                </div>
                <h4 className="font-semibold mb-1">Performance Tracking</h4>
                <p className="text-sm text-green-100">AI-powered insights and progress analytics</p>
              </div>
              <div className="text-center">
                <div className="bg-white bg-opacity-20 rounded-lg p-4 mb-2">
                  <Award className="h-8 w-8 mx-auto text-green-200" />
                </div>
                <h4 className="font-semibold mb-1">Expert Content</h4>
                <p className="text-sm text-green-100">Curated by successful IAS officers</p>
              </div>
            </div>

            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link
                to="/register"
                className="bg-white text-green-600 px-8 py-4 rounded-xl font-bold text-lg hover:bg-gray-100 transition-colors inline-flex items-center justify-center space-x-2"
              >
                <span>Start Free Trial</span>
                <ArrowRight className="h-5 w-5" />
              </Link>
            </div>
          </div>




        </div>
      </div>
    );
  }

  const currentQ = questions[currentQuestion];

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b border-gray-200 sticky top-0 z-10">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div>
              <h1 className="text-base sm:text-lg font-semibold text-gray-900">Free UPSC Demo Test</h1>
              <p className="text-xs sm:text-sm text-gray-600">Question {currentQuestion + 1} of {questions.length}</p>
            </div>
            <div className="flex items-center space-x-4">
              <div className="flex items-center space-x-2 bg-orange-50 px-3 py-2 rounded-lg">
                <Clock className="h-5 w-5 text-orange-600" />
                <span className="text-sm sm:text-base font-mono text-orange-600 font-medium">{formatTime(timeLeft)}</span>
              </div>
              <button
                onClick={handleSubmit}
                className="bg-green-600 text-white px-3 sm:px-4 py-2 rounded-lg text-sm sm:text-base font-medium hover:bg-green-700 transition-colors"
              >
                Submit Test
              </button>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-3 sm:px-6 lg:px-8 py-4 sm:py-8">
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
          {/* Question Panel */}
          <div className="lg:col-span-3">
            <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-8">
              <div className="mb-6">
                <h2 className="text-lg sm:text-xl font-semibold text-gray-900 mb-4">
                  Question {currentQuestion + 1}
                </h2>
                <p className="text-gray-700 text-base sm:text-lg leading-relaxed">
                  {currentQ.question}
                </p>
              </div>

              <div className="space-y-3">
                {currentQ.options.map((option, index) => (
                  <button
                    key={index}
                    onClick={() => handleAnswerSelect(index)}
                    className={`w-full text-left p-3 sm:p-4 rounded-lg border-2 transition-all text-sm sm:text-base ${selectedAnswers[currentQuestion] === index
                      ? 'border-blue-500 bg-blue-50 text-blue-900'
                      : 'border-gray-200 hover:border-gray-300 hover:bg-gray-50'
                      }`}
                  >
                    <span className="text-sm sm:text-base font-medium mr-2 sm:mr-3">
                      {String.fromCharCode(65 + index)}.
                    </span>
                    {option}
                  </button>
                ))}
              </div>

              <div className="flex justify-between mt-8">
                <button
                  onClick={handlePrevious}
                  disabled={currentQuestion === 0}
                  className="px-4 sm:px-6 py-2 border border-gray-300 rounded-lg text-sm sm:text-base font-medium text-gray-700 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  Previous
                </button>
                <button
                  onClick={handleNext}
                  disabled={currentQuestion === questions.length - 1}
                  className="px-4 sm:px-6 py-2 bg-blue-600 text-white rounded-lg text-sm sm:text-base font-medium hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  Next
                </button>
              </div>
            </div>
          </div>

          {/* Sidebar */}
          <div className="lg:col-span-1">
            <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6 sticky top-24">
              <h3 className="text-sm sm:text-base font-semibold text-gray-900 mb-4">Question Navigator</h3>
              <div className="grid grid-cols-5 gap-2 mb-6">
                {questions.map((_, index) => (
                  <button
                    key={index}
                    onClick={() => setCurrentQuestion(index)}
                    className={`w-7 h-7 sm:w-8 sm:h-8 rounded text-xs sm:text-sm font-medium ${selectedAnswers[index] !== undefined
                      ? 'bg-green-500 text-white'
                      : index === currentQuestion
                        ? 'bg-blue-500 text-white'
                        : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
                      }`}
                  >
                    {index + 1}
                  </button>
                ))}
              </div>

              <div className="space-y-2 text-xs sm:text-sm">
                <div className="flex items-center space-x-2">
                  <div className="w-4 h-4 bg-green-500 rounded"></div>
                  <span>Answered: {Object.keys(selectedAnswers).length}</span>
                </div>
                <div className="flex items-center space-x-2">
                  <div className="w-4 h-4 bg-gray-200 rounded"></div>
                  <span>Not Answered: {questions.length - Object.keys(selectedAnswers).length}</span>
                </div>
              </div>

              <div className="mt-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
                <p className="text-xs sm:text-sm font-medium text-blue-800 mb-2">💡 Demo Features</p>
                <ul className="text-xs text-blue-700 space-y-1">
                  <li>• Instant results & explanations</li>
                  <li>• Performance analysis</li>
                  <li>• UPSC-style questions</li>
                  <li>• No registration required</li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

export default DemoExam;



