import React, { useEffect, useState } from 'react'
import { <PERSON>, useNavigate, useParams } from 'react-router-dom'
import { ArrowLeft, Award, Calendar, Clock, FileText, HelpCircle, Play, ShieldCheck, Trophy, Lock, Crown } from 'lucide-react'
import moment from 'moment-timezone'
import { API_ENDPOINTS, apiUtils } from '../config/api'

interface Exam {
  id: string
  title: string
  description: string
  duration: number
  totalQuestions: number
  totalMarks: number
  passingMarks: number
  difficulty: string
  status: 'active' | 'inactive' | 'completed' | 'draft'
  startTime: string
  instructions: string
  attempts: number
  // Some APIs send the key spelled differently; handle both:
  isPremium?: number
  isPremimum?: number
}

interface PurchaseAccessResponse {
  accessGranted?: boolean
  // plus any other fields the API might return
}

const getDerivedExamStatus = (exam: Exam | null): 'upcoming' | 'live' | 'completed' | 'loading' => {
  if (!exam) return 'loading'
  if (exam.status !== 'active') {
    return 'completed'
  }
  const today = moment.utc().startOf('day')
  const examDate = moment.utc(exam.startTime).startOf('day')
  if (examDate.isAfter(today)) {
    return 'upcoming'
  } else if (examDate.isSame(today)) {
    return 'live'
  } else {
    return 'completed'
  }
}

function ExamDetails() {
  const { examId } = useParams<{ examId: string }>()
  const navigate = useNavigate()

  const [exam, setExam] = useState<Exam | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState('')

  // Premium access states
  const [accessChecking, setAccessChecking] = useState(false)
  const [hasAccess, setHasAccess] = useState<boolean>(false)
  const [accessError, setAccessError] = useState<string>('')

  useEffect(() => {
    if (examId) {
      fetchExamDetails(examId)
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [examId])

  useEffect(() => {
    // After exam is loaded, if it's premium, check access immediately using the exam id
    if (exam) {
      const premiumFlag = Number((exam as any).isPremium ?? (exam as any).isPremimum ?? 0) === 1
      if (premiumFlag) {
        checkPurchaseAccess(exam.id)
      } else {
        // Not premium, allow access
        setHasAccess(true)
        setAccessChecking(false)
        setAccessError('')
      }
    }
  }, [exam])

  const fetchExamDetails = async (id: string) => {
    try {
      setLoading(true)
      setError('')
      const response = await apiUtils.get(`${API_ENDPOINTS.EXAMS}/${id}`)
      if (response.ok) {
        const data: Exam = await response.json()
        setExam(data)
      } else {
        setError('Failed to load exam details.')
      }
    } catch (err) {
      setError('A network error occurred.')
    } finally {
      setLoading(false)
    }
  }

  const checkPurchaseAccess = async (id: string) => {
    // id must be the exam id per requirement
    setAccessChecking(true)
    setAccessError('')
    try {
      const response = await apiUtils.get(`${API_ENDPOINTS.PURCHASES}/access/${id}`)
      if (response.ok) {
        // Some servers may return 200 with empty body when no access;
        // handle content-type safely
        const contentType = response.headers.get('content-type') || ''
        if (contentType.includes('application/json')) {
          const data: PurchaseAccessResponse = await response.json()
          const granted = Boolean(data?.accessGranted ?? true)
          setHasAccess(granted)
          if (!granted) {
            setAccessError('Access Restricted. Please purchase to proceed.')
          }
        } else {
          const text = await response.text()
          const hasBody = text && text.trim().length > 0
          setHasAccess(hasBody)
          if (!hasBody) {
            setAccessError('Access Restricted. Please purchase to proceed.')
          }
        }
      } else {
        setHasAccess(false)
        setAccessError('Access Restricted. Please purchase to proceed.')
      }
    } catch {
      setHasAccess(false)
      setAccessError('Unable to verify access. Please try again.')
    } finally {
      setAccessChecking(false)
    }
  }

  const handleStartExam = () => {
    navigate(`/app/exams-start/${examId}`)
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-violet-600"></div>
      </div>
    )
  }

  if (error || !exam) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center text-center px-4">
        <div>
          <HelpCircle className="h-12 w-12 text-red-500 mx-auto mb-4" />
          <p className="text-xl font-semibold text-gray-800">{error || 'Exam not found'}</p>
          <Link to="/app/exams" className="text-violet-600 font-medium mt-4 inline-block">
            {'\u2190'} Back to Exams
          </Link>
        </div>
      </div>
    )
  }

  const derivedStatus = getDerivedExamStatus(exam)
  const isPremiumExam = Number((exam as any).isPremium ?? (exam as any).isPremimum ?? 0) === 1

  const getActionButton = () => {
    // Premium gating first
    if (isPremiumExam) {
      if (accessChecking) {
        return (
          <button
            disabled
            className="w-full sm:w-auto bg-gray-200 text-gray-600 px-8 py-4 rounded-xl font-semibold text-lg flex items-center justify-center space-x-2 cursor-not-allowed"
          >
            <Lock className="h-5 w-5" />
            <span>Checking access...</span>
          </button>
        )
      }
      if (!hasAccess) {
        return (
          <div className="w-full max-w-xl mx-auto">
            <div className="rounded-2xl border border-amber-200 bg-amber-50 p-5 text-center">
              <div className="mx-auto mb-2 flex h-10 w-10 items-center justify-center rounded-full bg-amber-100">
                <Lock className="h-5 w-5 text-amber-700" />
              </div>
              <p className="font-semibold text-amber-800">Access Restricted</p>
              <p className="mt-1 text-sm text-amber-700">
                {accessError || 'This is a premium exam. Please purchase to unlock.'}
              </p>
            </div>
          </div>
        )
      }
    }

    // If we reach here, either not premium or premium with access granted
    switch (derivedStatus) {
      case 'live':
        return (
          <button
            onClick={handleStartExam}
            className="w-full sm:w-auto bg-emerald-600 text-white px-8 py-4 rounded-xl font-semibold text-lg hover:bg-emerald-700 transition-colors flex items-center justify-center space-x-2 mx-auto animate-pulse"
          >
            <Play className="h-5 w-5" />
            <span>Start Live Exam</span>
          </button>
        )
      case 'completed':
        return (
          <div className="flex flex-col sm:flex-row items-center justify-center gap-4">
            <button
              onClick={handleStartExam}
              className="w-full sm:w-auto bg-violet-600 text-white px-8 py-3 rounded-xl font-semibold text-lg hover:bg-violet-700 transition-colors flex items-center justify-center space-x-2"
            >
              <Play className="h-5 w-5" />
              <span>Start</span>
            </button>
            <Link
              to={`/app/ranking/${exam.id}`}
              className="w-full sm:w-auto bg-yellow-100 text-yellow-800 px-8 py-3 rounded-xl font-semibold text-lg hover:bg-yellow-200 transition-colors flex items-center justify-center space-x-2"
            >
              <Trophy className="h-5 w-5" />
              <span>View Ranking</span>
            </Link>
          </div>
        )
      case 'upcoming':
        return (
          <button
            disabled
            className="w-full sm:w-auto bg-gray-200 text-gray-700 px-8 py-4 rounded-xl font-semibold text-lg flex items-center justify-center space-x-2 cursor-not-allowed"
          >
            <Clock className="h-5 w-5" />
            <span>Upcoming on {moment(exam.startTime).local().format('ll')}</span>
          </button>
        )
      default:
        return null
    }
  }

  const localStart = moment(exam.startTime).local()
  const tz = moment.tz.guess()

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-5xl mx-auto px-4">
        <Link
          to="/app/exams"
          className="group inline-flex items-center text-gray-600 hover:text-gray-900 mb-6 font-medium"
        >
          <ArrowLeft className="h-5 w-5 mr-2 transition-transform group-hover:-translate-x-0.5" />
          Back to All Exams
        </Link>

        <div className="relative overflow-hidden rounded-2xl border bg-white shadow-sm">
          {/* Header */}
          <div className="border-b bg-gradient-to-br from-gray-50 to-white px-6 py-6 md:px-8 md:py-8">
            <div className="flex flex-wrap items-center justify-between gap-3">
              <div>
                <div className="flex items-center gap-3">
                  {isPremiumExam && (
                    <span className="inline-flex items-center gap-1 rounded-full bg-gradient-to-r from-amber-100 to-rose-100 px-3 py-1 text-sm font-semibold text-amber-800">
                      <Crown className="h-4 w-4 text-amber-700" />
                      Premium Exam
                    </span>
                  )}
                  <span
                    className={`inline-flex items-center rounded-full px-3 py-1 text-xs font-semibold ${
                      derivedStatus === 'live'
                        ? 'bg-emerald-100 text-emerald-800'
                        : derivedStatus === 'upcoming'
                          ? 'bg-sky-100 text-sky-800'
                          : 'bg-gray-100 text-gray-800'
                    }`}
                  >
                    {derivedStatus === 'live'
                      ? 'Live Today'
                      : derivedStatus === 'upcoming'
                        ? 'Upcoming'
                        : 'Completed'}
                  </span>
                </div>
                <h1 className="mt-3 text-2xl md:text-3xl font-bold tracking-tight text-gray-900">
                  {exam.title}
                </h1>
                {exam.description && (
                  <p className="mt-2 max-w-3xl text-gray-600">{exam.description}</p>
                )}
              </div>

              <div className="text-right">
                <div className="text-sm text-gray-500 flex items-center justify-end gap-2">
                  <Calendar className="h-4 w-4" />
                  <span>{localStart.format('ll, LT')}</span>
                </div>
                <div className="mt-1 text-xs text-gray-400">Timezone: {tz}</div>
              </div>
            </div>
          </div>

          {/* Content */}
          <div className="px-6 py-6 md:px-8 md:py-8">
            {/* Stats */}
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 md:gap-6 mb-8">
              <div className="rounded-xl border bg-gradient-to-b from-violet-50 to-white p-4 text-center">
                <div className="mx-auto mb-2 flex h-10 w-10 items-center justify-center rounded-lg bg-violet-100">
                  <Clock className="h-5 w-5 text-violet-700" />
                </div>
                <div className="text-2xl font-bold text-gray-900">{exam.duration}</div>
                <div className="text-sm text-gray-600">Minutes</div>
              </div>
              <div className="rounded-xl border bg-gradient-to-b from-emerald-50 to-white p-4 text-center">
                <div className="mx-auto mb-2 flex h-10 w-10 items-center justify-center rounded-lg bg-emerald-100">
                  <FileText className="h-5 w-5 text-emerald-700" />
                </div>
                <div className="text-2xl font-bold text-gray-900">{exam.totalQuestions}</div>
                <div className="text-sm text-gray-600">Questions</div>
              </div>
              <div className="rounded-xl border bg-gradient-to-b from-amber-50 to-white p-4 text-center">
                <div className="mx-auto mb-2 flex h-10 w-10 items-center justify-center rounded-lg bg-amber-100">
                  <Award className="h-5 w-5 text-amber-700" />
                </div>
                <div className="text-2xl font-bold text-gray-900">{exam.totalMarks}</div>
                <div className="text-sm text-gray-600">Max Marks</div>
              </div>
              <div className="rounded-xl border bg-gradient-to-b from-purple-50 to-white p-4 text-center">
                <div className="mx-auto mb-2 flex h-10 w-10 items-center justify-center rounded-lg bg-purple-100">
                  <ShieldCheck className="h-5 w-5 text-purple-700" />
                </div>
                <div className="text-2xl font-bold text-gray-900">{exam.passingMarks}</div>
                <div className="text-sm text-gray-600">To Pass</div>
              </div>
            </div>

            {/* Instructions */}
            <div className="rounded-xl border bg-gradient-to-b from-gray-50 to-white p-6 mb-8">
              <h3 className="text-lg font-semibold text-gray-900">Instructions</h3>
              <div className="mt-2 whitespace-pre-wrap text-gray-700">
                {exam.instructions?.trim() ? exam.instructions : 'No instructions provided.'}
              </div>
            </div>

            {/* Actions */}
            <div className="text-center">{getActionButton()}</div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default ExamDetails