"use client";

import { useState, useEffect, useRef, useCallback } from "react";
import { usePara<PERSON>, useNavigate, useLocation, Link } from "react-router-dom";
import {
  Clock,
  AlertCircle,
  Flag,
  Loader2,
  HelpCircle,
  Shield,
  Monitor,
  Eye,
  AlertTriangle,
  CheckCircle2,
  Circle,
  Users,
  Calendar,
  Timer,
  X,
  Maximize,
  Minimize
} from "lucide-react";
import { API_ENDPOINTS, apiUtils } from "../config/api";
import { useAuth } from "../contexts/AuthContext";
import { getAuth } from "firebase/auth";
import QuestionText from "../utils/QuestionText";

interface Question {
  id: string;
  question: string;
  options: string[];
  correctAnswer: number | string;
  subject?: string;
  difficulty?: string;
  explanation?: string;
  marks?: number;
}

interface Exam {
  id: string;
  title: string;
  description?: string;
  duration: number; // in minutes
  totalQuestions: number;
  instructions?: string[];
  maxAttempts?: number;
  passingScore?: number;
  category?: string;
  difficulty?: string;
  createdAt?: string;
}

interface AnswerDetail {
  questionId: number;
  question: string;
  options: string[];
  correctAnswer: number;
  userAnswer?: string;
  isCorrect: boolean;
  isAttempted: boolean;
  marks: number;
  subject: string;
  difficulty: string;
}

interface AntiCheatWarning {
  type: 'tab-switch' | 'right-click' | 'keyboard-shortcut' | 'copy-paste' | 'fullscreen-exit';
  message: string;
  timestamp: Date;
  severity: 'low' | 'medium' | 'high';
}

function ExamRoom() {
  const { examId } = useParams<{ examId: string }>();
  const navigate = useNavigate();
  const location = useLocation();
  const { user, loading: authLoading } = useAuth();

  const searchParams = new URLSearchParams(location.search);
  const actualExamId = examId || searchParams.get("examId") || "1";

  // Exam state
  const [exam, setExam] = useState<Exam | null>(null);
  const [currentQuestion, setCurrentQuestion] = useState(0);
  const [selectedAnswers, setSelectedAnswers] = useState<{ [key: string]: string }>({});
  const [timeLeft, setTimeLeft] = useState(0);
  const [flaggedQuestions, setFlaggedQuestions] = useState<Set<number>>(new Set());
  const [showSubmitDialog, setShowSubmitDialog] = useState(false);
  const [examStartTime, setExamStartTime] = useState<Date | null>(null);
  const [questions, setQuestions] = useState<Question[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');

  // Anti-cheat state
  const [antiCheatWarnings, setAntiCheatWarnings] = useState<AntiCheatWarning[]>([]);
  const [tabSwitchCount, setTabSwitchCount] = useState(0);
  const [showAntiCheatWarning, setShowAntiCheatWarning] = useState(false);
  const [currentWarning, setCurrentWarning] = useState<AntiCheatWarning | null>(null);

  // Fullscreen state
  const [showFullscreenWarning, setShowFullscreenWarning] = useState(false);
  const [isFullscreen, setIsFullscreen] = useState(false);

  // Refs
  const hasInitialized = useRef(false);
  const questionsLoaded = useRef(false);
  const examLoaded = useRef(false);
  const timerRef = useRef<NodeJS.Timeout | null>(null);
  const pageVisibleRef = useRef(true);

  // Fullscreen functions
  const enterFullscreen = useCallback(() => {
    const element = document.documentElement;
    if (element.requestFullscreen) {
      element.requestFullscreen();
    } else if ((element as any).webkitRequestFullscreen) {
      (element as any).webkitRequestFullscreen();
    } else if ((element as any).msRequestFullscreen) {
      (element as any).msRequestFullscreen();
    }
  }, []);

  const exitFullscreen = useCallback(() => {
    if (document.exitFullscreen) {
      document.exitFullscreen();
    } else if ((document as any).webkitExitFullscreen) {
      (document as any).webkitExitFullscreen();
    } else if ((document as any).msExitFullscreen) {
      (document as any).msExitFullscreen();
    }
  }, []);

  const checkFullscreenStatus = useCallback(() => {
    const isCurrentlyFullscreen = !!(
      document.fullscreenElement ||
      (document as any).webkitFullscreenElement ||
      (document as any).msFullscreenElement
    );
    setIsFullscreen(isCurrentlyFullscreen);
    return isCurrentlyFullscreen;
  }, []);

  // Handle fullscreen change
  const handleFullscreenChange = useCallback(() => {
    const isCurrentlyFullscreen = checkFullscreenStatus();

    if (!isCurrentlyFullscreen && examStartTime) {
      // User exited fullscreen during exam
      addWarning('fullscreen-exit', 'Fullscreen mode exited during exam', 'high');
      setShowFullscreenWarning(true);
    }
  }, [examStartTime]);

  // Handle escape key
  const handleEscapeKey = useCallback((e: KeyboardEvent) => {
    if (e.key === 'Escape' && examStartTime) {
      e.preventDefault();
      if (isFullscreen) {
        // If in fullscreen and escape is pressed, show warning
        setShowFullscreenWarning(true);
      }
    }
  }, [examStartTime, isFullscreen]);

  // Auto fullscreen on component mount
  useEffect(() => {
    const initializeFullscreen = async () => {
      try {
        await enterFullscreen();
        setIsFullscreen(true);
      } catch (error) {
        console.log("Fullscreen request failed:", error);
        setShowFullscreenWarning(true);
      }
    };

    // Initialize fullscreen after a short delay
    const timer = setTimeout(() => {
      initializeFullscreen();
    }, 500);

    return () => clearTimeout(timer);
  }, [enterFullscreen]);

  // Setup fullscreen event listeners
  useEffect(() => {
    document.addEventListener('fullscreenchange', handleFullscreenChange);
    document.addEventListener('webkitfullscreenchange', handleFullscreenChange);
    document.addEventListener('msfullscreenchange', handleFullscreenChange);
    document.addEventListener('keydown', handleEscapeKey);

    return () => {
      document.removeEventListener('fullscreenchange', handleFullscreenChange);
      document.removeEventListener('webkitfullscreenchange', handleFullscreenChange);
      document.removeEventListener('msfullscreenchange', handleFullscreenChange);
      document.removeEventListener('keydown', handleEscapeKey);
    };
  }, [handleFullscreenChange, handleEscapeKey]);

  // Handle fullscreen actions
  const handleEnterFullscreen = () => {
    enterFullscreen();
    setShowFullscreenWarning(false);
  };

  const handleSubmitFromFullscreen = () => {
    setShowFullscreenWarning(false);
    setShowSubmitDialog(true);
  };

  // Anti-cheat functions
  const addWarning = useCallback((type: AntiCheatWarning['type'], message: string, severity: AntiCheatWarning['severity'] = 'medium') => {
    const warning: AntiCheatWarning = {
      type,
      message,
      timestamp: new Date(),
      severity
    };

    setAntiCheatWarnings(prev => [...prev, warning]);
    setCurrentWarning(warning);
    setShowAntiCheatWarning(true);

    // Auto hide warning after 4 seconds
    setTimeout(() => {
      setShowAntiCheatWarning(false);
      setCurrentWarning(null);
    }, 4000);
  }, []);

  // Disable right-click context menu
  const disableContextMenu = useCallback((e: MouseEvent) => {
    e.preventDefault();
    addWarning('right-click', 'Right-click is disabled during exam', 'low');
  }, [addWarning]);

  // Disable keyboard shortcuts
  const disableKeyboardShortcuts = useCallback((e: KeyboardEvent) => {
    // Disable F12, Ctrl+Shift+I, Ctrl+Shift+J, Ctrl+U, Ctrl+S, Ctrl+A, Ctrl+C, Ctrl+V, Alt+Tab
    const forbiddenKeys = [
      { key: 'F12' },
      { key: 'I', ctrlKey: true, shiftKey: true },
      { key: 'J', ctrlKey: true, shiftKey: true },
      { key: 'U', ctrlKey: true },
      { key: 'S', ctrlKey: true },
      { key: 'A', ctrlKey: true },
      { key: 'C', ctrlKey: true },
      { key: 'V', ctrlKey: true },
      { key: 'R', ctrlKey: true },
      { key: 'F5' },
      { key: 'Tab', altKey: true }
    ];

    for (const forbidden of forbiddenKeys) {
      if (
        e.key === forbidden.key &&
        (forbidden.ctrlKey ? e.ctrlKey : true) &&
        (forbidden.shiftKey ? e.shiftKey : true) &&
        (forbidden.altKey ? e.altKey : true)
      ) {
        e.preventDefault();
        addWarning('keyboard-shortcut', `${e.key} shortcut is disabled during exam`, 'high');
        return;
      }
    }
  }, [addWarning]);

  // Handle tab switching/window blur
  const handleVisibilityChange = useCallback(() => {
    if (document.hidden && examStartTime) {
      pageVisibleRef.current = false;
      setTabSwitchCount(prev => prev + 1);
      addWarning('tab-switch', `Tab switching detected (${tabSwitchCount + 1} times)`, 'high');
    } else {
      pageVisibleRef.current = true;
    }
  }, [addWarning, examStartTime, tabSwitchCount]);

  // Setup anti-cheat measures
  useEffect(() => {
    if (!examStartTime) return;

    // Add event listeners
    document.addEventListener('contextmenu', disableContextMenu);
    document.addEventListener('keydown', disableKeyboardShortcuts);
    document.addEventListener('visibilitychange', handleVisibilityChange);

    // Disable text selection
    document.body.style.userSelect = 'none';
    document.body.style.webkitUserSelect = 'none';
    document.body.style.msUserSelect = 'none';

    // Disable drag
    document.body.style.webkitUserDrag = 'none';

    return () => {
      // Cleanup event listeners
      document.removeEventListener('contextmenu', disableContextMenu);
      document.removeEventListener('keydown', disableKeyboardShortcuts);
      document.removeEventListener('visibilitychange', handleVisibilityChange);

      // Re-enable text selection
      document.body.style.userSelect = '';
      document.body.style.webkitUserSelect = '';
      document.body.style.msUserSelect = '';
      document.body.style.webkitUserDrag = '';
    };
  }, [examStartTime, disableContextMenu, disableKeyboardShortcuts, handleVisibilityChange]);

  // Initialize exam
  useEffect(() => {
    if (hasInitialized.current || authLoading) return;
    hasInitialized.current = true;

    console.log("ExamRoom initializing, examId:", actualExamId);
    if (!user && actualExamId === "1") {
      console.log("Guest user accessing demo exam");
    }
    setExamStartTime(new Date());

    if (!examLoaded.current) {
      fetchExamDetails(actualExamId);
    }
    if (!questionsLoaded.current) {
      fetchQuestions(actualExamId);
    }
  }, [authLoading, user, actualExamId]);

  // Timer effect
  useEffect(() => {
    // Exit if the exam data isn't loaded yet
    if (loading || questions.length === 0 || !exam) return;

    // 1. Get duration in minutes from the exam object. Default to 0 if not provided.
    const durationInMinutes = exam.duration || 0;

    // 2. Convert the duration from minutes to seconds.
    const durationInSeconds = durationInMinutes * 60;

    console.log(`Setting exam timer. Duration from API: ${durationInMinutes} minutes (${durationInSeconds} seconds).`);

    // 3. Set the initial time left in seconds.
    setTimeLeft(durationInSeconds);

    // 4. Start the countdown interval.
    timerRef.current = setInterval(() => {
      setTimeLeft((prev) => {
        // When the timer reaches zero, auto-submit the exam.
        if (prev <= 1) {
          clearInterval(timerRef.current!);
          handleSubmit(); // Make sure handleSubmit is wrapped in useCallback or defined outside
          return 0;
        }
        // Decrement the timer by 1 second.
        return prev - 1;
      });
    }, 1000);

    // Cleanup function to clear the interval when the component unmounts.
    return () => {
      if (timerRef.current) {
        clearInterval(timerRef.current);
      }
    };
  }, [loading, questions, exam]); // Dependencies: This effect runs when the exam data is ready.

  const getAuthHeaders = async (): Promise<HeadersInit> => {
    const headers: HeadersInit = {
      "Content-Type": "application/json",
      Accept: "application/json",
    };
    try {
      const auth = getAuth();
      const user = auth.currentUser;
      if (user) {
        const token = await user.getIdToken();
        headers["Authorization"] = `Bearer ${token}`;
        console.log("Added Firebase token to request headers");
      } else {
        console.warn("No authenticated user found for API request");
      }
    } catch (error) {
      console.error("Error getting Firebase token:", error);
    }
    return headers;
  };

  const fetchExamDetails = async (id: string) => {
    if (examLoaded.current) {
      console.log("Exam details already loaded, skipping API call");
      return;
    }

    try {
      setError('');
      examLoaded.current = true;
      console.log("Fetching exam details for examId:", id);

      // Use fetch directly to ensure a fresh response
      const headers = await getAuthHeaders();
      const response = await fetch(`${API_ENDPOINTS.EXAMS}/${id}`, {
        method: 'GET',
        headers,
      });

      console.log("Response received, bodyUsed:", response.bodyUsed);
      if (response.bodyUsed) {
        throw new Error("Response body already consumed before JSON parsing.");
      }

      if (response.ok) {
        const examData: Exam = await response.json();
        console.log("Exam details received:", examData);
        setExam(examData);
      } else {
        throw new Error(`Failed to fetch exam details: ${response.status} ${response.statusText}`);
      }
    } catch (err: any) {
      console.error("Error fetching exam details:", err);
      setError(err.message || "Failed to load exam details.");
    }
  };

  const fetchQuestions = async (examId: string) => {
    if (questionsLoaded.current) {
      console.log("Questions already loaded, skipping API call");
      return;
    }
    try {
      setLoading(true);
      setError('');
      questionsLoaded.current = true;
      console.log("Fetching questions for examId:", examId);

      const response = await apiUtils.get(`${API_ENDPOINTS.QUESTIONS}?examId=${examId}`);
      if (response.ok) {
        const apiQuestions: Question[] = await response.json();
        console.log("API questions received:", apiQuestions);
        setQuestions(apiQuestions);
      } else {
        throw new Error("Failed to fetch questions from the server.");
      }
    } catch (err: any) {
      console.error("Error fetching questions:", err);
      setError(err.message || "A network error occurred.");
    } finally {
      setLoading(false);
    }
  };

  const formatTime = (seconds: number): string => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = seconds % 60;
    return `${hours.toString().padStart(2, "0")}:${minutes.toString().padStart(2, "0")}:${secs.toString().padStart(2, "0")}`;
  };

  const handleAnswerSelect = (questionId: string, optionText: string) => {
    setSelectedAnswers((prev) => ({
      ...prev,
      [questionId]: optionText,
    }));
  };

  const handleFlagQuestion = () => {
    setFlaggedQuestions((prev) => {
      const newSet = new Set(prev);
      if (newSet.has(currentQuestion)) {
        newSet.delete(currentQuestion);
      } else {
        newSet.add(currentQuestion);
      }
      return newSet;
    });
  };

  const handleSubmit = async () => {
    if (timerRef.current) {
      clearInterval(timerRef.current);
    }

    // Exit fullscreen on submit
    if (isFullscreen) {
      exitFullscreen();
    }

    if (!user || !actualExamId) {
      setError("Cannot submit: User not logged in or exam ID is missing.");
      return;
    }

    setLoading(true);
    setError('');

    const examEndTime = new Date();
    const timeTakenMs = examStartTime ? examEndTime.getTime() - examStartTime.getTime() : 0;
    const timeTakenMinutes = Math.round(timeTakenMs / (1000 * 60));

    // Prepare detailed answers with question data
    const detailedAnswers = questions.map(question => ({
      questionId: question.id,
      question: question.question,
      options: question.options,
      correctAnswer: typeof question.correctAnswer === 'number' ? question.correctAnswer : question.options.indexOf(question.correctAnswer),
      userAnswer: selectedAnswers[question.id] || null,
      isCorrect: selectedAnswers[question.id] === (typeof question.correctAnswer === 'number' ? question.options[question.correctAnswer] : question.correctAnswer),
      isAttempted: selectedAnswers[question.id] !== undefined,
      subject: question.subject || 'General',
      difficulty: question.difficulty || 'Medium'
    }));

    const submissionPayload = {
      examId: actualExamId,
      userId: user.uid,
      timeTakenMinutes: timeTakenMinutes.toString(),
      answers: selectedAnswers,
      detailedAnswers: detailedAnswers,
      examTitle: exam?.title || 'Unknown Exam',
      examDuration: exam?.duration || 0,
      antiCheatWarnings: antiCheatWarnings,
      tabSwitchCount: tabSwitchCount
    };

    try {
      console.log("Submitting exam results:", submissionPayload);
      const response = await apiUtils.post(API_ENDPOINTS.EXAM_SUBMIT, submissionPayload);
      const savedResult = await response.json();

      if (!response.ok) {
        throw new Error(savedResult.message || 'Failed to submit exam results.');
      }

      console.log("Results saved successfully:", savedResult);
      if (savedResult && savedResult.id) {
        // Navigate with additional data for comprehensive PDF generation
        navigate(`/app/result/${savedResult.id}`, {
          state: {
            questions: questions,
            submissionPayload: submissionPayload,
            submissionResponse: savedResult
          }
        });
      } else {
        throw new Error("Submission successful, but did not receive a result ID.");
      }
    } catch (error: any) {
      console.error("Error submitting exam results:", error);
      setError(error.message || "An error occurred while submitting.");
      setLoading(false);
    }
  };

  const getQuestionStatus = (questionId: string, index: number): string => {
    if (selectedAnswers[questionId] !== undefined) {
      return "answered";
    } else if (flaggedQuestions.has(index)) {
      return "flagged";
    } else {
      return "not-answered";
    }
  };

  const getStatusColor = (status: string): string => {
    switch (status) {
      case "answered":
        return "bg-emerald-500 text-white border-emerald-500";
      case "flagged":
        return "bg-amber-500 text-white border-amber-500";
      default:
        return "bg-white text-gray-600 border-gray-200 hover:border-gray-300";
    }
  };

  const getProgressPercentage = (): number => {
    return Math.round((Object.keys(selectedAnswers).length / questions.length) * 100);
  };

  if (authLoading || loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center bg-white rounded-xl shadow-lg p-6 max-w-sm w-full mx-4">
          <div className="animate-spin h-12 w-12 border-4 border-blue-200 border-t-blue-600 rounded-full mx-auto mb-4"></div>
          <h3 className="text-lg font-semibold text-gray-900 mb-2">
            {authLoading ? "Authenticating..." : "Loading Exam..."}
          </h3>
          <p className="text-gray-600 text-sm">Please wait while we prepare your exam</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center p-4">
        <div className="text-center bg-white rounded-xl shadow-lg p-6 max-w-sm w-full">
          <div className="w-12 h-12 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <HelpCircle className="h-6 w-6 text-red-600" />
          </div>
          <h3 className="text-lg font-semibold text-gray-900 mb-2">Error Loading Exam</h3>
          <p className="text-gray-600 mb-4 text-sm">{error}</p>
          <Link
            to="/app/exams"
            className="inline-flex items-center justify-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors text-sm font-medium"
          >
            ← Back to Exams
          </Link>
        </div>
      </div>
    );
  }

  const currentQ = questions[currentQuestion];
  if (!currentQ || !exam) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center bg-white rounded-xl shadow-lg p-6 max-w-sm w-full mx-4">
          <div className="w-12 h-12 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <AlertCircle className="h-6 w-6 text-gray-600" />
          </div>
          <h3 className="text-lg font-semibold text-gray-900 mb-2">No Questions Available</h3>
          <p className="text-gray-600 mb-4 text-sm">No questions found for this exam.</p>
          <Link
            to="/app/exams"
            className="inline-flex items-center justify-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors text-sm font-medium"
          >
            Back to Exams
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Fullscreen Warning Dialog */}
      {showFullscreenWarning && (
        <div className="fixed inset-0 bg-black/70 backdrop-blur-sm flex items-center justify-center z-50">
          <div className="bg-white rounded-xl p-6 max-w-md mx-4 shadow-2xl border-2 border-red-200">
            <div className="text-center mb-6">
              <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <Maximize className="h-8 w-8 text-red-600" />
              </div>
              <h3 className="text-xl font-bold text-gray-900 mb-2">🔒 Fullscreen Mode Required</h3>
              <p className="text-gray-600">This exam must be taken in fullscreen mode to prevent cheating. Please enter fullscreen mode to continue.</p>
            </div>

            <div className="flex space-x-3">
              <button
                onClick={handleEnterFullscreen}
                className="flex-1 px-4 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors font-medium flex items-center justify-center space-x-2"
              >
                <Maximize className="h-4 w-4" />
                <span>Enter Fullscreen</span>
              </button>
              <button
                onClick={handleSubmitFromFullscreen}
                className="flex-1 px-4 py-3 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors font-medium shadow-lg shadow-red-200"
              >
                Submit Exam
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Anti-cheat warning overlay */}
      {showAntiCheatWarning && currentWarning && (
        <div className="fixed inset-0 bg-black/60 backdrop-blur-sm flex items-center justify-center z-50">
          <div className={`rounded-xl shadow-2xl border-l-4 p-6 max-w-md mx-4 animate-bounce ${currentWarning.severity === 'high' ? 'bg-red-50 border-red-500' :
            currentWarning.severity === 'medium' ? 'bg-yellow-50 border-yellow-500' :
              'bg-blue-50 border-blue-500'
            }`}>
            <div className="flex items-start justify-between">
              <div className="flex items-start">
                <div className="flex-shrink-0 mr-3">
                  {currentWarning.severity === 'high' ? (
                    <AlertTriangle className="h-8 w-8 text-red-600" />
                  ) : currentWarning.severity === 'medium' ? (
                    <AlertCircle className="h-8 w-8 text-yellow-600" />
                  ) : (
                    <Eye className="h-8 w-8 text-blue-600" />
                  )}
                </div>
                <div>
                  <h3 className={`text-lg font-bold mb-1 ${currentWarning.severity === 'high' ? 'text-red-800' :
                    currentWarning.severity === 'medium' ? 'text-yellow-800' :
                      'text-blue-800'
                    }`}>
                    ⚠️ SECURITY ALERT
                  </h3>
                  <p className={`text-base font-medium ${currentWarning.severity === 'high' ? 'text-red-700' :
                    currentWarning.severity === 'medium' ? 'text-yellow-700' :
                      'text-blue-700'
                    }`}>
                    {currentWarning.message}
                  </p>
                </div>
              </div>
              <button
                onClick={() => setShowAntiCheatWarning(false)}
                className="text-gray-400 hover:text-gray-600"
              >
                <X className="h-5 w-5" />
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Header - Keep unchanged */}
      <div className="bg-white shadow-sm border-b sticky top-0 z-40">
        <div className="max-w-7xl mx-auto px-4">
          <div className="flex justify-between items-center h-14">
            <div className="flex items-center space-x-3">
              <div className="flex items-center space-x-2">
                <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                <span className="text-sm text-green-600 font-medium">Live</span>
              </div>
              <div className="h-4 w-px bg-gray-200"></div>
              <div>
                <h1 className="text-base font-semibold text-gray-900">{exam.title}</h1>
                <p className="text-xs text-gray-600">
                  Q{currentQuestion + 1}/{questions.length} • {getProgressPercentage()}%
                </p>
              </div>
            </div>

            <div className="flex items-center space-x-3">
              {tabSwitchCount > 0 && (
                <div className="flex items-center space-x-1 px-2 py-1 rounded-md text-xs font-medium bg-red-100 text-red-700">
                  <AlertTriangle className="h-3 w-3" />
                  <span>{tabSwitchCount}</span>
                </div>
              )}

              {/* Timer */}
              <div className="flex items-center space-x-2 bg-red-50 px-3 py-2 rounded-lg border border-red-100">
                <Clock className="h-4 w-4 text-red-600" />
                <span className="font-mono text-sm font-bold text-red-600">
                  {formatTime(timeLeft)}
                </span>
              </div>

              <button
                onClick={() => setShowSubmitDialog(true)}
                className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors text-sm font-medium"
              >
                Submit
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content - Improved Layout */}
      <div className="max-w-7xl mx-auto px-4 py-6">
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
          {/* Question Section - 3/4 width, no background card */}
          <div className="lg:col-span-3">
            {/* Question Header */}
            <div className="flex items-center justify-between mb-6">
              <div className="flex items-center space-x-3">
                <div className="bg-blue-600 text-white rounded-full w-10 h-10 flex items-center justify-center font-bold text-lg">
                  {currentQuestion + 1}
                </div>
                <div>
                  <h2 className="text-xl font-bold text-gray-900">Question {currentQuestion + 1}</h2>
                  {currentQ.subject && <p className="text-sm text-blue-600 font-medium">{currentQ.subject}</p>}
                </div>
              </div>

              <button
                onClick={handleFlagQuestion}
                className={`flex items-center space-x-2 px-4 py-2 rounded-lg font-medium transition-all ${flaggedQuestions.has(currentQuestion)
                  ? "bg-amber-100 text-amber-800 border border-amber-200"
                  : "bg-gray-100 text-gray-600 hover:bg-gray-200"
                  }`}
              >
                <Flag className="h-4 w-4" />
                <span>{flaggedQuestions.has(currentQuestion) ? "Flagged" : "Flag"}</span>
              </button>
            </div>

            {/* Question Text - Better spacing for long questions */}
            <div className="mb-8 border-l-4 border-blue-500 pl-6">
              <QuestionText
                text={currentQ.question}
                paragraphClassName="text-lg text-gray-900 leading-relaxed font-medium"
                addLineMarkers={true}            // default true
                markerType="bullet"              // "bullet" | "dash" | "star" | "none"
              />
            </div>




            {/* Options - Improved design with green glow */}
            <div className="space-y-4 mb-8">
              {currentQ.options.map((option, index) => {
                const optionLabel = String.fromCharCode(65 + index);
                const isSelected = selectedAnswers[currentQ.id] === option;

                return (
                  <label
                    key={index}
                    className={`flex items-center p-4 border-2 rounded-xl cursor-pointer transition-all duration-200 hover:border-blue-300 hover:bg-blue-50/30 ${isSelected ? "border-green-400 bg-green-50/50" : "border-gray-200"
                      }`}
                  >
                    <div className={`flex-shrink-0 w-12 h-12 rounded-full border-2 mr-4 flex items-center justify-center text-lg font-bold transition-all duration-300 ${isSelected
                      ? "bg-green-500 border-green-500 text-white shadow-lg shadow-green-300 scale-110 animate-pulse"
                      : "border-gray-300 text-gray-600 bg-white hover:border-blue-400 hover:text-blue-600"
                      }`}>
                      {optionLabel}
                    </div>
                    <span className="text-gray-900 font-medium text-lg leading-relaxed flex-1">{option}</span>
                    <input
                      type="radio"
                      name={`answer-${currentQ.id}`}
                      value={option}
                      checked={isSelected}
                      onChange={(e) => handleAnswerSelect(currentQ.id, e.target.value)}
                      className="sr-only"
                    />
                  </label>
                );
              })}
            </div>

            {/* Navigation - Better responsive design */}
            <div className="flex justify-between items-center pt-6 border-t border-gray-200">
              <button
                onClick={() => setCurrentQuestion(Math.max(0, currentQuestion - 1))}
                disabled={currentQuestion === 0}
                className="flex items-center space-x-2 px-6 py-3 border-2 border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 hover:border-gray-400 disabled:opacity-50 disabled:cursor-not-allowed transition-all font-medium min-w-[120px] justify-center"
              >
                <span>← Previous</span>
              </button>

              <div className="hidden sm:flex items-center space-x-4">
                <span className="text-sm text-gray-500 font-medium">{currentQuestion + 1} of {questions.length}</span>
                <div className="w-32 bg-gray-200 rounded-full h-2">
                  <div
                    className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                    style={{ width: `${((currentQuestion + 1) / questions.length) * 100}%` }}
                  ></div>
                </div>
              </div>

              <button
                onClick={() => setCurrentQuestion(Math.min(questions.length - 1, currentQuestion + 1))}
                disabled={currentQuestion === questions.length - 1}
                className="flex items-center space-x-2 px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-all font-medium min-w-[120px] justify-center"
              >
                <span>Next →</span>
              </button>
            </div>
          </div>

          {/* Right Sidebar - 1/4 width */}
          <div className="space-y-4">
            {/* Questions Navigation */}
            <div className="bg-white rounded-lg border p-4 sticky top-20">
              <h3 className="text-sm font-bold text-gray-900 mb-4">Questions</h3>

              <div className="grid grid-cols-5 gap-2 mb-4">
                {questions.map((q, index) => {
                  const status = getQuestionStatus(q.id, index);
                  const isActive = index === currentQuestion;

                  return (
                    <button
                      key={index}
                      onClick={() => setCurrentQuestion(index)}
                      className={`w-10 h-10 rounded-lg text-sm font-bold transition-all border-2 ${getStatusColor(status)} ${isActive ? "ring-2 ring-blue-400 ring-offset-1 scale-105" : "hover:scale-105"
                        }`}
                    >
                      {index + 1}
                    </button>
                  );
                })}
              </div>

              {/* Status Summary */}
              <div className="space-y-2 text-sm border-t pt-4">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <div className="w-3 h-3 bg-emerald-500 rounded"></div>
                    <span className="text-gray-700">Answered</span>
                  </div>
                  <span className="font-bold text-emerald-600">{Object.keys(selectedAnswers).length}</span>
                </div>
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <div className="w-3 h-3 bg-amber-500 rounded"></div>
                    <span className="text-gray-700">Flagged</span>
                  </div>
                  <span className="font-bold text-amber-600">{flaggedQuestions.size}</span>
                </div>
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <div className="w-3 h-3 bg-gray-300 rounded"></div>
                    <span className="text-gray-700">Remaining</span>
                  </div>
                  <span className="font-bold text-gray-600">{questions.length - Object.keys(selectedAnswers).length}</span>
                </div>
              </div>
            </div>

            {/* Exam Info - Hidden on mobile */}
            <div className="hidden lg:block bg-blue-50 rounded-lg border border-blue-200 p-4">
              <h3 className="text-sm font-bold text-gray-900 mb-3 flex items-center space-x-2">
                <Users className="h-4 w-4 text-blue-600" />
                <span>Exam Info</span>
              </h3>

              <div className="space-y-2 text-sm">
                <div className="flex items-center justify-between">
                  <span className="text-gray-600">Total Questions</span>
                  <span className="font-bold">{questions.length}</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-gray-600">Time Left</span>
                  <span className="font-bold text-red-600">{Math.round(timeLeft / 60)}m</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-gray-600">Progress</span>
                  <span className="font-bold text-blue-600">{getProgressPercentage()}%</span>
                </div>
              </div>
            </div>

            {/* Security Info - Hidden on mobile */}
            <div className="hidden lg:block bg-yellow-50 rounded-lg border border-yellow-200 p-4">
              <div className="flex items-center space-x-2 mb-3">
                <Shield className="h-4 w-4 text-yellow-600" />
                <span className="text-sm font-bold text-gray-900">Security</span>
              </div>
              <ul className="text-xs text-yellow-800 space-y-1">
                <li>• Fullscreen mode enforced</li>
                <li>• Tab switching monitored</li>
                <li>• Right-click disabled</li>
                <li>• Auto-submit on timeout</li>
              </ul>

              {antiCheatWarnings.length > 0 && (
                <div className="mt-3 pt-2 border-t border-yellow-200">
                  <p className="text-xs font-bold text-red-600">
                    Violations: {antiCheatWarnings.length}
                  </p>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Submit Dialog */}
      {showSubmitDialog && (
        <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-xl p-6 max-w-md w-full shadow-2xl">
            <div className="text-center mb-6">
              <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <CheckCircle2 className="h-8 w-8 text-blue-600" />
              </div>
              <h3 className="text-xl font-bold text-gray-900 mb-2">Submit Exam?</h3>
              <p className="text-gray-600">This action cannot be undone.</p>
            </div>

            <div className="bg-gray-50 rounded-lg p-4 mb-6">
              <div className="grid grid-cols-2 gap-4">
                <div className="text-center">
                  <p className="text-gray-500 text-sm mb-1">Answered</p>
                  <p className="text-2xl font-bold text-emerald-600">{Object.keys(selectedAnswers).length}</p>
                </div>
                <div className="text-center">
                  <p className="text-gray-500 text-sm mb-1">Remaining</p>
                  <p className="text-2xl font-bold text-gray-600">{questions.length - Object.keys(selectedAnswers).length}</p>
                </div>
              </div>
            </div>

            <div className="flex space-x-3">
              <button
                onClick={() => setShowSubmitDialog(false)}
                className="flex-1 px-6 py-3 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors font-medium"
              >
                Cancel
              </button>
              <button
                onClick={handleSubmit}
                className="flex-1 px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors font-medium"
              >
                Submit
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}

export default ExamRoom;
