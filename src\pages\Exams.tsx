import React, { useState, useEffect, useRef } from "react"
import { Link, useLocation } from "react-router-dom"
import { Clock, Users, Calendar, Filter, Search, FileText, Award, Eye, ShoppingCart, CheckCircle, AlertCircle, User, Mail, Phone, X, ShieldCheck, CreditCard } from 'lucide-react'
import { API_ENDPOINTS, API_CONFIG, apiUtils } from "../config/api"
import moment from "moment-timezone"
import { useAuth } from "../contexts/AuthContext"
import { PaymentService } from "../services/paymentService"
import { PAYMENT_CONFIG } from "../config/payment"

interface Exam {
  id: string
  title: string
  description: string
  duration: number
  totalQuestions: number
  attempts: number
  difficulty: string
  status: string
  startTime: string
  endTime: string
  category: string
  score?: number
  isPremium: number
  basePrice?: number
  offerPrice?: number
  subject?: string
}

interface Purchase {
  id: string
  userId: string
  examId: string
  amount: number
  paymentId: string
  orderId: string
  status: string
  purchaseDate: string
  paymentMethod: string
  gateway?: string
}

interface UserProfileMinimal {
  userId: string | null
  name: string | null
  email: string | null
  phone: string | null
}

// Initialize PaymentService
const paymentService = new PaymentService(PAYMENT_CONFIG)

function Exams() {
  const { user } = useAuth()
  const location = useLocation()

  const [exams, setExams] = useState<Exam[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState("")
  const [filter, setFilter] = useState<"all" | "live" | "upcoming" | "completed">("all")
  const [searchTerm, setSearchTerm] = useState("")

  // Track purchased exams robustly irrespective of API shape
  const [purchasedExamIds, setPurchasedExamIds] = useState<Set<string>>(new Set())
  const [userPurchases, setUserPurchases] = useState<Purchase[]>([])

  const [paymentLoading, setPaymentLoading] = useState<string | null>(null)
  const [paymentError, setPaymentError] = useState("")

  const [showProfileModal, setShowProfileModal] = useState(false)
  const [profileData, setProfileData] = useState<UserProfileMinimal>({
    userId: null,
    name: null,
    email: null,
    phone: null,
  })
  const [profileLoading, setProfileLoading] = useState(false)
  const [profileError, setProfileError] = useState("")
  const [successAlert, setSuccessAlert] = useState("")
  const [profileFetched, setProfileFetched] = useState(false)
  const [profileValid, setProfileValid] = useState(false)
  const [pendingExam, setPendingExam] = useState<Exam | null>(null)

  const purchasesFetchedRef = useRef(false)
  const profileFetchedRef = useRef(false)

  useEffect(() => {
    fetchExams()
  }, [])

  useEffect(() => {
    if (user && !purchasesFetchedRef.current) {
      purchasesFetchedRef.current = true
      fetchUserPurchases()
    }
  }, [user])

  useEffect(() => {
  console.log("🔍 Profile fetch useEffect (Exams)", {
    user: !!user,
    profileFetched: profileFetched,
    profileFetchedRef: profileFetchedRef.current,
  });
  if (user && !profileFetchedRef.current && !profileFetched) {
    profileFetchedRef.current = true;
    checkUserProfileOnPageLoad();
  }
}, [user]);

  // Re-fetch purchases when navigating back to Exams page
  useEffect(() => {
    if (user && (location as any).state?.refreshPurchases) {
      fetchUserPurchases()
    }
  }, [location, user])

  useEffect(() => {
    if (successAlert) {
      const timer = setTimeout(() => setSuccessAlert(""), 3000)
      return () => clearTimeout(timer)
    }
  }, [successAlert])

  const fetchExams = async () => {
    try {
      setLoading(true)
      const response = await apiUtils.get(API_ENDPOINTS.EXAMS)
      if (response.ok) {
        setExams(await response.json())
      } else {
        setError("Failed to fetch exams")
      }
    } catch (err) {
      setError("Network error. Please try again.")
    } finally {
      setLoading(false)
    }
  }

  // Normalize /exams/me which can return either:
  // - purchases with examId, or
  // - a list of exams (with id), or
  // - a single object
  const fetchUserPurchases = async () => {
    if (!user) return
    try {
      const response = await apiUtils.get(`${API_CONFIG.API_BASE_URL}/exams/me`)
      if (response.ok) {
        const raw = await response.json()
        const list: any[] = Array.isArray(raw) ? raw : raw ? [raw] : []

        const ids = new Set<string>()
        const normalizedPurchases: Purchase[] = []

        for (const item of list) {
          if (!item || typeof item !== "object") continue
          // If the API returns purchase objects
          if ("examId" in item && typeof item.examId === "string") {
            ids.add(String(item.examId))
            normalizedPurchases.push({
              id: item.id ?? `purchase_${item.examId}`,
              userId: user.id ?? "",
              examId: String(item.examId),
              amount: item.amount ?? 0,
              paymentId: item.paymentId ?? "",
              orderId: item.orderId ?? "",
              status: item.status ?? "completed",
              purchaseDate: item.purchaseDate ?? new Date().toISOString(),
              paymentMethod: item.paymentMethod ?? "unknown",
              gateway: item.gateway ?? undefined,
            })
          }
          // If the API returns exam objects directly
          else if ("id" in item && typeof item.id === "string") {
            ids.add(String(item.id))
            // We won't fabricate full purchase details; just count via ids
          }
        }

        setPurchasedExamIds(ids)
        setUserPurchases(normalizedPurchases)
      } else {
        // No purchases found or API call failed, set empty
        setPurchasedExamIds(new Set())
        setUserPurchases([])
      }
    } catch (error) {
      console.error("Error fetching user purchases:", error)
      setPurchasedExamIds(new Set())
      setUserPurchases([])
    }
  }

  const fetchUserProfile = async (): Promise<UserProfileMinimal> => {
  if (!user) {
    throw new Error("User not authenticated");
  }

  // Check cache
  const cachedProfile = localStorage.getItem(`profile_${user.id}`);
  if (cachedProfile) {
    console.log("📦 Using cached profile:", JSON.parse(cachedProfile));
    return JSON.parse(cachedProfile);
  }

  // Check lock to prevent concurrent fetches
  if (localStorage.getItem(`profile_fetching_${user.id}`)) {
    console.log("⏳ Profile fetch already in progress");
    return new Promise((resolve) => {
      const check = () => {
        const profile = localStorage.getItem(`profile_${user.id}`);
        if (profile) {
          resolve(JSON.parse(profile));
        } else {
          setTimeout(check, 100);
        }
      };
      check();
    });
  }

  // Set lock
  localStorage.setItem(`profile_fetching_${user.id}`, "true");
  try {
    console.log("📡 Fetching user profile from API...");
    const response = await apiUtils.get(`${API_CONFIG.API_BASE_URL}/userProfiles/me/minimal`);

    // Read response body once
    let profileData = null;
    if (response.headers.get("content-type")?.includes("application/json")) {
      profileData = await response.json();
    } else {
      const text = await response.text();
      console.error("Unexpected response format:", text);
    }

    if (!response.ok) {
      if (response.status === 404) {
        console.log("❌ No profile found, returning empty profile");
        const emptyProfile = { userId: user.id || null, name: null, email: null, phone: null };
        localStorage.setItem(`profile_${user.id}`, JSON.stringify(emptyProfile));
        return emptyProfile;
      } else if (response.status === 401) {
        throw new Error("Authentication required. Please login again.");
      } else {
        throw new Error(`Failed to fetch profile data: ${response.statusText}`);
      }
    }

    const profile = {
      userId: user.id || null,
      name: profileData.name || null,
      email: profileData.email || null,
      phone: profileData.phone || null,
    };
    localStorage.setItem(`profile_${user.id}`, JSON.stringify(profile));
    console.log("✅ Profile fetched and cached:", profile);
    return profile;
  } catch (error: any) {
    console.error("❌ Error fetching user profile:", error);
    throw new Error("Network error fetching profile. Please try again.");
  } finally {
    localStorage.removeItem(`profile_fetching_${user.id}`); // Release lock
  }
};

  const validateProfile = (profile: UserProfileMinimal): boolean => {
    return !!(profile.name?.trim() && profile.email?.trim() && profile.phone?.trim())
  }

  const checkUserProfileOnPageLoad = async () => {
    if (!user || profileFetched) return
    try {
      const profile = await fetchUserProfile()
      const isValid = validateProfile(profile)
      setProfileData(profile)
      setProfileFetched(true)
      setProfileValid(isValid)
      if (!isValid) setShowProfileModal(true)
    } catch (_error: any) {
      setProfileFetched(true)
      setProfileValid(false)
      setShowProfileModal(true)
    }
  }

 const updateUserProfile = async (updatedProfile: UserProfileMinimal): Promise<void> => {
  if (!user) {
    throw new Error("User not authenticated");
  }

  try {
    console.log("📝 Updating user profile:", updatedProfile);
    const response = await apiUtils.patch(`${API_CONFIG.API_BASE_URL}/userProfiles/me`, {
      name: updatedProfile.name,
      email: updatedProfile.email,
      phone: updatedProfile.phone,
    });

    if (!response.ok) {
      throw new Error(`Failed to update profile: ${response.statusText}`);
    }

    console.log("✅ Profile updated successfully");
    setProfileData(updatedProfile);
    setProfileValid(validateProfile(updatedProfile));
    localStorage.setItem(`profile_${user.id}`, JSON.stringify(updatedProfile)); // Update cache
  } catch (error: any) {
    console.error("❌ Error updating user profile:", error);
    throw new Error("Failed to update profile. Please try again.");
  }
};
  const handleProfileFormSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault()
    setProfileLoading(true)
    setProfileError("")
    try {
      await updateUserProfile(profileData)
      setShowProfileModal(false)
      setSuccessAlert("Profile updated successfully!")
      if (pendingExam) {
        setTimeout(() => {
          initiatePaymentWithProfile(pendingExam, profileData)
        }, 500)
      }
    } catch (error: any) {
      setProfileError(error.message || "Failed to update profile")
    } finally {
      setProfileLoading(false)
    }
  }

  const generateRandomOrderId = (): string => {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789'
    let result = 'order_'
    for (let i = 0; i < 14; i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length))
    }
    return result
  }

  const generateRandomPaymentId = (): string => {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789'
    let result = 'pay_'
    for (let i = 0; i < 14; i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length))
    }
    return result
  }

  const handleFreePurchase = async (exam: Exam) => {
    setPaymentLoading(exam.id)
    setPaymentError("")
    
    try {
      const orderId = generateRandomOrderId()
      const paymentId = generateRandomPaymentId()
      
      const purchaseResponse = await apiUtils.post(
        `${API_CONFIG.API_BASE_URL}/purchases/exam/${exam.id}`,
        {
          amount: 0,
          paymentMethod: "razorpay",
          orderId: orderId,
          paymentId: paymentId,
        }
      )
      
      if (!purchaseResponse.ok) {
        throw new Error("Failed to save purchase")
      }

      const purchase: Purchase = {
        id: `purchase_${Date.now()}`,
        userId: user?.id || "",
        examId: exam.id,
        amount: 0,
        paymentId: paymentId,
        orderId: orderId,
        status: "completed",
        purchaseDate: new Date().toISOString(),
        paymentMethod: "razorpay",
        gateway: "razorpay",
      }

      // Update local purchased ids and purchases
      setPurchasedExamIds((prev) => new Set(prev).add(String(exam.id)))
      setUserPurchases((prev) => [...prev, purchase])

      alert("Exam added to your library successfully!")
      setPaymentLoading(null)
      setPendingExam(null)
    } catch (error) {
      console.error("Free purchase failed:", error)
      setPaymentError("Failed to add exam to your library. Please try again.")
      setPaymentLoading(null)
      setPendingExam(null)
    }
  }

  const initiatePaymentWithProfile = async (exam: Exam, profile: UserProfileMinimal) => {
    // Check if exam is free (offer price is 0)
    if (exam.offerPrice === 0) {
      await handleFreePurchase(exam)
      return
    }

    setPaymentLoading(exam.id)
    setPaymentError("")
    try {
      if (typeof (window as any).Razorpay !== "undefined") {
        await paymentService.initiateRazorpayPayment(
          { ...exam, id: exam.id, price: exam.offerPrice },
          profile,
          handlePaymentSuccess,
          handlePaymentError
        )
      } else {
        await paymentService.initiateCashfreePayment(
          { ...exam, id: exam.id, price: exam.offerPrice },
          profile,
          handlePaymentSuccess,
          handlePaymentError
        )
      }
    } catch (error) {
      handlePaymentError(error)
    }
  }

  const handlePurchase = async (exam: Exam) => {
    if (!user) {
      alert("Please login to purchase exams")
      return
    }
    if (!profileValid) {
      setPendingExam(exam)
      setShowProfileModal(true)
      return
    }
    await initiatePaymentWithProfile(exam, profileData)
  }

  const handlePaymentSuccess = async (response: any) => {
    try {
      if (!response.materialId || !response.userId) {
        throw new Error("Invalid payment response: missing materialId or userId")
      }

      const purchase: Purchase = {
        id: `purchase_${Date.now()}`,
        userId: response.userId.toString(),
        examId: response.materialId.toString(),
        amount: response.amount,
        paymentId: response.paymentId,
        orderId: response.orderId,
        status: "completed",
        purchaseDate: new Date().toISOString(),
        paymentMethod: response.paymentMethod || "razorpay",
        gateway: response.gateway || "razorpay",
      }

      const purchaseResponse = await apiUtils.post(
        `${API_CONFIG.API_BASE_URL}/purchases/exam/${response.materialId}`,
        {
          amount: response.amount,
          paymentMethod: response.paymentMethod,
          orderId: response.orderId,
          paymentId: response.paymentId,
        }
      )
      if (!purchaseResponse.ok) {
        throw new Error("Failed to save purchase")
      }

      // Update local purchased ids and purchases
      setPurchasedExamIds((prev) => new Set(prev).add(String(response.materialId)))
      setUserPurchases((prev) => [...prev, purchase])

      alert(
        `Payment successful via ${response.gateway === "cashfree" ? "Cashfree" : "Razorpay"}! You can now access the exam.`
      )
      setPaymentLoading(null)
      setPendingExam(null)
    } catch (error) {
      handlePaymentError(error)
    }
  }

  const handlePaymentError = (error: any) => {
    console.error("Payment failed:", error)
    setPaymentError("Payment failed. Please try again.")
    setPaymentLoading(null)
    setPendingExam(null)
  }

  const validateEmail = (email: string): boolean => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    return emailRegex.test(email)
  }

  const validatePhone = (phone: string): boolean => {
    const phoneRegex = /^\d{10}$/
    return phoneRegex.test(phone.replace(/\D/g, ""))
  }

  const getDerivedExamStatus = (exam: Exam): "upcoming" | "live" | "completed" => {
    if (exam.status !== "active") return "completed"
    const today = moment.utc().startOf("day")
    const examDate = moment.utc(exam.startTime).startOf("day")
    if (examDate.isAfter(today)) return "upcoming"
    else if (examDate.isSame(today)) return "live"
    else return "completed"
  }

  const isPurchased = (examId: string): boolean => {
    return purchasedExamIds.has(String(examId))
  }

  const filteredExams = exams.filter((exam) => {
    if (exam.status === "draft" || exam.status === "inactive") return false
    const derivedStatus = getDerivedExamStatus(exam)
    const matchesFilter = filter === "all" || derivedStatus === filter
    const matchesSearch =
      exam.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
      exam.description.toLowerCase().includes(searchTerm.toLowerCase())
    return matchesFilter && matchesSearch
  })

  const getStatusBadge = (status: "upcoming" | "live" | "completed") => {
    switch (status) {
      case "upcoming":
        return (
          <span className="bg-sky-100 text-sky-800 px-3 py-1 rounded-full text-sm font-medium">Upcoming</span>
        )
      case "live":
        return (
          <span className="bg-green-100 text-green-800 px-3 py-1 rounded-full text-sm font-medium animate-pulse">
            Live Today
          </span>
        )
      case "completed":
        return <span className="bg-gray-100 text-gray-800 px-3 py-1 rounded-full text-sm font-medium">Completed</span>
    }
  }

  const getDifficultyColor = (difficulty: string) => {
    return difficulty === "Easy"
      ? "text-green-700 bg-green-100"
      : difficulty === "Medium"
      ? "text-amber-700 bg-amber-100"
      : "text-rose-700 bg-rose-100"
  }

  const renderActionButton = (exam: Exam) => {
    const purchased = isPurchased(exam.id)

    if (purchased) {
      return (
        <div className="w-full space-y-2">
          <Link
            to={`/app/exams/${exam.id}`}
            className="w-full bg-emerald-600 text-white px-4 py-2 rounded-lg text-sm font-medium hover:bg-emerald-700 transition-colors flex items-center justify-center gap-2"
          >
            <Eye className="h-4 w-4" />
            <span>View Details</span>
          </Link>
          <div className="text-xs text-emerald-700 bg-emerald-50 border border-emerald-200 rounded-md px-2 py-1 flex items-center justify-center gap-1">
            <CheckCircle className="h-3 w-3" />
            <span>Purchased</span>
          </div>
        </div>
      )
    }

    if (exam.isPremium === 1) {
      const isFree = exam.offerPrice === 0
      return (
        <div className="w-full space-y-2">
          <button
            onClick={() => handlePurchase(exam)}
            disabled={paymentLoading === exam.id}
            className={`w-full px-4 py-2 rounded-lg text-sm font-medium transition-colors flex items-center justify-center gap-2 disabled:opacity-60 disabled:cursor-not-allowed relative ${
              isFree 
                ? "bg-green-600 text-white hover:bg-green-700" 
                : "bg-indigo-600 text-white hover:bg-indigo-700"
            }`}
            aria-busy={paymentLoading === exam.id}
            aria-live="polite"
          >
            {paymentLoading === exam.id ? (
              <>
                <span className="relative flex h-4 w-4">
                  <span className="animate-ping absolute inline-flex h-full w-full rounded-full bg-white opacity-75"></span>
                  <span className="relative inline-flex rounded-full h-4 w-4 bg-white/90"></span>
                </span>
                <span>{isFree ? "Adding to Library..." : "Processing Payment..."}</span>
              </>
            ) : (
              <>
                <ShoppingCart className="h-4 w-4" />
                <span>{isFree ? "Get Free" : `Buy Now - ₹${exam.offerPrice}`}</span>
              </>
            )}
          </button>
          <div className="flex items-center justify-center gap-2 text-[11px] text-gray-500">
            <ShieldCheck className="h-3.5 w-3.5" />
            <span>Secure checkout</span>
            <span className="mx-1">•</span>
            <CreditCard className="h-3.5 w-3.5" />
            <span>Instant access</span>
          </div>
        </div>
      )
    }

    return (
      <Link
        to={`/app/exams/${exam.id}`}
        className="w-full bg-neutral-900 text-white px-4 py-2 rounded-lg text-sm font-medium hover:bg-neutral-800 transition-colors flex items-center justify-center gap-2"
      >
        <Eye className="h-4 w-4" />
        <span>View Details</span>
      </Link>
    )
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="relative mx-auto mb-4 h-12 w-12">
            <div className="absolute inset-0 rounded-full border-2 border-gray-200"></div>
            <div className="absolute inset-0 rounded-full border-t-2 border-indigo-600 animate-spin"></div>
          </div>
          <p className="text-gray-600">Loading exams...</p>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <p className="text-red-600 mb-4">{error}</p>
          <button
            onClick={fetchExams}
            className="bg-indigo-600 text-white px-4 py-2 rounded-lg hover:bg-indigo-700"
          >
            Retry
          </button>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Exams</h1>
          <p className="text-gray-600 mt-1">Practice tests for UPSC preparation</p>
        </div>
        <div className="flex items-center gap-4 mt-4 sm:mt-0">
          <div className="flex items-center gap-2 rounded-md border border-gray-200 bg-white px-3 py-1.5">
            <ShoppingCart className="h-5 w-5 text-gray-600" />
            <span className="text-sm text-gray-700">
              {purchasedExamIds.size} purchased {purchasedExamIds.size === 1 ? "exam" : "exams"}
            </span>
          </div>

          {user && profileFetched && (
            <div className="flex items-center gap-2">
              {profileValid ? (
                <>
                  <CheckCircle className="h-4 w-4 text-green-500" />
                  <span className="text-xs text-green-700">Profile Complete</span>
                </>
              ) : (
                <>
                  <AlertCircle className="h-4 w-4 text-amber-500" />
                  <span className="text-xs text-amber-700">Profile Incomplete</span>
                </>
              )}
            </div>
          )}
        </div>
      </div>

      <div className="bg-white p-6 rounded-xl shadow-sm border border-gray-200">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 -translate-y-1/2 text-gray-400 h-4 w-4" />
            <input
              type="text"
              placeholder="Search exams..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-transparent"
            />
          </div>
          <div className="flex items-center gap-2">
            <Filter className="h-4 w-4 text-gray-500" />
            <select
              value={filter}
              onChange={(e) => setFilter(e.target.value as any)}
              className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-transparent w-full"
            >
              {["all", "live", "upcoming", "completed"].map((status) => (
                <option key={status} value={status}>
                  {status.charAt(0).toUpperCase() + status.slice(1)}
                </option>
              ))}
            </select>
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {filteredExams.map((exam) => {
          const purchased = isPurchased(exam.id)
          const status = getDerivedExamStatus(exam)
          const isPremium = exam.isPremium === 1
          const isFree = exam.offerPrice === 0
          return (
            <div
              key={exam.id}
              className={[
                "group relative rounded-2xl overflow-hidden transition-shadow",
                "bg-white border border-gray-200 shadow-sm hover:shadow-md",
                isPremium ? "ring-1 ring-indigo-100" : "",
              ].join(" ")}
            >
              {/* Premium ribbon */}
              {isPremium && !purchased && !isFree && (
                <div className="absolute right-0 top-0">
                  <div className="bg-gradient-to-r from-indigo-600 to-fuchsia-600 text-white text-xs font-semibold px-3 py-1.5 rounded-bl-xl shadow-sm flex items-center gap-1">
                    <Award className="h-3.5 w-3.5" />
                    Premium
                  </div>
                </div>
              )}

              {/* Free ribbon */}
              {isPremium && !purchased && isFree && (
                <div className="absolute right-0 top-0">
                  <div className="bg-gradient-to-r from-green-600 to-emerald-600 text-white text-xs font-semibold px-3 py-1.5 rounded-bl-xl shadow-sm flex items-center gap-1">
                    <Award className="h-3.5 w-3.5" />
                    Free
                  </div>
                </div>
              )}

              {/* Purchased ribbon */}
              {purchased && (
                <div className="absolute right-0 top-0">
                  <div className="bg-emerald-600 text-white text-xs font-semibold px-3 py-1.5 rounded-bl-xl shadow-sm flex items-center gap-1">
                    <CheckCircle className="h-3.5 w-3.5" />
                    Purchased
                  </div>
                </div>
              )}

              <div className="p-6">
                <div className="flex items-start justify-between mb-4">
                  <div className="flex items-center gap-2">
                    <FileText className="h-5 w-5 text-indigo-600" />
                    <span className="text-sm font-medium text-indigo-700 bg-indigo-50 px-2.5 py-1 rounded-md">
                      {exam.category}
                    </span>
                  </div>
                  <span className={`px-2.5 py-1 rounded-md text-xs font-semibold ${getDifficultyColor(exam.difficulty)}`}>
                    {exam.difficulty}
                  </span>
                </div>

                <h3 className="text-lg font-semibold text-gray-900 mb-2 line-clamp-1">{exam.title}</h3>
                <p className="text-gray-600 text-sm mb-4 line-clamp-2">{exam.description}</p>

                <div className="flex items-center justify-between text-sm text-gray-600 mb-4">
                  <div className="flex items-center gap-1.5">
                    <Calendar className="h-4 w-4 text-gray-500" />
                    <span>{exam.subject || "General"}</span>
                  </div>
                  {getStatusBadge(status)}
                </div>

                <div className="flex items-center text-sm text-gray-600 mb-4 gap-4">
                  <span className="flex items-center">
                    <Clock className="h-4 w-4 mr-1 text-gray-500" />
                    {exam.duration} min
                  </span>
                  <span className="flex items-center">
                    <FileText className="h-4 w-4 mr-1 text-gray-500" />
                    {exam.totalQuestions} Qs
                  </span>
                  <span className="flex items-center">
                    <Users className="h-4 w-4 mr-1 text-gray-500" />
                    {exam.attempts}
                  </span>
                </div>

                {/* Premium price block */}
                {isPremium && !isFree && (
                  <div className="mb-4 flex items-end justify-between">
                    <div className="flex items-baseline gap-2">
                      <span className="text-2xl font-bold text-gray-900">₹{exam.offerPrice}</span>
                      {exam.basePrice && exam.basePrice > (exam.offerPrice || 0) ? (
                        <span className="text-sm text-gray-500 line-through">₹{exam.basePrice}</span>
                      ) : null}
                    </div>
                    {!purchased && (
                      <span className="text-xs bg-emerald-50 text-emerald-700 px-2 py-1 rounded-md">
                        Save {exam.basePrice && exam.offerPrice ? Math.max(0, Math.round(100 - (exam.offerPrice / exam.basePrice) * 100)) : 0}%
                      </span>
                    )}
                  </div>
                )}

                {/* Free exam indicator */}
                {isPremium && isFree && !purchased && (
                  <div className="mb-4 flex items-center justify-center">
                   
                  </div>
                )}

                <div className="flex">
                  {renderActionButton(exam)}
                </div>
              </div>

              {/* Payment processing overlay per card */}
              {paymentLoading === exam.id && (
                <div className="absolute inset-0 bg-white/80 backdrop-blur-sm flex flex-col items-center justify-center gap-3 p-4">
                  <div className="relative h-10 w-10">
                    <div className="absolute inset-0 rounded-full border-2 border-indigo-200"></div>
                    <div className="absolute inset-0 rounded-full border-t-2 border-indigo-600 animate-spin"></div>
                  </div>
                  <div className="text-sm text-gray-700 font-medium">
                    {isFree ? "Adding to your library…" : "Processing your payment…"}
                  </div>
                  <div className="w-full max-w-[200px] h-1.5 bg-gray-200 rounded-full overflow-hidden">
                    <div className="h-full bg-indigo-600 animate-[progress_1.2s_ease-in-out_infinite] rounded-full" />
                  </div>
                  <div className="text-xs text-gray-500 flex items-center gap-1.5">
                    <ShieldCheck className="h-3.5 w-3.5 text-indigo-600" />
                    {isFree ? "Processing · Please wait" : "Secure · Do not close this window"}
                  </div>
                </div>
              )}
            </div>
          )
        })}
      </div>

      {filteredExams.length === 0 && (
        <div className="text-center py-12">
          <FileText className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">No exams found</h3>
          <p className="text-gray-600">Try adjusting your filters or search terms.</p>
        </div>
      )}

      {/* Profile Modal */}
      {showProfileModal && (
        <div className="fixed inset-0 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-xl shadow-xl max-w-md w-full max-h-[90vh] overflow-y-auto">
            <div className="p-6">
              <div className="flex items-center justify-between mb-6">
                <h3 className="text-xl font-semibold text-gray-900">Complete Your Profile</h3>
                <button
                  onClick={() => {
                    setShowProfileModal(false)
                    setPaymentLoading(null)
                    setPendingExam(null)
                    setProfileError("")
                  }}
                  className="text-gray-400 hover:text-gray-600 transition-colors"
                  aria-label="Close"
                >
                  <X className="h-6 w-6" />
                </button>
              </div>
              <p className="text-gray-600 mb-6">Please complete your profile information to proceed with purchases.</p>
              <form onSubmit={handleProfileFormSubmit} className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    <User className="h-4 w-4 inline mr-1" />
                    Full Name *
                  </label>
                  <input
                    type="text"
                    required
                    value={profileData.name || ""}
                    onChange={(e) => setProfileData((prev) => ({ ...prev, name: e.target.value }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-transparent"
                    placeholder="Enter your full name"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    <Mail className="h-4 w-4 inline mr-1" />
                    Email Address *
                  </label>
                  <input
                    type="email"
                    required
                    value={profileData.email || ""}
                    onChange={(e) => setProfileData((prev) => ({ ...prev, email: e.target.value }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-transparent"
                    placeholder="Enter your email address"
                  />
                  {profileData.email && !validateEmail(profileData.email) && (
                    <p className="text-red-500 text-xs mt-1">Please enter a valid email address</p>
                  )}
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    <Phone className="h-4 w-4 inline mr-1" />
                    Phone Number *
                  </label>
                  <input
                    type="tel"
                    required
                    value={profileData.phone || ""}
                    onChange={(e) => setProfileData((prev) => ({ ...prev, phone: e.target.value }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-transparent"
                    placeholder="Enter your phone number"
                    maxLength={10}
                  />
                  {profileData.phone && !validatePhone(profileData.phone) && (
                    <p className="text-red-500 text-xs mt-1">Please enter a valid 10-digit phone number</p>
                  )}
                </div>
                {profileError && (
                  <div className="bg-red-50 border border-red-200 rounded-lg p-3">
                    <div className="flex items-center gap-2">
                      <AlertCircle className="h-4 w-4 text-red-500" />
                      <span className="text-red-700 text-sm">{profileError}</span>
                    </div>
                  </div>
                )}
                <div className="flex gap-3 pt-4">
                  <button
                    type="button"
                    onClick={() => {
                      setShowProfileModal(false)
                      setPaymentLoading(null)
                      setPendingExam(null)
                      setProfileError("")
                    }}
                    className="flex-1 px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
                  >
                    Cancel
                  </button>
                  <button
                    type="submit"
                    disabled={
                      profileLoading ||
                      !profileData.name?.trim() ||
                      !profileData.email?.trim() ||
                      !profileData.phone?.trim() ||
                      !validateEmail(profileData.email || "") ||
                      !validatePhone(profileData.phone || "")
                    }
                    className="flex-1 bg-indigo-600 text-white px-4 py-2 rounded-lg hover:bg-indigo-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center gap-2"
                  >
                    {profileLoading ? (
                      <>
                        <span className="relative flex h-4 w-4">
                          <span className="animate-ping absolute inline-flex h-full w-full rounded-full bg-white opacity-75"></span>
                          <span className="relative inline-flex rounded-full h-4 w-4 bg-white/90"></span>
                        </span>
                        <span>Updating...</span>
                      </>
                    ) : (
                      <span>Update Profile</span>
                    )}
                  </button>
                </div>
              </form>
            </div>
          </div>
        </div>
      )}

      {/* Alerts */}
      {successAlert && (
        <div className="fixed top-4 right-4 bg-green-50 border border-green-200 text-green-800 px-4 py-3 rounded-lg shadow-lg z-50">
          <div className="flex items-center gap-2">
            <CheckCircle className="h-4 w-4" />
            <span>{successAlert}</span>
          </div>
        </div>
      )}

      {paymentError && (
        <div className="fixed bottom-4 right-4 bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg z-50">
          <div className="flex items-center gap-2">
            <AlertCircle className="h-4 w-4" />
            <span>{paymentError}</span>
          </div>
        </div>
      )}

      {/* keyframes for progress bar */}
      <style>{`
        @keyframes progress {
          0% { transform: translateX(-100%); }
          50% { transform: translateX(-20%); }
          100% { transform: translateX(100%); }
        }
      `}</style>
    </div>
  )
}

export default Exams
