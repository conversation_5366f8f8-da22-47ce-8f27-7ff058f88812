import React, { useState } from 'react';
import { Link } from 'react-router-dom';
import { ArrowLeft, HelpCircle, Search, BookOpen, MessageCircle, Phone, Mail, ChevronDown, ChevronRight } from 'lucide-react';

function HelpCenter() {
  const [searchQuery, setSearchQuery] = useState('');
  const [expandedFaq, setExpandedFaq] = useState<number | null>(null);

  const faqs = [
    {
      question: "How do I access my purchased courses?",
      answer: "After successful payment, you can access your courses from the 'My Courses' section in your dashboard. All purchased materials will be available immediately."
    },
    {
      question: "What payment methods do you accept?",
      answer: "We accept all major payment methods including UPI, Net Banking, Credit/Debit Cards, and digital wallets through our secure payment gateway."
    },
    {
      question: "Can I download study materials for offline use?",
      answer: "Yes, most of our study materials are available for download. Look for the download icon next to each material in your course dashboard."
    },
    {
      question: "How often are daily quizzes updated?",
      answer: "Daily quizzes are updated every day with fresh questions covering current affairs and static portions of the UPSC syllabus."
    },
    {
      question: "Do you provide refunds?",
      answer: "We have a 7-day refund policy for our courses. Please contact our support team within 7 days of purchase for refund requests."
    },
    {
      question: "How can I track my preparation progress?",
      answer: "Your dashboard provides detailed analytics including quiz scores, time spent studying, and progress tracking across different subjects."
    }
  ];

  const helpCategories = [
    {
      title: "Getting Started",
      description: "Learn how to navigate and use our platform",
      icon: BookOpen,
      color: "bg-blue-100 text-blue-600"
    },
    {
      title: "Courses & Materials",
      description: "Information about accessing and using study materials",
      icon: BookOpen,
      color: "bg-green-100 text-green-600"
    },
    {
      title: "Payment & Billing",
      description: "Payment methods, refunds, and billing queries",
      icon: MessageCircle,
      color: "bg-purple-100 text-purple-600"
    },
    {
      title: "Technical Support",
      description: "Troubleshooting and technical assistance",
      icon: HelpCircle,
      color: "bg-orange-100 text-orange-600"
    }
  ];

  const filteredFaqs = faqs.filter(faq =>
    faq.question.toLowerCase().includes(searchQuery.toLowerCase()) ||
    faq.answer.toLowerCase().includes(searchQuery.toLowerCase())
  );

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b border-gray-200">
        <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <Link
              to="/"
              className="flex items-center space-x-2 text-gray-600 hover:text-gray-900"
            >
              <ArrowLeft className="h-5 w-5" />
              <span>Back to Home</span>
            </Link>
            <div className="flex items-center space-x-2">
              <HelpCircle className="h-6 w-6 text-blue-600" />
              <h1 className="text-xl font-bold text-gray-900">Help Center</h1>
            </div>
          </div>
        </div>
      </div>

      {/* Hero Section */}
      <div className="bg-gradient-to-r from-blue-600 to-indigo-600 py-16">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h1 className="text-4xl font-bold text-white mb-4">How can we help you?</h1>
          <p className="text-xl text-blue-100 mb-8">
            Find answers to common questions or get in touch with our support team
          </p>

          {/* Search Bar */}
          <div className="relative max-w-2xl mx-auto">
            <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
            <input
              type="text"
              placeholder="Search for help articles..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="w-full pl-12 pr-4 py-4 rounded-xl border-0 focus:ring-2 focus:ring-blue-300 text-lg"
            />
          </div>
        </div>
      </div>

      <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        {/* Quick Contact */}
        <div className="bg-white rounded-lg shadow-sm p-8 mb-12">
          <h2 className="text-2xl font-bold text-gray-900 mb-6 text-center">Need Immediate Help?</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {/* WhatsApp */}
            <a
              href="https://whatsapp.com/channel/0029Vb6fu778PgsCE0AbJY1R"
              target="_blank"
              rel="noopener noreferrer"
              className="bg-green-50 p-6 rounded-lg text-center hover:bg-green-100 transition-colors group"
            >
              <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mx-auto mb-4 group-hover:bg-green-200 transition-colors">
                <MessageCircle className="h-6 w-6 text-green-600" />
              </div>
              <h3 className="font-semibold text-gray-900 mb-2">WhatsApp</h3>
              <p className="text-gray-600 text-sm">Join our channel for updates</p>
            </a>

            {/* Telegram */}
            <a
              href="https://t.me/letscrackupsc"
              target="_blank"
              rel="noopener noreferrer"
              className="bg-blue-50 p-6 rounded-lg text-center hover:bg-blue-100 transition-colors group"
            >
              <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mx-auto mb-4 group-hover:bg-blue-200 transition-colors">
                <MessageCircle className="h-6 w-6 text-blue-600" />
              </div>
              <h3 className="font-semibold text-gray-900 mb-2">Telegram</h3>
              <p className="text-gray-600 text-sm">Community discussions</p>
            </a>

            {/* Phone */}
            <a
              href="tel:8855965237"
              className="bg-purple-50 p-6 rounded-lg text-center hover:bg-purple-100 transition-colors group"
            >
              <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mx-auto mb-4 group-hover:bg-purple-200 transition-colors">
                <Phone className="h-6 w-6 text-purple-600" />
              </div>
              <h3 className="font-semibold text-gray-900 mb-2">Call Us</h3>
              <p className="text-gray-600 text-sm">8855965237</p>
            </a>

            {/* Email */}
            <a
              href="mailto:<EMAIL>"
              className="bg-red-50 p-6 rounded-lg text-center hover:bg-red-100 transition-colors group"
            >
              <div className="w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center mx-auto mb-4 group-hover:bg-red-200 transition-colors">
                <Mail className="h-6 w-6 text-red-600" />
              </div>
              <h3 className="font-semibold text-gray-900 mb-2">Email</h3>
              <p className="text-gray-600 text-sm">Get detailed support</p>
            </a>
          </div>
        </div>

        {/* Help Categories */}
        <div className="mb-12">
          <h2 className="text-2xl font-bold text-gray-900 mb-8 text-center">Browse Help Topics</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {helpCategories.map((category, index) => (
              <div key={index} className="bg-white p-6 rounded-lg shadow-sm hover:shadow-md transition-shadow cursor-pointer">
                <div className={`w-12 h-12 ${category.color} rounded-lg flex items-center justify-center mb-4`}>
                  <category.icon className="h-6 w-6" />
                </div>
                <h3 className="font-semibold text-gray-900 mb-2">{category.title}</h3>
                <p className="text-gray-600 text-sm">{category.description}</p>
              </div>
            ))}
          </div>
        </div>

        {/* FAQ Section */}
        <div className="bg-white rounded-lg shadow-sm p-8">
          <h2 className="text-2xl font-bold text-gray-900 mb-8 text-center">Frequently Asked Questions</h2>
          <div className="space-y-4">
            {filteredFaqs.map((faq, index) => (
              <div key={index} className="border border-gray-200 rounded-lg">
                <button
                  onClick={() => setExpandedFaq(expandedFaq === index ? null : index)}
                  className="w-full px-6 py-4 text-left flex items-center justify-between hover:bg-gray-50 transition-colors"
                >
                  <span className="font-semibold text-gray-900">{faq.question}</span>
                  {expandedFaq === index ? (
                    <ChevronDown className="h-5 w-5 text-gray-500" />
                  ) : (
                    <ChevronRight className="h-5 w-5 text-gray-500" />
                  )}
                </button>
                {expandedFaq === index && (
                  <div className="px-6 pb-4">
                    <p className="text-gray-600">{faq.answer}</p>
                  </div>
                )}
              </div>
            ))}
          </div>

          {filteredFaqs.length === 0 && searchQuery && (
            <div className="text-center py-8">
              <p className="text-gray-600">No results found for "{searchQuery}"</p>
              <p className="text-gray-500 mt-2">Try different keywords or contact our support team</p>
            </div>
          )}
        </div>

        {/* Still Need Help */}
        <div className="text-center mt-12">
          <h3 className="text-xl font-semibold text-gray-900 mb-4">Still need help?</h3>
          <p className="text-gray-600 mb-6">
            Can't find what you're looking for? Our support team is here to help.
          </p>
          <Link
            to="/contact"
            className="bg-blue-600 text-white px-8 py-3 rounded-lg font-semibold hover:bg-blue-700 transition-colors"
          >
            Contact Support
          </Link>
        </div>
      </div>
    </div>
  );
}

export default HelpCenter;