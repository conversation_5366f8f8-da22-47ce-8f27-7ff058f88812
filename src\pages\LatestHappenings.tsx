import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import {
  ArrowLeft,
  Calendar,
  Globe,
  TrendingUp,
  Leaf,
  Zap,
  Users,
  BookOpen,
  ExternalLink,
  Clock,
  Tag
} from 'lucide-react';
import { API_ENDPOINTS, apiUtils } from '../config/api';

interface NewsItem {
  id: string;
  title: string;
  category: string;
  date: string;
  summary: string;
  details: string;
  relevance: string;
  tags: string[];
  importance: 'high' | 'medium' | 'low';
}

const LatestHappenings: React.FC = () => {
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [selectedNews, setSelectedNews] = useState<NewsItem | null>(null);
  const [newsItems, setNewsItems] = useState<NewsItem[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchNewsItems();
  }, []);

  const fetchNewsItems = async () => {
    try {
      setLoading(true);
      const response = await apiUtils.get(API_ENDPOINTS.NEWS_ITEMS);
      if (response.ok) {
        const data = await response.json();
        setNewsItems(data);
      } else {
        // Fallback to hardcoded data
        setNewsItems(fallbackNewsItems);
      }
    } catch (err) {
      console.error('Error fetching news items:', err);
      setNewsItems(fallbackNewsItems);
    } finally {
      setLoading(false);
    }
  };
  const fallbackNewsItems: NewsItem[] = [
    {
      id: '1',
      title: "China's Dam Construction on Brahmaputra River",
      category: 'international',
      date: '2025-01-15',
      summary: "China formally begins dam construction on Brahmaputra River in Tibet, near Indian border in Arunachal Pradesh.",
      details: "This development has significant implications for India's water security, bilateral relations with China, and environmental concerns in the northeastern region. The dam could affect water flow to India and Bangladesh, making it a critical issue for regional hydro-politics.",
      relevance: "Important for International Relations, Environment & Ecology, and Internal Security sections. Links to India-China border disputes and water diplomacy.",
      tags: ['Water Security', 'India-China Relations', 'Brahmaputra', 'Environmental Impact'],
      importance: 'high'
    },
    {
      id: '2',
      title: "New Criminal Laws Implementation in India",
      category: 'governance',
      date: '2025-01-10',
      summary: "Several states implementing new criminal laws integrating technology for quicker justice delivery.",
      details: "The new criminal laws represent a paradigm shift in India's legal framework, emphasizing technology integration, victim-centric approach, and faster case disposal. This includes digital evidence handling, online FIR registration, and AI-assisted case management.",
      relevance: "Crucial for Polity & Governance, covering judicial reforms, technology in governance, and legal system modernization.",
      tags: ['Criminal Law Reform', 'Digital Justice', 'Legal Technology', 'Judicial System'],
      importance: 'high'
    },
    {
      id: '3',
      title: "AI for Economic Transformation in India",
      category: 'technology',
      date: '2025-01-08',
      summary: "Analysis of Artificial Intelligence's role in transforming India's economy, innovation, and job markets.",
      details: "AI is reshaping various sectors including healthcare, agriculture, manufacturing, and services. The government's AI mission focuses on responsible AI development, skill enhancement, and creating an AI-ready workforce while addressing concerns about job displacement.",
      relevance: "Important for Science & Technology, Economy sections. Covers digital economy, future of work, and technological innovation.",
      tags: ['Artificial Intelligence', 'Digital Economy', 'Innovation', 'Future of Work'],
      importance: 'high'
    },
    {
      id: '4',
      title: "Carbon Credit Trading Scheme and CBAM",
      category: 'environment',
      date: '2025-01-05',
      summary: "India's Carbon Credit Trading Scheme and EU's Carbon Border Adjustment Mechanism implications.",
      details: "The Carbon Credit Trading Scheme aims to create a market-based mechanism for carbon reduction. CBAM will impact India's exports to EU, requiring carbon accounting and potentially affecting trade competitiveness in steel, cement, and aluminum sectors.",
      relevance: "Critical for Environment & Ecology, International Trade, and Economy. Links to climate change mitigation and trade policy.",
      tags: ['Carbon Trading', 'Climate Policy', 'International Trade', 'Green Economy'],
      importance: 'high'
    },
    {
      id: '5',
      title: "NATO Developments and Strategic Implications",
      category: 'international',
      date: '2025-01-03',
      summary: "Recent NATO developments and their strategic implications for India's foreign policy.",
      details: "NATO's evolving role in global security, expansion discussions, and strategic partnerships affect India's multi-alignment policy. India's engagement with NATO partners while maintaining strategic autonomy remains crucial.",
      relevance: "Important for International Relations, covering strategic partnerships, defense cooperation, and foreign policy.",
      tags: ['NATO', 'Strategic Partnership', 'Defense Cooperation', 'Foreign Policy'],
      importance: 'medium'
    },
    {
      id: '6',
      title: "Valley of Flowers Conservation Spotlight",
      category: 'environment',
      date: '2024-12-28',
      summary: "Conservation efforts and biodiversity significance of Valley of Flowers National Park.",
      details: "The UNESCO World Heritage site faces challenges from climate change and tourism pressure. Conservation efforts include habitat restoration, species monitoring, and sustainable tourism practices.",
      relevance: "Relevant for Environment & Ecology, Geography, and Art & Culture sections. Covers biodiversity conservation and UNESCO sites.",
      tags: ['Biodiversity', 'UNESCO Heritage', 'Conservation', 'Uttarakhand'],
      importance: 'medium'
    },
    {
      id: '7',
      title: "India's Electric Vehicle Transition Policy",
      category: 'economy',
      date: '2024-12-25',
      summary: "Policy debates and implementation strategies for electric vehicle adoption in India.",
      details: "The EV transition involves infrastructure development, battery technology, charging networks, and policy incentives. Challenges include battery disposal, grid capacity, and affordability for mass adoption.",
      relevance: "Important for Economy, Environment, and Science & Technology. Covers sustainable development and energy transition.",
      tags: ['Electric Vehicles', 'Sustainable Transport', 'Energy Transition', 'Green Mobility'],
      importance: 'high'
    },
    {
      id: '8',
      title: "Tribal Empowerment and Demographic Dividend",
      category: 'governance',
      date: '2024-12-20',
      summary: "Policy initiatives for tribal empowerment and converting demographic trends into development opportunities.",
      details: "Focus on tribal education, healthcare, livelihood opportunities, and preserving cultural identity while ensuring mainstream integration. Demographic dividend strategies include skill development and employment generation.",
      relevance: "Crucial for Social Justice, Governance, and Indian Society sections. Covers inclusive development and social welfare.",
      tags: ['Tribal Development', 'Social Justice', 'Demographic Dividend', 'Inclusive Growth'],
      importance: 'medium'
    }
  ];

  const categories = [
    { id: 'all', name: 'All Topics', icon: Globe },
    { id: 'international', name: 'International Relations', icon: Globe },
    { id: 'economy', name: 'Economy', icon: TrendingUp },
    { id: 'environment', name: 'Environment', icon: Leaf },
    { id: 'technology', name: 'Science & Tech', icon: Zap },
    { id: 'governance', name: 'Governance', icon: Users },
    { id: 'culture', name: 'Art & Culture', icon: BookOpen }
  ];

  const filteredNews = selectedCategory === 'all'
    ? newsItems
    : newsItems.filter(item => item.category === selectedCategory);

  const getImportanceColor = (importance: string) => {
    switch (importance) {
      case 'high': return 'bg-red-100 text-red-800 border-red-200';
      case 'medium': return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'low': return 'bg-green-100 text-green-800 border-green-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getCategoryIcon = (category: string) => {
    const categoryData = categories.find(cat => cat.id === category);
    if (!categoryData) return Globe;
    return categoryData.icon;
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <Link to="/" className="flex items-center space-x-2 text-blue-600 hover:text-blue-700">
                <ArrowLeft className="h-4 w-4" />
                <span>Back to Home</span>
              </Link>
              <div className="h-6 w-px bg-gray-300"></div>
              <div className="flex items-center space-x-2">
                <div className="w-8 h-8 bg-gradient-to-r from-blue-600 to-indigo-600 rounded-lg flex items-center justify-center">
                  <span className="text-white font-bold text-sm">B</span>
                </div>
                <span className="font-semibold text-gray-900">Brainstorm UPSC</span>
              </div>
            </div>
            <div className="flex items-center space-x-4">
              <Link
                to="/login"
                className="text-gray-600 hover:text-blue-600 transition-colors"
              >
                Login
              </Link>
              <Link
                to="/register"
                className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors"
              >
                Sign Up
              </Link>
            </div>
          </div>
        </div>
      </div>

      {/* Hero Section */}
      <div className="bg-gradient-to-r from-blue-600 to-indigo-600 text-white py-12">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <h1 className="text-4xl font-bold mb-4">Latest Happenings for UPSC 2025-2026</h1>
            <p className="text-xl text-blue-100 max-w-3xl mx-auto">
              Stay updated with the most recent and significant national and international events
              that are highly likely to feature in upcoming UPSC Prelims and Mains examinations.
            </p>
            <div className="mt-6 flex items-center justify-center space-x-2 text-blue-100">
              <Clock className="h-4 w-4" />
              <span>Updated Daily • Last Update: January 15, 2025</span>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
          {/* Sidebar - Categories */}
          <div className="lg:col-span-1">
            <div className="bg-white rounded-lg shadow-sm p-6 sticky top-4">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Categories</h3>
              <div className="space-y-2">
                {categories.map((category) => {
                  const Icon = category.icon;
                  return (
                    <button
                      key={category.id}
                      onClick={() => setSelectedCategory(category.id)}
                      className={`w-full flex items-center space-x-3 px-3 py-2 rounded-lg text-left transition-colors ${selectedCategory === category.id
                        ? 'bg-blue-100 text-blue-700 border border-blue-200'
                        : 'text-gray-600 hover:bg-gray-100'
                        }`}
                    >
                      <Icon className="h-4 w-4" />
                      <span className="text-sm font-medium">{category.name}</span>
                    </button>
                  );
                })}
              </div>

              {/* Quick Stats */}
              <div className="mt-8 p-4 bg-gray-50 rounded-lg">
                <h4 className="font-medium text-gray-900 mb-3">Quick Stats</h4>
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span className="text-gray-600">Total Updates:</span>
                    <span className="font-medium">{newsItems.length}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">High Priority:</span>
                    <span className="font-medium text-red-600">
                      {newsItems.filter(item => item.importance === 'high').length}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">This Week:</span>
                    <span className="font-medium text-green-600">5</span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Main Content */}
          <div className="lg:col-span-3">
            {/* News Grid */}
            <div className="space-y-6">
              {filteredNews.map((news) => {
                const CategoryIcon = getCategoryIcon(news.category);
                return (
                  <div
                    key={news.id}
                    className="bg-white rounded-lg shadow-sm border hover:shadow-md transition-shadow cursor-pointer"
                    onClick={() => setSelectedNews(news)}
                  >
                    <div className="p-6">
                      <div className="flex items-start justify-between mb-4">
                        <div className="flex items-center space-x-3">
                          <div className="p-2 bg-blue-100 rounded-lg">
                            <CategoryIcon className="h-4 w-4 text-blue-600" />
                          </div>
                          <div>
                            <span className="text-sm font-medium text-blue-600 capitalize">
                              {categories.find(cat => cat.id === news.category)?.name}
                            </span>
                            <div className="flex items-center space-x-2 mt-1">
                              <Calendar className="h-3 w-3 text-gray-400" />
                              <span className="text-xs text-gray-500">
                                {new Date(news.date).toLocaleDateString('en-US', {
                                  year: 'numeric',
                                  month: 'long',
                                  day: 'numeric'
                                })}
                              </span>
                            </div>
                          </div>
                        </div>
                        <span className={`px-2 py-1 rounded-full text-xs font-medium border ${getImportanceColor(news.importance)}`}>
                          {news.importance.toUpperCase()}
                        </span>
                      </div>

                      <h3 className="text-xl font-semibold text-gray-900 mb-3 hover:text-blue-600 transition-colors">
                        {news.title}
                      </h3>

                      <p className="text-gray-600 mb-4 line-clamp-2">
                        {news.summary}
                      </p>

                      <div className="flex flex-wrap gap-2 mb-4">
                        {news.tags.slice(0, 3).map((tag, index) => (
                          <span
                            key={index}
                            className="inline-flex items-center space-x-1 px-2 py-1 bg-gray-100 text-gray-700 rounded-md text-xs"
                          >
                            <Tag className="h-3 w-3" />
                            <span>{tag}</span>
                          </span>
                        ))}
                        {news.tags.length > 3 && (
                          <span className="text-xs text-gray-500">+{news.tags.length - 3} more</span>
                        )}
                      </div>

                      <div className="flex items-center justify-between">
                        <div className="text-sm text-gray-500">
                          <span className="font-medium">UPSC Relevance:</span> {news.relevance.split('.')[0]}...
                        </div>
                        <button className="text-blue-600 hover:text-blue-700 text-sm font-medium flex items-center space-x-1">
                          <span>Read More</span>
                          <ExternalLink className="h-3 w-3" />
                        </button>
                      </div>
                    </div>
                  </div>
                );
              })}
            </div>

            {/* Call to Action */}
            <div className="mt-12 bg-gradient-to-r from-blue-600 to-indigo-600 rounded-lg p-8 text-center text-white">
              <h3 className="text-2xl font-bold mb-4">Want Personalized Current Affairs?</h3>
              <p className="text-blue-100 mb-6 max-w-2xl mx-auto">
                Join Brainstorm UPSC to get daily current affairs updates, detailed analysis,
                practice questions, and expert insights tailored for UPSC preparation.
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Link
                  to="/register"
                  className="bg-white text-blue-600 px-6 py-3 rounded-lg font-semibold hover:bg-gray-100 transition-colors"
                >
                  Start Free Trial
                </Link>
                <Link
                  to="/daily-quiz"
                  className="border-2 border-white text-white px-6 py-3 rounded-lg font-semibold hover:bg-white hover:text-blue-600 transition-colors"
                >
                  Try Daily Quiz
                </Link>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* News Detail Modal */}
      {selectedNews && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <div className="bg-white rounded-lg max-w-4xl w-full max-h-[90vh] overflow-y-auto">
            <div className="p-6">
              <div className="flex items-start justify-between mb-6">
                <div className="flex items-center space-x-3">
                  <div className="p-2 bg-blue-100 rounded-lg">
                    {React.createElement(getCategoryIcon(selectedNews.category), {
                      className: "h-5 w-5 text-blue-600"
                    })}
                  </div>
                  <div>
                    <span className="text-sm font-medium text-blue-600 capitalize">
                      {categories.find(cat => cat.id === selectedNews.category)?.name}
                    </span>
                    <div className="flex items-center space-x-2 mt-1">
                      <Calendar className="h-3 w-3 text-gray-400" />
                      <span className="text-xs text-gray-500">
                        {new Date(selectedNews.date).toLocaleDateString()}
                      </span>
                    </div>
                  </div>
                </div>
                <button
                  onClick={() => setSelectedNews(null)}
                  className="text-gray-400 hover:text-gray-600"
                >
                  ✕
                </button>
              </div>

              <h2 className="text-2xl font-bold text-gray-900 mb-4">
                {selectedNews.title}
              </h2>

              <div className="prose max-w-none mb-6">
                <p className="text-gray-700 leading-relaxed mb-4">
                  {selectedNews.details}
                </p>

                <div className="bg-blue-50 border-l-4 border-blue-400 p-4 mb-4">
                  <h4 className="font-semibold text-blue-900 mb-2">UPSC Relevance:</h4>
                  <p className="text-blue-800">{selectedNews.relevance}</p>
                </div>
              </div>

              <div className="flex flex-wrap gap-2 mb-6">
                {selectedNews.tags.map((tag, index) => (
                  <span
                    key={index}
                    className="inline-flex items-center space-x-1 px-3 py-1 bg-gray-100 text-gray-700 rounded-full text-sm"
                  >
                    <Tag className="h-3 w-3" />
                    <span>{tag}</span>
                  </span>
                ))}
              </div>

              <div className="flex justify-end space-x-4">
                <button
                  onClick={() => setSelectedNews(null)}
                  className="px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors"
                >
                  Close
                </button>
                <Link
                  to="/register"
                  className="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors"
                >
                  Join for More Updates
                </Link>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default LatestHappenings;

