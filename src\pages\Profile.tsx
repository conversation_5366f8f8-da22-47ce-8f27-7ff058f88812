import React, { useState, useEffect } from 'react';
import { User, Mail, Phone, Calendar, Edit, Save, X, Trophy, BookOpen, Clock, MapPin } from 'lucide-react';
import { useAuth } from '../contexts/AuthContext';
import { API_ENDPOINTS, apiUtils } from '../config/api';

interface UserProfile {
  id?: string; // Firestore ID is a string
  userId: string;
  name: string; // Backend uses 'name'
  email: string;
  phone: string; // Backend uses 'phone' with country code
  dateOfBirth: string; // Stored as yyyy-MM-dd string
  address: string;
  city: string;
  state: string;
  pincode: string;
  createdAt?: any;
  updatedAt?: any;
}

// Map frontend form data to backend UserProfile model
const mapFormDataToProfile = (formData: any, existingProfile: UserProfile | null, userId: string): Omit<UserProfile, 'id'> => {
  return {
    userId: existingProfile?.userId || userId,
    name: formData.fullName,
    email: formData.email,
    phone: formData.mobile ? `+91${formData.mobile}` : '',
    dateOfBirth: formData.dateOfBirth,
    address: formData.address,
    city: formData.city,
    state: formData.state,
    pincode: formData.pincode,
  };
};

function Profile() {
  const { user } = useAuth();
  const [isEditing, setIsEditing] = useState(false);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState('');
  const [userProfile, setUserProfile] = useState<UserProfile | null>(null);

  // Use a separate state for form data to avoid mutating userProfile directly
  const [formData, setFormData] = useState({
    fullName: '',
    email: '',
    mobile: '',
    dateOfBirth: '',
    address: '',
    city: '',
    state: '',
    pincode: ''
  });

  useEffect(() => {
    const fetchUserProfile = async () => {
      if (!user?.id) {
        setError('User not authenticated');
        setLoading(false);
        return;
      }

      try {
        setLoading(true);
        const response = await apiUtils.get(API_ENDPOINTS.CURRENT_USER_PROFILE);

        if (response.ok) {
          const profile: UserProfile = await response.json();
          setUserProfile(profile);
          setFormData({
            fullName: profile.name || '',
            email: profile.email || '',
            mobile: (profile.phone || '').replace('+91', ''),
            dateOfBirth: profile.dateOfBirth || '',
            address: profile.address || '',
            city: profile.city || '',
            state: profile.state || '',
            pincode: profile.pincode || '',
          });
        } else if (response.status === 404) {
          // *** GRACEFUL HANDLING OF NEW USER ***
          // Profile not found, so we initialize a new one.
          console.log('No profile found. Initializing a new profile form.');
          setUserProfile({ userId: user.id } as UserProfile); // Create a temporary profile object
          setFormData({
            fullName: user.displayName || '',
            email: user.email || '',
            mobile: '',
            dateOfBirth: '',
            address: '',
            city: '',
            state: '',
            pincode: '',
          });
          setIsEditing(true); // Automatically enter edit mode
          setError('Welcome! Please complete your profile to get started.'); // Informative message
        } else {
          // Handle other errors (500, etc.)
          const errorData = await response.json();
          setError(errorData.message || 'Failed to load profile data.');
        }
      } catch (err) {
        console.error('Error fetching user profile:', err);
        setError('A network error occurred. Please try again.');
      } finally {
        setLoading(false);
      }
    };

    if (user) {
        fetchUserProfile();
    }
  }, [user]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  const handleSave = async () => {
    if (!user?.id) return;

    setSaving(true);
    setError('');

    const profilePayload = mapFormDataToProfile(formData, userProfile, user.id);

    try {
      let response;
      let updatedProfile;

      if (userProfile && userProfile.id) {
        // *** UPDATE existing profile (PUT request) ***
        console.log("Updating existing profile:", userProfile.id);
        const payloadWithId = { ...profilePayload, id: userProfile.id };
        response = await apiUtils.put(`${API_ENDPOINTS.USER_PROFILES}/${userProfile.id}`, payloadWithId);
      } else {
        // *** CREATE new profile (POST request) ***
        console.log("Creating new profile for user:", user.id);
        response = await apiUtils.post(API_ENDPOINTS.USER_PROFILES, profilePayload);
      }

      if (response.ok) {
        updatedProfile = await response.json();
        setUserProfile(updatedProfile); // IMPORTANT: Update state with the saved profile (contains the new ID)
        setIsEditing(false);
      } else {
        const errorData = await response.json();
        setError(errorData.message || 'Failed to save profile. Please check your input.');
      }
    } catch (err) {
      console.error('Error saving profile:', err);
      setError('A network error occurred. Please try again.');
    } finally {
      setSaving(false);
    }
  };

  const handleCancel = () => {
    setIsEditing(false);
    setError('');
    // Reset form to last saved state
    if (userProfile) {
        setFormData({
            fullName: userProfile.name || '',
            email: userProfile.email || '',
            mobile: (userProfile.phone || '').replace('+91', ''),
            dateOfBirth: userProfile.dateOfBirth || '',
            address: userProfile.address || '',
            city: userProfile.city || '',
            state: userProfile.state || '',
            pincode: userProfile.pincode || '',
        });
    }
  };
  
  const indianStates = [ 'Andhra Pradesh', 'Arunachal Pradesh', 'Assam', 'Bihar', 'Chhattisgarh', 'Goa', 'Gujarat', 'Haryana', 'Himachal Pradesh', 'Jharkhand', 'Karnataka', 'Kerala', 'Madhya Pradesh', 'Maharashtra', 'Manipur', 'Meghalaya', 'Mizoram', 'Nagaland', 'Odisha', 'Punjab', 'Rajasthan', 'Sikkim', 'Tamil Nadu', 'Telangana', 'Tripura', 'Uttar Pradesh', 'Uttarakhand', 'West Bengal', 'Delhi', 'Jammu and Kashmir', 'Ladakh', 'Puducherry' ];
  const stats = [ { label: 'Exams Taken', value: '24', icon: Trophy, color: 'text-blue-600' }, { label: 'Study Hours', value: '156', icon: Clock, color: 'text-green-600' }, { label: 'Materials Purchased', value: '12', icon: BookOpen, color: 'text-purple-600' }, { label: 'Average Score', value: '88%', icon: Trophy, color: 'text-orange-600' } ];

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-96">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  // We can now render the component even if userProfile is not fully loaded from DB
  // because we create a temporary one for new users.
  if (!userProfile) {
    return (
      <div className="text-center py-12">
        <p className="text-red-500">{error || 'Could not load user profile.'}</p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Profile</h1>
          <p className="text-gray-600 mt-1">Manage your account settings and preferences</p>
        </div>
        <div className="flex space-x-2">
          {isEditing ? (
            <>
              <button onClick={handleCancel} className="flex items-center space-x-2 px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors" disabled={saving} >
                <X className="h-4 w-4" />
                <span>Cancel</span>
              </button>
              <button onClick={handleSave} disabled={saving} className="flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50" >
                <Save className="h-4 w-4" />
                <span>{saving ? 'Saving...' : 'Save'}</span>
              </button>
            </>
          ) : (
            <button onClick={() => setIsEditing(true)} className="flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors" >
              <Edit className="h-4 w-4" />
              <span>Edit Profile</span>
            </button>
          )}
        </div>
      </div>

      {error && (
        <div className="bg-red-50 border border-red-200 text-red-600 px-4 py-3 rounded-lg">
          {error}
        </div>
      )}

      {/* Stats Cards - Only show if not a new user in edit mode */}
      {!isEditing || (userProfile && userProfile.id) ? (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {stats.map((stat, index) => (
          <div key={index} className="bg-white p-6 rounded-xl shadow-sm border border-gray-200">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">{stat.label}</p>
                <p className="text-2xl font-bold text-gray-900">{stat.value}</p>
              </div>
              <div className={`p-3 rounded-lg bg-gray-100`}>
                <stat.icon className={`h-6 w-6 ${stat.color}`} />
              </div>
            </div>
          </div>
        ))}
      </div>
      ) : null}

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Profile Information */}
        <div className="lg:col-span-2 bg-white rounded-xl shadow-sm border border-gray-200">
          <div className="p-6 border-b border-gray-200">
            <h3 className="text-lg font-semibold text-gray-900">Personal Information</h3>
          </div>
          <div className="p-6 space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Full Name</label>
                {isEditing ? (
                  <input type="text" name="fullName" value={formData.fullName} onChange={handleInputChange} className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent" />
                ) : (
                  <div className="flex items-center space-x-2">
                    <User className="h-5 w-5 text-gray-400" />
                    <span className="text-gray-900">{userProfile.name || 'Not provided'}</span>
                  </div>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Email</label>
                  <div className="flex items-center space-x-2">
                    <Mail className="h-5 w-5 text-gray-400" />
                    {/* Email is usually not editable */}
                    <span className="text-gray-900">{userProfile.email}</span>
                  </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Mobile</label>
                {isEditing ? (
                  <div className="relative">
                    <span className="absolute left-3 top-2 text-gray-500">+91</span>
                    <input type="tel" name="mobile" value={formData.mobile} onChange={(e) => { const value = e.target.value.replace(/\D/g, '').slice(0, 10); setFormData(prev => ({ ...prev, mobile: value })); }} className="w-full pl-12 pr-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent" maxLength={10} />
                  </div>
                ) : (
                  <div className="flex items-center space-x-2">
                    <Phone className="h-5 w-5 text-gray-400" />
                    <span className="text-gray-900">{userProfile.phone || 'Not provided'}</span>
                  </div>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Date of Birth</label>
                {isEditing ? (
                  <input type="date" name="dateOfBirth" value={formData.dateOfBirth} onChange={handleInputChange} className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent" />
                ) : (
                  <div className="flex items-center space-x-2">
                    <Calendar className="h-5 w-5 text-gray-400" />
                    <span className="text-gray-900"> {userProfile.dateOfBirth ? new Date(userProfile.dateOfBirth).toLocaleDateString('en-CA') : 'Not provided'} </span>
                  </div>
                )}
              </div>

              <div className="md:col-span-2">
                <label className="block text-sm font-medium text-gray-700 mb-2">Address</label>
                {isEditing ? (
                  <textarea name="address" value={formData.address} onChange={handleInputChange} rows={3} className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent" />
                ) : (
                  <div className="flex items-start space-x-2">
                    <MapPin className="h-5 w-5 text-gray-400 mt-0.5" />
                    <div>
                      <p className="text-gray-900">{userProfile.address || 'Not provided'}</p>
                      {(userProfile.city || userProfile.state || userProfile.pincode) && ( <p className="text-gray-600 text-sm"> {[userProfile.city, userProfile.state, userProfile.pincode].filter(Boolean).join(', ')} </p> )}
                    </div>
                  </div>
                )}
              </div>

              {isEditing && (
                <>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">City</label>
                    <input type="text" name="city" value={formData.city} onChange={handleInputChange} className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent" />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">State</label>
                    <select name="state" value={formData.state} onChange={handleInputChange} className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent" >
                      <option value="">Select State</option>
                      {indianStates.map(state => ( <option key={state} value={state}>{state}</option> ))}
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Pincode</label>
                    <input type="text" name="pincode" value={formData.pincode} onChange={(e) => { const value = e.target.value.replace(/\D/g, '').slice(0, 6); setFormData(prev => ({ ...prev, pincode: value })); }} className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent" maxLength={6} />
                  </div>
                </>
              )}
            </div>
          </div>
        </div>

        {/* Subscription & Preferences - Only show if not a new user in edit mode */}
         {!isEditing || (userProfile && userProfile.id) ? (
        <div className="space-y-6">
          <div className="bg-white rounded-xl shadow-sm border border-gray-200">
            <div className="p-6 border-b border-gray-200">
              <h3 className="text-lg font-semibold text-gray-900">Subscription</h3>
            </div>
            <div className="p-6">
              <div className="bg-gradient-to-r from-blue-50 to-indigo-50 p-4 rounded-lg">
                <div className="flex items-center justify-between mb-2">
                  <span className="text-sm font-medium text-gray-600">Current Plan</span>
                  <span className="bg-green-100 text-green-800 px-2 py-1 rounded-full text-xs font-medium"> {user?.subscription === 'premium' ? 'Premium' : 'Free'} </span>
                </div>
                <p className="text-lg font-bold text-gray-900"> {user?.subscription === 'premium' ? '₹2,999/year' : 'Free Plan'} </p>
                <p className="text-sm text-gray-600"> {user?.subscription === 'premium' ? 'Expires on Dec 31, 2024' : 'Upgrade to unlock premium features'} </p>
              </div>
              <button className="w-full mt-4 bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-700 transition-colors">
                {user?.subscription === 'premium' ? 'Manage Subscription' : 'Upgrade to Premium'}
              </button>
            </div>
          </div>
        </div>
        ) : null}
      </div>
    </div>
  );
}

export default Profile;