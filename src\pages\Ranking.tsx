import React, { useState, useEffect } from 'react';
import { use<PERSON>ara<PERSON>, Link } from 'react-router-dom';
import { Trophy, Medal, Award, Clock, Users, ArrowLeft, Crown, Star, TrendingUp, CheckCircle } from 'lucide-react';
import { API_ENDPOINTS, apiUtils } from '../config/api';

function Ranking() {
  const { examId } = useParams();
  const [ranking, setRanking] = useState<any>(null);
  const [exam, setExam] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [filter, setFilter] = useState('all'); // all, top100, nearby

  useEffect(() => {
    if (examId) {
      fetchRanking(examId);
      fetchExamDetails(examId);
    }
  }, [examId]);

  // Auto-refresh for live rankings
  useEffect(() => {
    if (ranking?.isLive && examId) {
      const interval = setInterval(() => {
        fetchRanking(examId);
      }, 30000); // Refresh every 30 seconds for live exams

      return () => clearInterval(interval);
    }
  }, [ranking?.isLive, examId]);

  const fetchRanking = async (id: string) => {
    try {
      setLoading(true);
      setError('');

      // Check if exam is live and fetch live ranking
      const examResponse = await apiUtils.get(`${API_ENDPOINTS.EXAMS}/${id}`);
      let examData = null;
      if (examResponse.ok) {
        examData = await examResponse.json();
      }

      // Determine which ranking endpoint to use
      let rankingEndpoint = `${API_ENDPOINTS.EXAM_RESULTS}/ranking/${id}`;
      if (examData?.status === 'live') {
        rankingEndpoint = `${API_ENDPOINTS.EXAM_RESULTS}/live-ranking/${id}`;
      }

      // Fetch ranking data with authentication
      const response = await apiUtils.get(rankingEndpoint);

      if (response.ok) {
        const data = await response.json();
        console.log('Ranking data received:', data);

        // Transform API response to match frontend expectations
        const transformedRanking = transformRankingData(data, examData);
        setRanking(transformedRanking);
        return;
      } else {
        console.warn(`API returned ${response.status}, falling back to mock data`);
        // Generate mock ranking data as fallback
        const mockRanking = generateMockRanking(id);
        setRanking(mockRanking);
      }

    } catch (err) {
      console.error('Error fetching ranking:', err);
      setError('Unable to load ranking data. Showing sample data.');
      // Generate mock data even on error
      const mockRanking = generateMockRanking(id);
      setRanking(mockRanking);
    } finally {
      setLoading(false);
    }
  };

  const fetchExamDetails = async (id: string) => {
    try {
      const response = await apiUtils.get(`${API_ENDPOINTS.EXAMS}/${id}`);
      if (response.ok) {
        const data = await response.json();
        setExam(data);
      } else {
        // Mock exam data
        setExam({
          id: id,
          title: `UPSC Prelims Mock Test ${id}`,
          description: "Comprehensive test covering General Studies Paper I topics",
          duration: 120,
          questions: 100,
          participants: Math.floor(Math.random() * 1000) + 500,
          status: 'completed'
        });
      }
    } catch (err) {
      console.error('Error fetching exam details:', err);
    }
  };

  const transformRankingData = (apiData: any, examData: any) => {
    // Handle both regular ranking and live ranking responses
    const isLiveRanking = apiData.currentRankings !== undefined;

    if (isLiveRanking) {
      // Transform LiveRankingResponse
      return {
        examId: apiData.examId,
        examTitle: apiData.examTitle,
        totalParticipants: apiData.totalParticipants,
        topPerformers: apiData.currentRankings?.map((entry: any) => ({
          rank: entry.currentRank || entry.rank,
          name: entry.participantName || entry.name,
          email: entry.participantEmail || entry.email,
          score: entry.currentScore || entry.score || 0,
          totalMarks: entry.totalMarks || 0,
          maxMarks: entry.maxMarks || 200,
          timeTaken: entry.timeSpentMinutes || entry.timeTaken || 0,
          correctAnswers: entry.correctAnswers || 0,
          incorrectAnswers: entry.incorrectAnswers || 0,
          unanswered: entry.unanswered || 0,
          percentile: entry.percentile || 0,
          completedAt: entry.completedAt || entry.lastActivity,
          participantStatus: entry.participantStatus || 'completed',
          rankChange: entry.rankChange,
          rankChangeValue: entry.rankChangeValue
        })) || [],
        statistics: {
          averageScore: apiData.liveStats?.averageScore || 0,
          averageTime: apiData.liveStats?.averageTimeSpent || 0,
          highestScore: apiData.liveStats?.currentHighestScore || 0,
          lowestScore: apiData.liveStats?.currentLowestScore || 0,
          passPercentage: 75 // Default value
        },
        lastUpdated: apiData.lastUpdated,
        isLive: true,
        activeParticipants: apiData.activeParticipants,
        completedParticipants: apiData.completedParticipants
      };
    } else {
      // Transform regular ExamRankingResponse
      return {
        examId: apiData.examId,
        examTitle: apiData.examTitle,
        totalParticipants: apiData.totalParticipants,
        topPerformers: apiData.topPerformers?.map((entry: any) => ({
          rank: entry.rank,
          name: entry.name,
          email: entry.email,
          score: entry.score,
          totalMarks: entry.totalMarks,
          maxMarks: entry.maxMarks,
          timeTaken: entry.timeTaken,
          correctAnswers: entry.correctAnswers,
          incorrectAnswers: entry.incorrectAnswers,
          unanswered: entry.unanswered,
          percentile: entry.percentile,
          completedAt: entry.completedAt
        })) || [],
        statistics: apiData.statistics || {
          averageScore: 0,
          averageTime: 0,
          highestScore: 0,
          lowestScore: 0,
          passPercentage: 0
        },
        lastUpdated: apiData.lastUpdated,
        isLive: false
      };
    }
  };

  const generateMockRanking = (examId: string) => {
    const totalParticipants = Math.floor(Math.random() * 1000) + 500;

    // Generate top performers
    const topPerformers = Array.from({ length: 100 }, (_, i) => ({
      rank: i + 1,
      name: i < 3 ? `Top Performer ${i + 1}` : `User ${String.fromCharCode(65 + (i % 26))}***`,
      email: `user${i + 1}@example.com`,
      score: Math.max(0, 98 - i * 0.5 - Math.random() * 2),
      totalMarks: 0,
      maxMarks: 200,
      timeTaken: 90 + i * 2 + Math.floor(Math.random() * 10),
      correctAnswers: 0,
      incorrectAnswers: 0,
      unanswered: 0,
      percentile: Math.max(0, 100 - (i / totalParticipants) * 100),
      completedAt: new Date(Date.now() - Math.random() * 24 * 60 * 60 * 1000).toISOString()
    }));

    // Calculate derived values
    topPerformers.forEach(performer => {
      performer.totalMarks = Math.round((performer.score / 100) * 200);
      performer.correctAnswers = Math.floor(performer.totalMarks / 2);
      performer.incorrectAnswers = Math.floor((200 - performer.totalMarks) / 2.67);
      performer.unanswered = 100 - performer.correctAnswers - performer.incorrectAnswers;
    });

    return {
      examId: examId,
      examTitle: `UPSC Prelims Mock Test ${examId}`,
      totalParticipants,
      topPerformers,
      statistics: {
        averageScore: Math.round(topPerformers.reduce((sum, p) => sum + p.score, 0) / topPerformers.length * 0.7),
        averageTime: Math.round(topPerformers.reduce((sum, p) => sum + p.timeTaken, 0) / topPerformers.length * 1.2),
        highestScore: topPerformers[0]?.score || 98,
        lowestScore: Math.max(0, topPerformers[topPerformers.length - 1]?.score - 20 || 20),
        passPercentage: Math.floor(Math.random() * 30) + 60
      },
      lastUpdated: new Date().toISOString()
    };
  };

  const getRankIcon = (rank: number) => {
    if (rank === 1) return <Crown className="h-6 w-6 text-yellow-500" />;
    if (rank === 2) return <Medal className="h-6 w-6 text-gray-400" />;
    if (rank === 3) return <Award className="h-6 w-6 text-amber-600" />;
    if (rank <= 10) return <Star className="h-5 w-5 text-blue-500" />;
    return <Trophy className="h-4 w-4 text-gray-400" />;
  };

  const getRankBadgeColor = (rank: number) => {
    if (rank === 1) return 'bg-gradient-to-r from-yellow-400 to-yellow-600 text-white';
    if (rank === 2) return 'bg-gradient-to-r from-gray-300 to-gray-500 text-white';
    if (rank === 3) return 'bg-gradient-to-r from-amber-400 to-amber-600 text-white';
    if (rank <= 10) return 'bg-gradient-to-r from-blue-500 to-blue-600 text-white';
    if (rank <= 50) return 'bg-gradient-to-r from-green-500 to-green-600 text-white';
    return 'bg-gray-100 text-gray-700';
  };

  const getScoreColor = (score: number) => {
    if (score >= 90) return 'text-green-600';
    if (score >= 75) return 'text-blue-600';
    if (score >= 60) return 'text-yellow-600';
    return 'text-red-600';
  };

  const formatTime = (minutes: number) => {
    const hours = Math.floor(minutes / 60);
    const mins = minutes % 60;
    if (hours > 0) {
      return `${hours}h ${mins}m`;
    }
    return `${mins}m`;
  };

  const filteredRanking = ranking?.topPerformers?.filter((performer: any) => {
    if (filter === 'top100') return performer.rank <= 100;
    if (filter === 'nearby') return performer.rank <= 50; // Mock nearby logic
    return true;
  }) || [];

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading ranking...</p>
        </div>
      </div>
    );
  }

  if (error && !ranking) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <Trophy className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">Unable to load ranking</h3>
          <p className="text-gray-600 mb-4">{error}</p>
          <Link
            to="/app/exams"
            className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors"
          >
            Back to Exams
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="mb-8">
          <Link
            to="/app/exams"
            className="flex items-center text-gray-600 hover:text-gray-900 mb-4"
          >
            <ArrowLeft className="h-5 w-5 mr-2" />
            Back to Exams
          </Link>

          <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
            <div className="flex items-center justify-between">
              <div className="flex-1">
                <div className="flex items-center space-x-3 mb-2">
                  <h1 className="text-3xl font-bold text-gray-900">
                    {exam?.title || ranking?.examTitle} - Leaderboard
                  </h1>
                  {ranking?.isLive && (
                    <div className="flex items-center space-x-2">
                      <div className="w-3 h-3 bg-red-500 rounded-full animate-pulse"></div>
                      <span className="text-red-600 font-medium text-sm">LIVE</span>
                    </div>
                  )}
                </div>
                <p className="text-gray-600">
                  {exam?.description || 'Comprehensive UPSC preparation test'}
                </p>
                {ranking?.isLive && (
                  <div className="mt-3 flex items-center space-x-4 text-sm">
                    <span className="text-green-600">
                      <Users className="h-4 w-4 inline mr-1" />
                      {ranking.activeParticipants} Active
                    </span>
                    <span className="text-blue-600">
                      <CheckCircle className="h-4 w-4 inline mr-1" />
                      {ranking.completedParticipants} Completed
                    </span>
                    <span className="text-gray-500">
                      Last updated: {new Date(ranking.lastUpdated).toLocaleTimeString()}
                    </span>
                  </div>
                )}
              </div>
              <div className="text-right">
                <div className="bg-blue-100 rounded-lg p-4">
                  <Trophy className="h-8 w-8 text-blue-600 mx-auto mb-2" />
                  <div className="text-2xl font-bold text-gray-900">
                    {ranking?.totalParticipants || 0}
                  </div>
                  <div className="text-sm text-gray-600">Total Participants</div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Statistics */}
        {ranking?.statistics && (
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
            <div className="bg-white p-6 rounded-xl shadow-sm border border-gray-200 text-center">
              <TrendingUp className="h-8 w-8 text-green-600 mx-auto mb-2" />
              <div className="text-2xl font-bold text-gray-900">{ranking.statistics.averageScore}%</div>
              <div className="text-sm text-gray-600">Average Score</div>
            </div>
            <div className="bg-white p-6 rounded-xl shadow-sm border border-gray-200 text-center">
              <Clock className="h-8 w-8 text-blue-600 mx-auto mb-2" />
              <div className="text-2xl font-bold text-gray-900">{formatTime(ranking.statistics.averageTime)}</div>
              <div className="text-sm text-gray-600">Average Time</div>
            </div>
            <div className="bg-white p-6 rounded-xl shadow-sm border border-gray-200 text-center">
              <Crown className="h-8 w-8 text-yellow-600 mx-auto mb-2" />
              <div className="text-2xl font-bold text-gray-900">{ranking.statistics.highestScore}%</div>
              <div className="text-sm text-gray-600">Highest Score</div>
            </div>
            <div className="bg-white p-6 rounded-xl shadow-sm border border-gray-200 text-center">
              <Users className="h-8 w-8 text-purple-600 mx-auto mb-2" />
              <div className="text-2xl font-bold text-gray-900">{ranking.statistics.passPercentage}%</div>
              <div className="text-sm text-gray-600">Pass Rate</div>
            </div>
          </div>
        )}

        {/* Filters */}
        <div className="bg-white p-6 rounded-xl shadow-sm border border-gray-200 mb-8">
          <div className="flex items-center space-x-4">
            <span className="text-sm font-medium text-gray-700">Show:</span>
            {[
              { key: 'all', label: 'All Participants' },
              { key: 'top100', label: 'Top 100' },
              { key: 'nearby', label: 'Top 50' }
            ].map((filterOption) => (
              <button
                key={filterOption.key}
                onClick={() => setFilter(filterOption.key)}
                className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors ${filter === filterOption.key
                  ? 'bg-blue-600 text-white'
                  : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
                  }`}
              >
                {filterOption.label}
              </button>
            ))}
          </div>
        </div>

        {/* Ranking Table */}
        <div className="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden">
          <div className="px-6 py-4 border-b border-gray-200">
            <h3 className="text-lg font-semibold text-gray-900">
              Leaderboard ({filteredRanking.length} participants)
            </h3>
          </div>

          <div className="overflow-x-auto">
            <table className="w-full">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Rank
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Participant
                  </th>
                  {ranking?.isLive && (
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Status
                    </th>
                  )}
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Score
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Marks
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Time Taken
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Percentile
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {filteredRanking.map((performer: any, index: number) => (
                  <tr key={performer.rank} className={`hover:bg-gray-50 ${performer.rank <= 3 ? 'bg-yellow-50' : ''}`}>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center space-x-3">
                        {getRankIcon(performer.rank)}
                        <span className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${getRankBadgeColor(performer.rank)}`}>
                          #{performer.rank}
                        </span>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div>
                        <div className="flex items-center space-x-2">
                          <div className="text-sm font-medium text-gray-900">{performer.name}</div>
                          {performer.rankChange && (
                            <span className={`text-xs px-2 py-1 rounded-full ${performer.rankChange === 'up' ? 'bg-green-100 text-green-800' :
                              performer.rankChange === 'down' ? 'bg-red-100 text-red-800' :
                                performer.rankChange === 'new' ? 'bg-blue-100 text-blue-800' :
                                  'bg-gray-100 text-gray-800'
                              }`}>
                              {performer.rankChange === 'up' && '↗️'}
                              {performer.rankChange === 'down' && '↘️'}
                              {performer.rankChange === 'new' && '🆕'}
                              {performer.rankChangeValue && Math.abs(performer.rankChangeValue)}
                            </span>
                          )}
                        </div>
                        <div className="text-sm text-gray-500">
                          C: {performer.correctAnswers || 0} | W: {performer.incorrectAnswers || 0} | U: {performer.unanswered || 0}
                        </div>
                      </div>
                    </td>
                    {ranking?.isLive && (
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center space-x-2">
                          <div className={`w-2 h-2 rounded-full ${performer.participantStatus === 'active' ? 'bg-green-500 animate-pulse' :
                            performer.participantStatus === 'completed' ? 'bg-blue-500' :
                              performer.participantStatus === 'paused' ? 'bg-yellow-500' :
                                'bg-gray-500'
                            }`}></div>
                          <span className={`text-xs font-medium capitalize ${performer.participantStatus === 'active' ? 'text-green-700' :
                            performer.participantStatus === 'completed' ? 'text-blue-700' :
                              performer.participantStatus === 'paused' ? 'text-yellow-700' :
                                'text-gray-700'
                            }`}>
                            {performer.participantStatus || 'completed'}
                          </span>
                        </div>
                      </td>
                    )}
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`text-lg font-bold ${getScoreColor(performer.score)}`}>
                        {performer.score.toFixed(1)}%
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className="text-sm text-gray-900">
                        {performer.totalMarks}/{performer.maxMarks}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center space-x-1">
                        <Clock className="h-4 w-4 text-gray-400" />
                        <span className="text-sm text-gray-900">{formatTime(performer.timeTaken)}</span>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className="text-sm font-medium text-gray-900">
                        {performer.percentile.toFixed(1)}%
                      </span>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>

        {filteredRanking.length === 0 && (
          <div className="text-center py-12">
            <Trophy className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">No ranking data available</h3>
            <p className="text-gray-600">Results will appear here once participants complete the exam.</p>
          </div>
        )}
      </div>
    </div>
  );
}

export default Ranking;
