"use client";

import { useState, useEffect } from "react";
import { use<PERSON><PERSON><PERSON>, <PERSON>, useLocation } from "react-router-dom";
import { Trophy, Clock, ArrowLeft, CheckCircle, XCircle, MinusCircle, Loader2, Award, Target, Timer, Users, TrendingUp, Eye, EyeOff } from "lucide-react";
import { API_ENDPOINTS, apiUtils } from "../config/api";
import ResultPDFGenerator from "../components/ResultPDFGenerator";

interface Question {
  id: string;
  question: string;
  options: string[];
  correctAnswer: number | string;
  subject?: string;
  difficulty?: string;
  explanation?: string;
  marks?: number;
}

interface DetailedAnswer {
  questionId: string;
  question: string;
  options: string[];
  correctAnswer: number;
  userAnswer: string | null;
  isCorrect: boolean;
  isAttempted: boolean;
  subject: string;
  difficulty: string;
}

interface ExamResult {
    id: string;
    examId: string;
    userId: string;
    examTitle: string;
    score: number;
    totalQuestions: number;
    totalMarks: number;
    correctAnswers: number;
    incorrectAnswers: number;
    unanswered: number;
    timeTakenMinutes: string;
    rank?: number | null;
    percentile?: number | null;
    subjects?: string | null;
    completedAt: string;
    detailedAnswers?: DetailedAnswer[];
}

interface SubmissionPayload {
  examId: string;
  userId: string;
  timeTakenMinutes: string;
  answers: { [key: string]: string };
  detailedAnswers: DetailedAnswer[];
  examTitle: string;
  examDuration: number;
  antiCheatWarnings: any[];
  tabSwitchCount: number;
}

interface TopPerformer {
  rank: number;
  name: string;
  email?: string | null;
  score?: number | null;
  totalMarks?: number | null;
  maxMarks?: number | null;
  timeTaken?: string | null;
  correctAnswers: number;
  incorrectAnswers?: number | null;
  unanswered?: number | null;
  percentile?: number | null;
  completedAt?: string | null;
}

interface ExamStatistics {
  averageScore: number;
  averageTime: number;
  highestScore: number;
  lowestScore: number;
  passPercentage: number;
  totalParticipants: number;
}

interface LeaderboardData {
  examId: string;
  examTitle: string;
  totalParticipants: number;
  topPerformers: TopPerformer[];
  statistics: ExamStatistics;
  lastUpdated: string;
}

function Results() {
  const { resultId } = useParams<{ resultId: string }>();
  const location = useLocation();
  const [result, setResult] = useState<ExamResult | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState("");
  const [showLeaderboard, setShowLeaderboard] = useState(false);
  const [leaderboardData, setLeaderboardData] = useState<LeaderboardData | null>(null);
  const [leaderboardLoading, setLeaderboardLoading] = useState(false);

  // Get additional data from navigation state
  const navigationState = location.state as {
    questions?: Question[];
    submissionPayload?: SubmissionPayload;
    submissionResponse?: any;
  } | null;

  useEffect(() => {
    if (!resultId) {
      setError("No result ID found in URL.");
      setLoading(false);
      return;
    }

    const fetchResultData = async () => {
        try {
            setLoading(true);
            const resultResponse = await apiUtils.get(`${API_ENDPOINTS.EXAM_RESULTS}/${resultId}`);
            if (!resultResponse.ok) {
              const errorData = await resultResponse.json().catch(() => ({ message: "Could not load your exam result." }));
              throw new Error(errorData.message);
            }
            const resultData: ExamResult = await resultResponse.json();
            setResult(resultData);
        } catch (err: any) {
            setError(err.message || "An unexpected error occurred.");
        } finally {
            setLoading(false);
        }
    };

    fetchResultData();
  }, [resultId]);

  const fetchLeaderboardData = async () => {
    if (!result?.examId) return;
    
    try {
      setLeaderboardLoading(true);
      const response = await apiUtils.get(`${API_ENDPOINTS.EXAM_RESULTS}/ranking/${result.examId}`);
      if (!response.ok) {
        throw new Error("Failed to load leaderboard data");
      }
      const data: LeaderboardData = await response.json();
      setLeaderboardData(data);
    } catch (err: any) {
      console.error("Error fetching leaderboard:", err);
    } finally {
      setLeaderboardLoading(false);
    }
  };

  const toggleLeaderboard = async () => {
    if (!showLeaderboard && !leaderboardData) {
      await fetchLeaderboardData();
    }
    setShowLeaderboard(!showLeaderboard);
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center bg-white rounded-xl shadow-lg p-8 max-w-sm w-full mx-4">
          <Loader2 className="animate-spin h-12 w-12 text-blue-600 mx-auto mb-4" />
          <h3 className="text-lg font-semibold text-gray-900 mb-2">Generating Report</h3>
          <p className="text-gray-600">Please wait while we prepare your results...</p>
        </div>
      </div>
    );
  }

  if (error || !result) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center text-center p-4">
        <div className="bg-white rounded-xl shadow-lg p-8 max-w-md w-full">
          <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <XCircle className="h-8 w-8 text-red-600" />
          </div>
          <h3 className="text-xl font-semibold text-gray-900 mb-2">Results Not Found</h3>
          <p className="text-red-600 mb-6">{error || "Results not found."}</p>
          <Link 
            to="/app/exams" 
            className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors font-medium"
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Exams
          </Link>
        </div>
      </div>
    );
  }

  const getScoreColor = (score: number) => {
    const percentage = result.totalMarks > 0 ? (score / result.totalMarks) * 100 : 0;
    if (percentage >= 75) return "text-green-600";
    if (percentage >= 40) return "text-yellow-600";
    return "text-red-600";
  };
  
  const getGrade = (score: number) => {
    const percentage = result.totalMarks > 0 ? (score / result.totalMarks) * 100 : 0;
    if (percentage >= 90) return "A+";
    if (percentage >= 80) return "A";
    if (percentage >= 70) return "B+";
    if (percentage >= 60) return "B";
    if (percentage >= 50) return "C";
    return "F";
  };

  const getGradeColor = (score: number) => {
    const percentage = result.totalMarks > 0 ? (score / result.totalMarks) * 100 : 0;
    if (percentage >= 75) return "bg-green-100 text-green-800 border-green-200";
    if (percentage >= 40) return "bg-yellow-100 text-yellow-800 border-yellow-200";
    return "bg-red-100 text-red-800 border-red-200";
  };

  const getRankColor = (rank: number) => {
    if (rank === 1) return "bg-yellow-100 text-yellow-800 border-yellow-300";
    if (rank === 2) return "bg-gray-100 text-gray-800 border-gray-300";
    if (rank === 3) return "bg-orange-100 text-orange-800 border-orange-300";
    return "bg-blue-50 text-blue-800 border-blue-200";
  };

  const getRankIcon = (rank: number) => {
    if (rank === 1) return "🥇";
    if (rank === 2) return "🥈";
    if (rank === 3) return "🥉";
    return rank.toString();
  };
  
  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="mb-6">
          <Link to="/app/exams" className="flex items-center text-gray-600 hover:text-gray-900 font-medium transition-colors">
            <ArrowLeft className="h-5 w-5 mr-2" />
            Back to Exams
          </Link>
        </div>

        {/* Main Results Card */}
        <div className="bg-white rounded-2xl shadow-sm border overflow-hidden mb-8">
          {/* Header Section */}
          <div className="bg-gradient-to-r from-blue-600 to-purple-600 text-white p-8 text-center">
            <Trophy className="h-16 w-16 mx-auto mb-4 opacity-90" />
            <h1 className="text-3xl font-bold mb-2">Exam Results</h1>
            <p className="text-blue-100 text-lg">{result.examTitle}</p>
            <p className="text-blue-200 text-sm mt-2">
              Completed on {new Date(result.completedAt).toLocaleDateString('en-US', {
                weekday: 'long',
                year: 'numeric',
                month: 'long',
                day: 'numeric'
              })}
            </p>
          </div>

          {/* Score Overview */}
          <div className="p-8">
            <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
              <div className="text-center p-6 bg-blue-50 rounded-xl border border-blue-100">
                <div className={`text-4xl font-bold mb-2 ${getScoreColor(result.score)}`}>
                  {result.score}
                </div>
                <div className="text-sm text-gray-600 mb-1">Your Score</div>
                <div className="text-xs text-gray-500">out of {result.totalMarks}</div>
              </div>
              
              <div className="text-center p-6 bg-gradient-to-br from-purple-50 to-pink-50 rounded-xl border border-purple-100">
                <div className={`text-4xl font-bold mb-2 px-3 py-1 rounded-lg border-2 inline-block ${getGradeColor(result.score)}`}>
                  {getGrade(result.score)}
                </div>
                <div className="text-sm text-gray-600 mb-1">Grade</div>
                <div className="text-xs text-gray-500">
                  {((result.score / result.totalMarks) * 100).toFixed(1)}%
                </div>
              </div>
              
              <div className="text-center p-6 bg-green-50 rounded-xl border border-green-100">
                <div className="text-4xl font-bold text-green-600 mb-2">
                  {result.correctAnswers}
                </div>
                <div className="text-sm text-gray-600 mb-1">Correct</div>
                <div className="text-xs text-gray-500">answers</div>
              </div>
              
              <div className="text-center p-6 bg-orange-50 rounded-xl border border-orange-100">
                <div className="text-4xl font-bold text-orange-600 mb-2">
                  {result.timeTakenMinutes}
                </div>
                <div className="text-sm text-gray-600 mb-1">Minutes</div>
                <div className="text-xs text-gray-500">time taken</div>
              </div>
            </div>

            {/* Performance Breakdown */}
            <div className="bg-gray-50 rounded-xl p-6 mb-8">
              <h3 className="text-lg font-semibold text-gray-900 mb-6 flex items-center">
                <Target className="h-5 w-5 mr-2 text-blue-600" />
                Performance Breakdown
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div className="text-center">
                  <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-3">
                    <CheckCircle className="h-8 w-8 text-green-600"/>
                  </div>
                  <p className="text-3xl font-bold text-green-600 mb-1">{result.correctAnswers}</p>
                  <p className="text-sm text-gray-600 mb-2">Correct Answers</p>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div 
                      className="bg-green-500 h-2 rounded-full transition-all duration-500"
                      style={{ width: `${(result.correctAnswers / result.totalQuestions) * 100}%` }}
                    ></div>
                  </div>
                </div>
                
                <div className="text-center">
                  <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-3">
                    <XCircle className="h-8 w-8 text-red-600"/>
                  </div>
                  <p className="text-3xl font-bold text-red-600 mb-1">{result.incorrectAnswers}</p>
                  <p className="text-sm text-gray-600 mb-2">Incorrect Answers</p>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div 
                      className="bg-red-500 h-2 rounded-full transition-all duration-500"
                      style={{ width: `${(result.incorrectAnswers / result.totalQuestions) * 100}%` }}
                    ></div>
                  </div>
                </div>
                
                <div className="text-center">
                  <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-3">
                    <MinusCircle className="h-8 w-8 text-gray-600"/>
                  </div>
                  <p className="text-3xl font-bold text-gray-600 mb-1">{result.unanswered}</p>
                  <p className="text-sm text-gray-600 mb-2">Unanswered</p>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div 
                      className="bg-gray-500 h-2 rounded-full transition-all duration-500"
                      style={{ width: `${(result.unanswered / result.totalQuestions) * 100}%` }}
                    ></div>
                  </div>
                </div>
              </div>
            </div>

            {/* Question Analysis */}
            {result.detailedAnswers && result.detailedAnswers.length > 0 && (
              <div className="mb-8">
                <h3 className="text-lg font-semibold text-gray-900 mb-6 flex items-center">
                  <Award className="h-5 w-5 mr-2 text-purple-600" />
                  Question Analysis ({result.detailedAnswers.length} Questions)
                </h3>
                <div className="space-y-6 max-h-96 overflow-y-auto pr-2">
                  {result.detailedAnswers.map((answer, index) => (
                    <div key={answer.questionId} className="border border-gray-200 rounded-xl p-6 bg-white">
                      <div className="flex items-start justify-between mb-4">
                        <div className="flex items-center space-x-3">
                          <div className="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center text-white font-semibold text-sm">
                            {index + 1}
                          </div>
                          <div>
                            <span className="text-sm text-gray-500">Question {index + 1}</span>
                            <div className="flex items-center space-x-2 mt-1">
                              <span className="text-xs bg-gray-100 text-gray-600 px-2 py-1 rounded">
                                {answer.subject}
                              </span>
                              <span className="text-xs bg-blue-100 text-blue-600 px-2 py-1 rounded">
                                {answer.difficulty}
                              </span>
                            </div>
                          </div>
                        </div>
                        <div className={`px-3 py-1 rounded-full text-xs font-semibold ${
                          answer.isCorrect 
                            ? 'bg-green-100 text-green-800' 
                            : answer.isAttempted 
                              ? 'bg-red-100 text-red-800' 
                              : 'bg-gray-100 text-gray-800'
                        }`}>
                          {answer.isCorrect ? '✓ Correct' : answer.isAttempted ? '✗ Incorrect' : '- Unanswered'}
                        </div>
                      </div>
                      
                      <p className="text-gray-900 font-medium mb-4 leading-relaxed">
                        {answer.question}
                      </p>
                      
                      <div className="space-y-2">
                        {answer.options.map((option, optionIndex) => {
                          const isCorrect = optionIndex === answer.correctAnswer;
                          const isUserAnswer = answer.userAnswer === option;
                          
                          return (
                            <div 
                              key={optionIndex}
                              className={`flex items-center p-3 rounded-lg border-2 ${
                                isCorrect && isUserAnswer
                                  ? 'border-green-500 bg-green-50'
                                  : isCorrect
                                    ? 'border-green-300 bg-green-50'
                                    : isUserAnswer
                                      ? 'border-red-300 bg-red-50'
                                      : 'border-gray-200 bg-gray-50'
                              }`}
                            >
                              <div className={`w-6 h-6 rounded-full border-2 mr-3 flex items-center justify-center text-xs font-bold ${
                                isCorrect && isUserAnswer
                                  ? 'border-green-500 bg-green-500 text-white'
                                  : isCorrect
                                    ? 'border-green-500 bg-green-500 text-white'
                                    : isUserAnswer
                                      ? 'border-red-500 bg-red-500 text-white'
                                      : 'border-gray-300 bg-white text-gray-600'
                              }`}>
                                {String.fromCharCode(65 + optionIndex)}
                              </div>
                              <span className={`flex-grow ${
                                isCorrect ? 'font-semibold text-green-800' : 
                                isUserAnswer ? 'font-semibold text-red-800' : 'text-gray-700'
                              }`}>
                                {option}
                              </span>
                              {isCorrect && (
                                <CheckCircle className="h-5 w-5 text-green-600 ml-2" />
                              )}
                              {isUserAnswer && !isCorrect && (
                                <XCircle className="h-5 w-5 text-red-600 ml-2" />
                              )}
                            </div>
                          );
                        })}
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Action Buttons */}
            <div className="flex flex-col sm:flex-row gap-4 justify-center pt-6 border-t border-gray-200">
              <ResultPDFGenerator 
                result={result} 
                questions={navigationState?.questions || []}
                submissionPayload={navigationState?.submissionPayload}
                submissionResponse={navigationState?.submissionResponse}
              />
              
              <button
                onClick={toggleLeaderboard}
                disabled={leaderboardLoading}
                className="bg-yellow-100 text-yellow-800 px-6 py-3 rounded-lg hover:bg-yellow-200 transition-colors flex items-center justify-center space-x-2 font-medium disabled:opacity-50"
              >
                {leaderboardLoading ? (
                  <Loader2 className="h-5 w-5 animate-spin" />
                ) : showLeaderboard ? (
                  <EyeOff className="h-5 w-5" />
                ) : (
                  <Eye className="h-5 w-5" />
                )}
                <span>
                  {leaderboardLoading ? 'Loading...' : showLeaderboard ? 'Hide Leaderboard' : 'View Leaderboard'}
                </span>
              </button>
              
              <Link 
                to="/app/exams" 
                className="bg-gray-200 text-gray-800 px-6 py-3 rounded-lg hover:bg-gray-300 transition-colors text-center font-medium"
              >
                Take Another Exam
              </Link>
            </div>
          </div>
        </div>

        {/* Leaderboard Section */}
        {showLeaderboard && leaderboardData && (
          <div className="bg-white rounded-2xl shadow-sm border overflow-hidden">
            <div className="bg-gradient-to-r from-yellow-500 to-orange-500 text-white p-6">
              <div className="flex items-center justify-center mb-2">
                <Trophy className="h-8 w-8 mr-3" />
                <h2 className="text-2xl font-bold">Exam Leaderboard</h2>
              </div>
              <p className="text-center text-yellow-100">{leaderboardData.examTitle}</p>
            </div>

            <div className="p-6">
              {/* Statistics Overview */}
              <div className="mb-8">
                <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                  <TrendingUp className="h-5 w-5 mr-2 text-blue-600" />
                  Exam Statistics
                </h3>
                <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4">
                  <div className="bg-blue-50 rounded-lg p-4 text-center border border-blue-100">
                    <div className="text-2xl font-bold text-blue-600">{leaderboardData.statistics.totalParticipants}</div>
                    <div className="text-sm text-gray-600 mt-1">Total Participants</div>
                  </div>
                  <div className="bg-green-50 rounded-lg p-4 text-center border border-green-100">
                    <div className="text-2xl font-bold text-green-600">{leaderboardData.statistics.averageScore.toFixed(1)}</div>
                    <div className="text-sm text-gray-600 mt-1">Average Score</div>
                  </div>
                  <div className="bg-purple-50 rounded-lg p-4 text-center border border-purple-100">
                    <div className="text-2xl font-bold text-purple-600">{leaderboardData.statistics.averageTime}</div>
                    <div className="text-sm text-gray-600 mt-1">Avg Time (min)</div>
                  </div>
                  <div className="bg-yellow-50 rounded-lg p-4 text-center border border-yellow-100">
                    <div className="text-2xl font-bold text-yellow-600">{leaderboardData.statistics.highestScore}</div>
                    <div className="text-sm text-gray-600 mt-1">Highest Score</div>
                  </div>
                  <div className="bg-red-50 rounded-lg p-4 text-center border border-red-100">
                    <div className="text-2xl font-bold text-red-600">{leaderboardData.statistics.lowestScore}</div>
                    <div className="text-sm text-gray-600 mt-1">Lowest Score</div>
                  </div>
                  <div className="bg-indigo-50 rounded-lg p-4 text-center border border-indigo-100">
                    <div className="text-2xl font-bold text-indigo-600">{leaderboardData.statistics.passPercentage}%</div>
                    <div className="text-sm text-gray-600 mt-1">Pass Rate</div>
                  </div>
                </div>
              </div>

              {/* Top Performers List */}
              <div>
                <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                  <Users className="h-5 w-5 mr-2 text-purple-600" />
                  Top Performers ({leaderboardData.topPerformers.length} participants)
                </h3>
                <div className="space-y-3">
                  {leaderboardData.topPerformers.map((performer) => (
                    <div 
                      key={`${performer.rank}-${performer.name}`}
                      className="flex items-center justify-between p-4 bg-gray-50 rounded-lg border border-gray-200 hover:bg-gray-100 transition-colors"
                    >
                      <div className="flex items-center space-x-4">
                        <div className={`w-10 h-10 rounded-lg border-2 flex items-center justify-center font-bold text-lg ${getRankColor(performer.rank)}`}>
                          {getRankIcon(performer.rank)}
                        </div>
                        <div>
                          <p className="font-semibold text-gray-900">
                            {/\S+@\S+\.\S+/.test(performer.name) ? "None" : performer.name}
                          </p>
                          <p className="text-sm text-gray-500">Rank #{performer.rank}</p>
                        </div>
                      </div>
                      <div className="text-right">
                        <div className="flex items-center space-x-6">
                          <div className="text-center">
                            <p className="font-bold text-green-600 text-lg">{performer.correctAnswers}</p>
                            <p className="text-xs text-gray-500">Correct</p>
                          </div>
                          {performer.score && (
                            <div className="text-center">
                              <p className="font-bold text-blue-600 text-lg">{performer.score}</p>
                              <p className="text-xs text-gray-500">Score</p>
                            </div>
                          )}
                          {performer.timeTaken && (
                            <div className="text-center">
                              <p className="font-bold text-purple-600 text-lg">{performer.timeTaken}</p>
                              <p className="text-xs text-gray-500">Time</p>
                            </div>
                          )}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}

export default Results;