import React, { useState, useEffect } from 'react';
import { Users, FileText, BookOpen, IndianRupee , Bell } from 'lucide-react';
import { BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, LineChart, Line } from 'recharts';
import { API_ENDPOINTS, apiUtils } from '../../config/api';

function AdminDashboard() {
  const [stats, setStats] = useState([]);
  const [userGrowthData, setUserGrowthData] = useState([]);
  const [recentActivities, setRecentActivities] = useState([]);
  const [loading, setLoading] = useState(true);

  // Icon mapping for stats
  const iconMap = {
    totalUsers: Users,
    activeExams: FileText,
    studyMaterials: BookOpen,
    monthlyRevenue: IndianRupee
  };

  // Colors for stats cards
  const colorMap = {
    totalUsers: 'bg-blue-600',
    activeExams: 'bg-green-600',
    studyMaterials: 'bg-purple-600',
    monthlyRevenue: 'bg-indigo-600'
  };

  useEffect(() => {
    fetchAdminData();
  }, []);

  const fetchAdminData = async () => {
    try {
      setLoading(true);
      const [statsRes, growthRes, activitiesRes] = await Promise.all([
        apiUtils.get(API_ENDPOINTS.DASHBOARD_STATS),
        apiUtils.get(API_ENDPOINTS.USER_GROWTH_DATA),
        apiUtils.get(API_ENDPOINTS.RECENT_ACTIVITIES)
      ]);

      if (statsRes.ok) {
        const statsData = await statsRes.json();
        // Map the API response to the stats format
        const mappedStats = [
          {
            label: 'Total Users',
            value: statsData.totalUsers,
            icon: iconMap.totalUsers,
            color: colorMap.totalUsers,
            change: `+${Math.floor(statsData.totalUsers * 0.05)} this month` // Placeholder change calculation
          },
          {
            label: 'Active Exams',
            value: statsData.activeExams,
            icon: iconMap.activeExams,
            color: colorMap.activeExams,
            change: `+${Math.floor(statsData.activeExams * 0.1)} this month`
          },
          {
            label: 'Study Materials',
            value: statsData.studyMaterials,
            icon: iconMap.studyMaterials,
            color: colorMap.studyMaterials,
            change: `+${Math.floor(statsData.studyMaterials * 0.08)} this month`
          },
          {
            label: 'Monthly Revenue',
            value: `${statsData.monthlyRevenue.toFixed(2)}`,
            icon: iconMap.monthlyRevenue,
            color: colorMap.monthlyRevenue,
            change: '+10% this month'
          }
        ];
        setStats(mappedStats);
      }

      if (growthRes.ok) {
        const growthData = await growthRes.json();
        setUserGrowthData(growthData);
      }

      if (activitiesRes.ok) {
        const activitiesData = await activitiesRes.json();
        setRecentActivities(activitiesData);
      }
    } catch (err) {
      console.error('Error fetching admin data:', err);
    } finally {
      setLoading(false);
    }
  };

  const examData = [
    { name: 'Prelims', completed: 1250, pending: 340 },
    { name: 'Mains', completed: 890, pending: 210 },
    { name: 'Current Affairs', completed: 1560, pending: 120 },
    { name: 'Subject Tests', completed: 980, pending: 180 }
  ];

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Admin Dashboard</h1>
          <p className="text-gray-600 mt-1">Overview of platform performance and user activity</p>
        </div>
        <button className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors flex items-center space-x-2">
          <Bell className="h-4 w-4" />
          <span>Send Notification</span>
        </button>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {stats.map((stat, index) => (
          <div key={index} className="bg-white p-6 rounded-xl shadow-sm border border-gray-200">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">{stat.label}</p>
                <p className="text-2xl font-bold text-gray-900">{stat.value}</p>
                <p className="text-sm text-green-600 font-medium">{stat.change}</p>
              </div>
              <div className={`${stat.color} p-3 rounded-lg`}>
                <stat.icon className="h-6 w-6 text-white" />
              </div>
            </div>
          </div>
        ))}
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* User Growth Chart */}
        <div className="bg-white p-6 rounded-xl shadow-sm border border-gray-200">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">User Growth</h3>
          <ResponsiveContainer width="100%" height={300}>
            <LineChart data={userGrowthData}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="month" />
              <YAxis />
              <Tooltip />
              <Line
                type="monotone"
                dataKey="users"
                stroke="#3B82F6"
                strokeWidth={3}
                dot={{ fill: '#3B82F6', strokeWidth: 2, r: 4 }}
              />
            </LineChart>
          </ResponsiveContainer>
        </div>

        {/* Exam Analytics */}
        <div className="bg-white p-6 rounded-xl shadow-sm border border-gray-200">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Exam Analytics</h3>
          <ResponsiveContainer width="100%" height={300}>
            <BarChart data={examData}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="name" />
              <YAxis />
              <Tooltip />
              <Bar dataKey="completed" fill="#10B981" />
              <Bar dataKey="pending" fill="#F59E0B" />
            </BarChart>
          </ResponsiveContainer>
        </div>
      </div>

      {/* Recent Activities */}
      <div className="bg-white rounded-xl shadow-sm border border-gray-200">
        <div className="p-6 border-b border-gray-200">
          <h3 className="text-lg font-semibold text-gray-900">Recent Activities</h3>
        </div>
        <div className="p-6">
          <div className="space-y-4">
            {recentActivities.map((activity) => (
              <div key={activity.id} className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                <div className="flex items-center space-x-3">
                  <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                  <div>
                    <p className="text-sm font-medium text-gray-900">
                      <span className="font-semibold">{activity.user}</span> {activity.action} <span className="font-semibold">{activity.item}</span>
                    </p>
                    <p className="text-xs text-gray-500">{activity.time}</p>
                  </div>
                </div>
              </div>
            ))}
          </div>
          <div className="mt-4 text-center">
            <button className="text-blue-600 hover:text-blue-700 text-sm font-medium">
              View all activities
            </button>
          </div>
        </div>
      </div>

      {/* Quick Actions */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="bg-gradient-to-r from-blue-500 to-blue-600 p-6 rounded-xl text-white">
          <h3 className="text-lg font-semibold mb-2">Create New Exam</h3>
          <p className="text-blue-100 text-sm mb-4">Set up a new exam for students</p>
          <button className="bg-white text-blue-600 px-4 py-2 rounded-lg text-sm font-medium hover:bg-blue-50 transition-colors">
            Create Exam
          </button>
        </div>

        <div className="bg-gradient-to-r from-green-500 to-green-600 p-6 rounded-xl text-white">
          <h3 className="text-lg font-semibold mb-2">Upload Materials</h3>
          <p className="text-green-100 text-sm mb-4">Add new study materials</p>
          <button className="bg-white text-green-600 px-4 py-2 rounded-lg text-sm font-medium hover:bg-green-50 transition-colors">
            Upload
          </button>
        </div>

        <div className="bg-gradient-to-r from-purple-500 to-purple-600 p-6 rounded-xl text-white">
          <h3 className="text-lg font-semibold mb-2">User Management</h3>
          <p className="text-purple-100 text-sm mb-4">Manage user accounts</p>
          <button className="bg-white text-purple-600 px-4 py-2 rounded-lg text-sm font-medium hover:bg-purple-50 transition-colors">
            Manage Users
          </button>
        </div>
      </div>
    </div>
  );
}

export default AdminDashboard;