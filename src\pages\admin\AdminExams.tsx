"use client"
import type React from "react"
import { useState, useEffect } from "react"
import { Plus, Search, Filter, Edit, Trash2, Clock, Users, FileText, Upload } from 'lucide-react'
import { API_ENDPOINTS, apiUtils } from "../../config/api"
import { toast } from "react-toastify"
import moment from "moment-timezone"
import EnhancedCsvUploadModal from "./CsvUploadModal"
import CreateExamModal from "./CreateExamModal"

// Type definitions
interface Exam {
  id?: string
  title: string
  description: string
  subject: string
  category: string
  duration: number
  totalQuestions: number
  totalMarks: number
  passingMarks: number
  difficulty: string
  status: string
  startTime: string
  endTime: string
  questionIds: string[]
  instructions: string
  isPremium: string
  basePrice?: number
  offerPrice?: number
  attempts: number
  createdAt?: string
  updatedAt?: string
}

interface Question {
  id?: string
  question: string
  options: string[]
  correctAnswer: string
  explanation: string
  subject: string
  topic: string
  category: string
  difficulty: string
  marks: number
  negativeMarks: number
  questionType: string
  tags: string[]
  imageUrl?: string
  status: string
  examId?: string
  createdAt?: string
  updatedAt?: string
}

export type { Question }

interface ExamFormData {
  title: string
  description: string
  subject: string
  category: string
  duration: string
  totalQuestions: string
  totalMarks: string
  passingMarks: string
  difficulty: string
  status: string
  startDate: string
  startTime: string
  instructions: string
  isPremium: string
  basePrice: string
  offerPrice: string
}

interface QuestionFormData {
  question: string
  options: string[]
  correctAnswer: string
  explanation: string
  subject: string
  topic: string
  category: string
  difficulty: string
  marks: string
  negativeMarks: string
  questionType: string
  tags: string
  imageUrl: string
}

const AdminExams: React.FC = () => {
  const [searchTerm, setSearchTerm] = useState("")
  const [filter, setFilter] = useState("all")
  const [showCreateModal, setShowCreateModal] = useState(false)
  const [showQuestionModal, setShowQuestionModal] = useState(false)
  const [showCsvUploadModal, setShowCsvUploadModal] = useState(false)
  const [selectedExamId, setSelectedExamId] = useState<string | null>(null)
  const [exams, setExams] = useState<Exam[]>([])
  const [loading, setLoading] = useState(true)
  const [showEditModal, setShowEditModal] = useState(false)
  const [editingExam, setEditingExam] = useState<Exam | null>(null)

  const [examFormData, setExamFormData] = useState<ExamFormData>({
    title: "",
    description: "",
    subject: "",
    category: "",
    duration: "",
    totalQuestions: "",
    totalMarks: "",
    passingMarks: "",
    difficulty: "Medium",
    status: "draft",
    startDate: "",
    startTime: "",
    instructions: "",
    isPremium: "Free",
    basePrice: "",
    offerPrice: "",
  })

  const [questionFormData, setQuestionFormData] = useState<QuestionFormData>({
    question: "",
    options: ["", "", "", ""],
    correctAnswer: "",
    explanation: "",
    subject: "",
    topic: "",
    category: "",
    difficulty: "Medium",
    marks: "2",
    negativeMarks: "0.66",
    questionType: "mcq",
    tags: "",
    imageUrl: "",
  })

  useEffect(() => {
    fetchExams()
  }, [])

  const fetchExams = async () => {
    try {
      setLoading(true)
      const response = await apiUtils.get(API_ENDPOINTS.EXAMS)
      if (response.ok) {
        const data = await response.json()
        setExams(data)
      } else {
        toast.error("Failed to fetch exams")
        setExams([])
      }
    } catch (err) {
      console.error("Error fetching exams:", err)
      toast.error("Error fetching exams")
      setExams([])
    } finally {
      setLoading(false)
    }
  }

  const handleExamFormChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target
    if (showEditModal && editingExam) {
      setEditingExam((prev) => ({
        ...prev!,
        [name]: value,
        ...(name === "isPremium" && value === "Free" ? { basePrice: undefined, offerPrice: undefined } : {}),
      }))
    } else {
      setExamFormData((prev) => ({
        ...prev,
        [name]: value,
        ...(name === "isPremium" && value === "Free" ? { basePrice: "", offerPrice: "" } : {}),
      }))
    }
  }

  const handleQuestionFormChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>,
    index?: number,
  ) => {
    const { name, value } = e.target
    if (name === "options" && index !== undefined) {
      setQuestionFormData((prev) => {
        const newOptions = [...prev.options]
        newOptions[index] = value
        return { ...prev, options: newOptions }
      })
    } else {
      setQuestionFormData((prev) => ({ ...prev, [name]: value }))
    }
  }

  const validateExamForm = (formData: ExamFormData): boolean => {
    if (!formData.title.trim()) {
      toast.error("Exam title is required")
      return false
    }
    if (!formData.subject.trim()) {
      toast.error("Subject is required")
      return false
    }
    if (!formData.category.trim()) {
      toast.error("Category is required")
      return false
    }
    if (!formData.duration || isNaN(Number(formData.duration)) || Number(formData.duration) <= 0) {
      toast.error("Valid duration is required")
      return false
    }
    if (!formData.totalMarks || isNaN(Number(formData.totalMarks)) || Number(formData.totalMarks) <= 0) {
      toast.error("Valid total marks are required")
      return false
    }
    if (!formData.passingMarks || isNaN(Number(formData.passingMarks)) || Number(formData.passingMarks) <= 0) {
      toast.error("Valid passing marks are required")
      return false
    }
    if (formData.isPremium === "Premium") {
      if (!formData.basePrice || isNaN(Number(formData.basePrice)) || Number(formData.basePrice) <= 0) {
        toast.error("Valid base price is required for premium exams")
        return false
      }
      if (!formData.offerPrice || isNaN(Number(formData.offerPrice)) || Number(formData.offerPrice) < 0) {
        toast.error("Valid offer price is required for premium exams")
        return false
      }
      if (Number(formData.offerPrice) > Number(formData.basePrice)) {
        toast.error("Offer price cannot be greater than base price")
        return false
      }
    }
    if (!formData.startDate || !formData.startTime) {
      toast.error("Start date and time are required")
      return false
    }
    return true
  }

  const validateQuestionForm = (): boolean => {
    if (!questionFormData.question.trim()) {
      toast.error("Question text is required")
      return false
    }
    if (questionFormData.options.some((opt) => !opt.trim())) {
      toast.error("All options must be filled")
      return false
    }
    if (!questionFormData.correctAnswer.trim()) {
      toast.error("Correct answer is required")
      return false
    }
    if (!questionFormData.subject.trim()) {
      toast.error("Subject is required")
      return false
    }
    if (!questionFormData.marks || isNaN(Number(questionFormData.marks)) || Number(questionFormData.marks) <= 0) {
      toast.error("Valid marks are required")
      return false
    }
    if (
      !questionFormData.negativeMarks ||
      isNaN(Number(questionFormData.negativeMarks)) ||
      Number(questionFormData.negativeMarks) < 0
    ) {
      toast.error("Valid negative marks are required")
      return false
    }
    return true
  }

  const handleCreateExam = async (e: React.FormEvent, publish: boolean) => {
  e.preventDefault()
  if (!validateExamForm(examFormData)) return

  try {
    const startTime = moment
      .tz(`${examFormData.startDate} ${examFormData.startTime}`, "YYYY-MM-DD HH:mm", "Asia/Kolkata")
      .utc()
      .format("YYYY-MM-DDTHH:mm:ss.SSS[Z]")

    const endTime = moment
      .tz(`${examFormData.startDate} ${examFormData.startTime}`, "YYYY-MM-DD HH:mm", "Asia/Kolkata")
      .add(Number(examFormData.duration), "minutes")
      .utc()
      .format("YYYY-MM-DDTHH:mm:ss.SSS[Z]")

    const exam: Omit<Exam, 'id'> = {
      title: examFormData.title,
      description: examFormData.description,
      subject: examFormData.subject,
      category: examFormData.category,
      duration: Number(examFormData.duration),
      totalQuestions: 0,
      totalMarks: Number(examFormData.totalMarks),
      passingMarks: Number(examFormData.passingMarks),
      difficulty: examFormData.difficulty,
      status: publish ? "active" : "draft",
      startTime,
      endTime,
      questionIds: [],
      instructions: examFormData.instructions,
      isPremium: examFormData.isPremium === "Premium" ? 1 : 0, // ✅ Integer mapping
      ...(examFormData.isPremium === "Premium" && {
        basePrice: Number(examFormData.basePrice),
        offerPrice: Number(examFormData.offerPrice),
      }),
      attempts: 0,
    }

    const response = await apiUtils.post(API_ENDPOINTS.EXAMS, exam)
    if (response.ok) {
      const newExam = await response.json()
      setExams((prev) => [...prev, newExam])
      setShowCreateModal(false)
      setExamFormData({
        title: "",
        description: "",
        subject: "",
        category: "",
        duration: "",
        totalQuestions: "",
        totalMarks: "",
        passingMarks: "",
        difficulty: "Medium",
        status: "draft",
        startDate: "",
        startTime: "",
        instructions: "",
        isPremium: "Free",
        basePrice: "",
        offerPrice: "",
      })
      toast.success(`Exam ${publish ? "published" : "saved as draft"} successfully`)
    } else {
      const error = await response.json()
      toast.error(error.message || "Failed to create exam")
    }
  } catch (err) {
    console.error("Error creating exam:", err)
    toast.error("Error creating exam")
  }
}


  const handleOpenEditModal = (exam: Exam) => {
  setEditingExam({
    ...exam,
    startDate: moment(exam.startTime).tz("Asia/Kolkata").format("YYYY-MM-DD"),
    startTime: moment(exam.startTime).tz("Asia/Kolkata").format("HH:mm"),
    basePrice: exam.basePrice != null ? exam.basePrice.toString() : "",
    offerPrice: exam.offerPrice != null ? exam.offerPrice.toString() : "",
  })
  setShowEditModal(true)
}


  const handleUpdateExam = async (e: React.FormEvent) => {
  e.preventDefault()
  if (!editingExam) return

  // Normalize isPremium to string for validation (handles both number and string)
  const isPremiumString =
    typeof editingExam.isPremium === "number"
      ? editingExam.isPremium === 1
        ? "Premium"
        : "Free"
      : editingExam.isPremium

  const validationData: ExamFormData = {
    title: editingExam.title,
    description: editingExam.description,
    subject: editingExam.subject,
    category: editingExam.category,
    duration: editingExam.duration.toString(),
    totalQuestions: editingExam.totalQuestions.toString(),
    totalMarks: editingExam.totalMarks.toString(),
    passingMarks: editingExam.passingMarks.toString(),
    difficulty: editingExam.difficulty,
    status: editingExam.status,
    startDate: editingExam.startDate || "",
    startTime: editingExam.startTime || "",
    instructions: editingExam.instructions,
    isPremium: isPremiumString,
    basePrice:
      editingExam.basePrice !== undefined ? editingExam.basePrice.toString() : "",
    offerPrice:
      editingExam.offerPrice !== undefined ? editingExam.offerPrice.toString() : "",
  }

  if (!validateExamForm(validationData)) return

  try {
    const startTime = moment
      .tz(
        `${editingExam.startDate} ${editingExam.startTime}`,
        "YYYY-MM-DD HH:mm",
        "Asia/Kolkata"
      )
      .utc()
      .format("YYYY-MM-DDTHH:mm:ss.SSS[Z]")

    const endTime = moment
      .tz(
        `${editingExam.startDate} ${editingExam.startTime}`,
        "YYYY-MM-DD HH:mm",
        "Asia/Kolkata"
      )
      .add(Number(editingExam.duration), "minutes")
      .utc()
      .format("YYYY-MM-DDTHH:mm:ss.SSS[Z]")

    // Robust mapping: accept both number (from backend) or string (from form)
    const isPremiumFlag =
      typeof editingExam.isPremium === "number"
        ? editingExam.isPremium === 1
        : editingExam.isPremium === "Premium"
    const isPremiumInt = isPremiumFlag ? 1 : 0

    const updatedExamPayload: Exam = {
      ...editingExam,
      isPremium: isPremiumInt, // send integer to backend
      startTime,
      endTime,
      totalQuestions: editingExam.questionIds.length,
      ...(isPremiumFlag && {
        basePrice: Number(editingExam.basePrice),
        offerPrice: Number(editingExam.offerPrice),
      }),
      ...(!isPremiumFlag && {
        basePrice: undefined,
        offerPrice: undefined,
      }),
    }

    const response = await apiUtils.put(
      `${API_ENDPOINTS.EXAMS}/${editingExam.id}`,
      updatedExamPayload
    )
    if (response.ok) {
      const updatedExam = await response.json()
      setExams(exams.map((ex) => (ex.id === updatedExam.id ? updatedExam : ex)))
      setShowEditModal(false)
      setEditingExam(null)
      toast.success("Exam updated successfully")
    } else {
      const error = await response.json()
      toast.error(error.message || "Failed to update exam")
    }
  } catch (err) {
    console.error("Error updating exam:", err)
    toast.error("Error updating exam")
  }
}


  const handleAddQuestion = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!validateQuestionForm() || !selectedExamId) return

    try {
      const question: Omit<Question, 'id'> = {
        question: questionFormData.question,
        options: questionFormData.options,
        correctAnswer: questionFormData.correctAnswer,
        explanation: questionFormData.explanation,
        subject: questionFormData.subject,
        topic: questionFormData.topic,
        category: questionFormData.category,
        difficulty: questionFormData.difficulty,
        marks: Number(questionFormData.marks),
        negativeMarks: Number(questionFormData.negativeMarks),
        questionType: questionFormData.questionType,
        tags: questionFormData.tags.split(",").map((tag) => tag.trim()).filter((tag) => tag),
        imageUrl: questionFormData.imageUrl || undefined,
        status: "active",
        examId: selectedExamId,
      }

      const response = await apiUtils.post(API_ENDPOINTS.QUESTIONS, question)
      if (response.ok) {
        const newQuestion = await response.json()
        setExams(prevExams => prevExams.map(exam => {
          if (exam.id === selectedExamId) {
            const updatedQuestionIds = [...exam.questionIds, newQuestion.id]
            return {
              ...exam,
              questionIds: updatedQuestionIds,
              totalQuestions: updatedQuestionIds.length,
            }
          }
          return exam
        }))
        setShowQuestionModal(false)
        setQuestionFormData({
          question: "",
          options: ["", "", "", ""],
          correctAnswer: "",
          explanation: "",
          subject: "",
          topic: "",
          category: "",
          difficulty: "Medium",
          marks: "2",
          negativeMarks: "0.66",
          questionType: "mcq",
          tags: "",
          imageUrl: "",
        })
        toast.success("Question added successfully and exam updated!")
      } else {
        const error = await response.json()
        toast.error(error.message || "Failed to add question")
      }
    } catch (err) {
      console.error("Error adding question:", err)
      toast.error("Error adding question")
    }
  }

  const handleDeleteExam = async (id: string) => {
    if (!window.confirm("Are you sure you want to delete this exam? This action cannot be undone.")) return
    try {
      const response = await apiUtils.delete(`${API_ENDPOINTS.EXAMS}/${id}`)
      if (response.ok) {
        setExams((prev) => prev.filter((exam) => exam.id !== id))
        toast.success("Exam deleted successfully")
      } else {
        toast.error("Failed to delete exam")
      }
    } catch (err) {
      console.error("Error deleting exam:", err)
      toast.error("Error deleting exam")
    }
  }

  const handleQuestionsAdded = (questions: Question[]) => {
    setExams((prev) =>
      prev.map((exam) =>
        exam.id === selectedExamId
          ? {
              ...exam,
              questionIds: [...exam.questionIds, ...questions.map((q) => q.id!)],
              totalQuestions: exam.questionIds.length + questions.length,
            }
          : exam,
      ),
    )
    setShowCsvUploadModal(false)
    setSelectedExamId(null)
    toast.success(`Successfully added ${questions.length} questions to the exam!`)
  }

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "active":
        return <span className="bg-green-100 text-green-800 px-2 py-1 rounded-full text-xs font-medium">Active</span>
      case "draft":
        return <span className="bg-yellow-100 text-yellow-800 px-2 py-1 rounded-full text-xs font-medium">Draft</span>
      case "completed":
        return <span className="bg-gray-100 text-gray-800 px-2 py-1 rounded-full text-xs font-medium">Completed</span>
      default:
        return null
    }
  }

  const filteredExams = exams.filter((exam) => {
    const matchesSearch =
      exam.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
      exam.description.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesFilter =
      filter === "all" || exam.status === filter || exam.category.toLowerCase() === filter.toLowerCase()
    return matchesSearch && matchesFilter
  })

  return (
    <div className="space-y-6">
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Exam Management</h1>
          <p className="text-gray-600 mt-1">Create and manage exams for students</p>
        </div>
        <button
          onClick={() => setShowCreateModal(true)}
          className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors flex items-center space-x-2 mt-4 sm:mt-0"
        >
          <Plus className="h-4 w-4" />
          <span>Create Exam</span>
        </button>
      </div>

      <div className="bg-white p-6 rounded-xl shadow-sm border border-gray-200">
        <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0">
          <div className="flex items-center space-x-4">
            <Filter className="h-5 w-5 text-gray-600" />
            <div className="flex space-x-2">
              {["all", "active", "draft", "Prelims", "Mains", "Mock Test"].map((filterOption) => (
                <button
                  key={filterOption}
                  onClick={() => setFilter(filterOption)}
                  className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
                    filter === filterOption ? "bg-blue-600 text-white" : "bg-gray-100 text-gray-600 hover:bg-gray-200"
                  }`}
                >
                  {filterOption}
                </button>
              ))}
            </div>
          </div>
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
            <input
              type="text"
              placeholder="Search exams..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent w-64"
            />
          </div>
        </div>
      </div>

      {loading ? (
        <div className="flex justify-center items-center py-12">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
          <span className="ml-2">Loading exams...</span>
        </div>
      ) : filteredExams.length === 0 ? (
        <div className="text-center py-12">
          <FileText className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <p className="text-gray-500">No exams found</p>
        </div>
      ) : (
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {filteredExams.map((exam) => (
            <div
              key={exam.id}
              className="bg-white rounded-xl shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow"
            >
              <div className="flex items-start justify-between mb-4">
                <div className="flex-1">
                  <div className="flex items-center space-x-3 mb-2">
                    <h3 className="text-lg font-semibold text-gray-900">{exam.title}</h3>
                    {getStatusBadge(exam.status)}
                  </div>
                  <p className="text-gray-600 text-sm mb-3">{exam.description}</p>
                  <div className="flex items-center space-x-4 text-sm text-gray-500">
                    <div className="flex items-center">
                      <Clock className="h-4 w-4 mr-1" />
                      {exam.duration} min
                    </div>
                    <div className="flex items-center">
                      <FileText className="h-4 w-4 mr-1" />
                      {exam.totalQuestions} questions
                    </div>
                    <div className="flex items-center">
                      <Users className="h-4 w-4 mr-1" />
                      {exam.attempts} attempts
                    </div>
                  </div>
                </div>
              </div>
              <div className="border-t border-gray-200 pt-4">
                <div className="flex items-center justify-between text-sm text-gray-500 mb-4">
                  <span>Category: {exam.category}</span>
                  <span>Created: {moment(exam.createdAt).format("YYYY-MM-DD")}</span>
                </div>
                <div className="flex items-center justify-between text-sm text-gray-500 mb-4">
                  <span>Scheduled: {moment(exam.startTime).format("YYYY-MM-DD HH:mm")}</span>
                  <span>Premium: {exam.isPremium === "Premium" ? "Yes" : "No"}</span>
                </div>
                <div className="flex flex-wrap gap-2">
                  <button
                    onClick={() => {
                      setSelectedExamId(exam.id!)
                      setShowQuestionModal(true)
                    }}
                    className="flex items-center space-x-1 px-3 py-2 text-blue-600 hover:bg-blue-50 rounded-lg transition-colors text-sm"
                  >
                    <Plus className="h-4 w-4" />
                    <span>Add Question</span>
                  </button>
                  <button
                    onClick={() => {
                      setSelectedExamId(exam.id!)
                      setShowCsvUploadModal(true)
                    }}
                    className="flex items-center space-x-1 px-3 py-2 text-purple-600 hover:bg-purple-50 rounded-lg transition-colors text-sm"
                  >
                    <Upload className="h-4 w-4" />
                    <span>Upload CSV</span>
                  </button>
                  <button
                    onClick={() => handleOpenEditModal(exam)}
                    className="flex items-center space-x-1 px-3 py-2 text-green-600 hover:bg-green-50 rounded-lg transition-colors text-sm"
                  >
                    <Edit className="h-4 w-4" />
                    <span>Edit</span>
                  </button>
                  <button
                    onClick={() => handleDeleteExam(exam.id!)}
                    className="flex items-center space-x-1 px-3 py-2 text-red-600 hover:bg-red-50 rounded-lg transition-colors text-sm"
                  >
                    <Trash2 className="h-4 w-4" />
                    <span>Delete</span>
                  </button>
                </div>
              </div>
            </div>
          ))}
        </div>
      )}

      {/* Create Exam Modal */}
      <CreateExamModal
        isOpen={showCreateModal}
        onClose={() => setShowCreateModal(false)}
        examFormData={examFormData}
        onFormChange={handleExamFormChange}
        onCreateExam={handleCreateExam}
      />

      {/* Edit Exam Modal */}
      {showEditModal && editingExam && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-xl p-6 max-w-2xl w-full mx-4 max-h-[90vh] overflow-y-auto">
            <div className="flex justify-between items-center mb-6">
              <h3 className="text-xl font-semibold text-gray-900">Edit Exam</h3>
              <button onClick={() => setShowEditModal(false)} className="text-gray-400 hover:text-gray-600">×</button>
            </div>
            <form className="space-y-6" onSubmit={handleUpdateExam}>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Exam Title</label>
                  <input
                    type="text"
                    name="title"
                    value={editingExam.title}
                    onChange={handleExamFormChange}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Category</label>
                  <select
                    name="category"
                    value={editingExam.category}
                    onChange={handleExamFormChange}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg"
                  >
                    <option value="Prelims">Prelims</option>
                    <option value="Mains">Mains</option>
                    <option value="Mock Test">Mock Test</option>
                  </select>
                </div>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Description</label>
                <textarea
                  name="description"
                  value={editingExam.description}
                  onChange={handleExamFormChange}
                  rows={3}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg"
                />
              </div>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Duration (minutes)</label>
                  <input
                    type="number"
                    name="duration"
                    value={editingExam.duration}
                    onChange={handleExamFormChange}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Status</label>
                  <select
                    name="status"
                    value={editingExam.status}
                    onChange={handleExamFormChange}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg"
                  >
                    <option value="draft">Draft</option>
                    <option value="active">Active</option>
                    <option value="completed">Completed</option>
                  </select>
                </div>
              </div>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Subject</label>
                  <input
                    type="text"
                    name="subject"
                    value={editingExam.subject}
                    onChange={handleExamFormChange}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Premium</label>
                  <select
                    name="isPremium"
                    value={editingExam.isPremium}
                    onChange={handleExamFormChange}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg"
                  >
                    <option value="Free">Free</option>
                    <option value="Premium">Premium</option>
                  </select>
                </div>
              </div>
              {editingExam.isPremium === "Premium" && (
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Base Price</label>
                    <input
                      type="number"
                      name="basePrice"
                      value={editingExam.basePrice !== undefined ? editingExam.basePrice : ""}
                      onChange={handleExamFormChange}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Offer Price</label>
                    <input
                      type="number"
                      name="offerPrice"
                      value={editingExam.offerPrice !== undefined ? editingExam.offerPrice : ""}
                      onChange={handleExamFormChange}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg"
                    />
                  </div>
                </div>
              )}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Start Date</label>
                  <input
                    type="date"
                    name="startDate"
                    value={editingExam.startDate || ""}
                    onChange={handleExamFormChange}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Start Time</label>
                  <input
                    type="time"
                    name="startTime"
                    value={editingExam.startTime || ""}
                    onChange={handleExamFormChange}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg"
                  />
                </div>
              </div>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Total Marks</label>
                  <input
                    type="number"
                    name="totalMarks"
                    value={editingExam.totalMarks}
                    onChange={handleExamFormChange}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Passing Marks</label>
                  <input
                    type="number"
                    name="passingMarks"
                    value={editingExam.passingMarks}
                    onChange={handleExamFormChange}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg"
                  />
                </div>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Instructions</label>
                <textarea
                  name="instructions"
                  value={editingExam.instructions}
                  onChange={handleExamFormChange}
                  rows={3}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg"
                />
              </div>
              <div className="flex space-x-3">
                <button
                  type="button"
                  onClick={() => setShowEditModal(false)}
                  className="flex-1 px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors"
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  className="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                >
                  Update Exam
                </button>
              </div>
            </form>
          </div>
        </div>
      )}

    {/* Add Question Modal */}
{showQuestionModal && (
  <div className="fixed inset-0 flex items-center justify-center z-50 pointer-events-none">
    <div className="relative bg-white/90 backdrop-blur-md border border-gray-200 rounded-2xl shadow-xl p-8 max-w-3xl w-full mx-4 max-h-[90vh] overflow-y-auto transition-all duration-300 pointer-events-auto">
      {/* Header */}
      <div className="flex justify-between items-center mb-5 border-b border-gray-200 pb-3">
        <h3 className="text-xl font-bold text-gray-900">➕ Add Question</h3>
        <button
          onClick={() => setShowQuestionModal(false)}
          className="text-gray-400 hover:text-red-500 transition-colors text-2xl leading-none"
        >
          ×
        </button>
      </div>

      {/* Form */}
      <form className="space-y-5" onSubmit={handleAddQuestion}>
        {/* Question */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">Question</label>
          <textarea
            name="question"
            value={questionFormData.question}
            onChange={handleQuestionFormChange}
            rows={3}
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition text-sm"
            placeholder="Enter question text"
          />
        </div>

        {/* Options */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">Options</label>
          {questionFormData.options.map((option, index) => (
            <input
              key={index}
              type="text"
              name="options"
              value={option}
              onChange={(e) => handleQuestionFormChange(e, index)}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition text-sm mb-2"
              placeholder={`Option ${index + 1}`}
            />
          ))}
        </div>

        {/* Correct Answer */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">Correct Answer</label>
          <input
            type="text"
            name="correctAnswer"
            value={questionFormData.correctAnswer}
            onChange={handleQuestionFormChange}
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500 transition text-sm"
            placeholder="Enter correct answer"
          />
        </div>

        {/* Explanation */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">Explanation</label>
          <textarea
            name="explanation"
            value={questionFormData.explanation}
            onChange={handleQuestionFormChange}
            rows={2}
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500 transition text-sm"
            placeholder="Enter explanation"
          />
        </div>

        {/* Subject & Topic */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Subject</label>
            <input
              type="text"
              name="subject"
              value={questionFormData.subject}
              onChange={handleQuestionFormChange}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition text-sm"
              placeholder="Enter subject"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Topic</label>
            <input
              type="text"
              name="topic"
              value={questionFormData.topic}
              onChange={handleQuestionFormChange}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition text-sm"
              placeholder="Enter topic"
            />
          </div>
        </div>

        {/* Category & Difficulty */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Category</label>
            <select
              name="category"
              value={questionFormData.category}
              onChange={handleQuestionFormChange}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition text-sm"
            >
              <option value="">Select category</option>
              <option value="Prelims">Prelims</option>
              <option value="Mains">Mains</option>
              <option value="Mock Test">Mock Test</option>
            </select>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Difficulty</label>
            <select
              name="difficulty"
              value={questionFormData.difficulty}
              onChange={handleQuestionFormChange}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500 transition text-sm"
            >
              <option value="Easy">Easy</option>
              <option value="Medium">Medium</option>
              <option value="Hard">Hard</option>
            </select>
          </div>
        </div>

        {/* Marks & Negative Marks */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Marks</label>
            <input
              type="number"
              name="marks"
              value={questionFormData.marks}
              onChange={handleQuestionFormChange}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition text-sm"
              placeholder="2"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Negative Marks</label>
            <input
              type="number"
              name="negativeMarks"
              value={questionFormData.negativeMarks}
              onChange={handleQuestionFormChange}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-red-500 transition text-sm"
              placeholder="0.66"
            />
          </div>
        </div>

        {/* Question Type */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">Question Type</label>
          <select
            name="questionType"
            value={questionFormData.questionType}
            onChange={handleQuestionFormChange}
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition text-sm"
          >
            <option value="mcq">Multiple Choice</option>
            <option value="truefalse">True/False</option>
            <option value="shortanswer">Short Answer</option>
          </select>
        </div>

        {/* Tags */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">Tags (comma-separated)</label>
          <input
            type="text"
            name="tags"
            value={questionFormData.tags}
            onChange={handleQuestionFormChange}
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-teal-500 transition text-sm"
            placeholder="e.g., math, algebra"
          />
        </div>

        {/* Image URL */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">Image URL (optional)</label>
          <input
            type="text"
            name="imageUrl"
            value={questionFormData.imageUrl}
            onChange={handleQuestionFormChange}
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-pink-500 transition text-sm"
            placeholder="Enter image URL"
          />
        </div>

        {/* Actions */}
        <div className="flex justify-end gap-3 pt-4">
          <button
            type="button"
            onClick={() => setShowQuestionModal(false)}
            className="px-5 py-2 border border-gray-300 rounded-lg text-gray-700 font-medium hover:bg-gray-100 transition"
          >
            Cancel
          </button>
          <button
            type="submit"
            className="px-6 py-2 bg-gradient-to-r from-blue-600 to-blue-700 text-white rounded-lg font-semibold shadow hover:from-blue-700 hover:to-blue-800 transition"
          >
            Add Question
          </button>
        </div>
      </form>
    </div>
  </div>
)}

      {/* Enhanced CSV Upload Modal */}
      {showCsvUploadModal && selectedExamId && (
        <EnhancedCsvUploadModal
          examId={selectedExamId}
          onClose={() => {
            setShowCsvUploadModal(false)
            setSelectedExamId(null)
          }}
          onQuestionsAdded={handleQuestionsAdded}
        />
      )}
    </div>
  )
}

export default AdminExams
