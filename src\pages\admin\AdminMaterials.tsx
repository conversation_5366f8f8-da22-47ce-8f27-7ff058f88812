import React, { useEffect, useMemo, useState, useCallback } from "react"
import { Search, Filter, Edit, Trash2, Eye, Upload, FileText, Video, Headphones, X } from 'lucide-react'
import { useAuth } from "../../contexts/AuthContext"
import { UploadMaterialModal } from "../../components/admin/UploadMaterialModal"
import { API_ENDPOINTS, apiUtils } from "../../config/api"

type Material = {
  id: string
  title: string
  description: string
  type: string
  subject: string
  price: number
  originalPrice?: number | null
  rating?: number | null
  pages?: number | null
  author?: string
  isPremium?: number | boolean
  thumbnailUrl?: string | null
  status: string
  downloads?: number
  fileSize?: string
  filePath?: string
}

type PreviewModalProps = {
  isOpen: boolean
  url: string | null
  title?: string
  onClose: () => void
}

function PreviewModal({ isOpen, url, title, onClose }: PreviewModalProps) {
  // Close on ESC
  useEffect(() => {
    if (!isOpen) return
    const onKeyDown = (e: KeyboardEvent) => {
      if (e.key === "Escape") onClose()
    }
    window.addEventListener("keydown", onKeyDown)
    return () => window.removeEventListener("keydown", onKeyDown)
  }, [isOpen, onClose])

  if (!isOpen || !url) return null

  return (
    <div
      role="dialog"
      aria-modal="true"
      aria-label={title || "Preview"}
      className="fixed inset-0 z-[1000] flex items-center justify-center"
    >
      {/* Backdrop */}
      <div
        className="absolute inset-0 bg-black/60"
        onClick={onClose}
        aria-hidden="true"
      />
      {/* Modal Panel */}
      <div className="relative z-[1001] w-[95vw] h-[85vh] bg-white rounded-lg shadow-xl overflow-hidden flex flex-col">
        <div className="flex items-center justify-between px-4 py-2 border-b">
          <div className="font-medium truncate pr-2">{title || "Preview"}</div>
          <button
            onClick={onClose}
            aria-label="Close preview"
            className="p-2 rounded-md hover:bg-gray-100 transition-colors"
          >
            <X className="h-5 w-5" />
          </button>
        </div>
        <div className="flex-1 bg-gray-50">
          <iframe
            title={title || "PDF Preview"}
            src={url}
            className="w-full h-full"
            onError={() => alert("Failed to load PDF preview. The file may not be accessible or is not a valid PDF.")}
          />
        </div>
      </div>
    </div>
  )
}

export default function AdminMaterialsCard() {
  const { user } = useAuth()
  const [searchTerm, setSearchTerm] = useState("")
  const [filter, setFilter] = useState("all")
  const [showUploadModal, setShowUploadModal] = useState(false)
  const [uploading, setUploading] = useState(false)
  const [materials, setMaterials] = useState<Material[]>([])
  const [loading, setLoading] = useState(true)

  // Preview state
  const [isPreviewOpen, setIsPreviewOpen] = useState(false)
  const [previewUrl, setPreviewUrl] = useState<string | null>(null)
  const [previewTitle, setPreviewTitle] = useState<string | undefined>(undefined)

  const fetchMaterials = async () => {
    try {
      setLoading(true)
      const response = await apiUtils.get(API_ENDPOINTS.STUDY_MATERIALS)
      if (response.ok) {
        const data = await response.json()
        setMaterials(data)
      } else {
        console.error("Failed to fetch materials", response.status)
      }
    } catch (err) {
      console.error("Error fetching materials:", err)
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchMaterials()
  }, [])

  const filteredMaterials = useMemo(
    () =>
      materials.filter((material) => {
        const matchesSearch =
          material.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
          material.description.toLowerCase().includes(searchTerm.toLowerCase())
        const matchesFilter =
          filter === "all" ||
          material.status === filter ||
          material.type === filter ||
          material.subject === filter
        return matchesSearch && matchesFilter
      }),
    [materials, searchTerm, filter]
  )

  const getTypeIcon = (type: string) => {
    switch (type) {
      case "Video":
        return <Video className="h-10 w-10 text-red-600" />
      case "Audio":
        return <Headphones className="h-10 w-10 text-purple-600" />
      default:
        // PDF / Document
        return <FileText className="h-12 w-12 text-blue-600" />
    }
  }

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "published":
        return (
          <span className="bg-green-100 text-green-800 px-2 py-1 rounded-full text-xs font-medium">
            Published
          </span>
        )
      case "draft":
        return (
          <span className="bg-yellow-100 text-yellow-800 px-2 py-1 rounded-full text-xs font-medium">
            Draft
          </span>
        )
      case "archived":
        return (
          <span className="bg-gray-100 text-gray-800 px-2 py-1 rounded-full text-xs font-medium">
            Archived
          </span>
        )
      default:
        return null
    }
  }

  const handleUpload = async (file: File, materialData: any) => {
    try {
      setUploading(true)
      const { getAuth } = await import("firebase/auth")
      const auth = getAuth()
      const currentUser = auth.currentUser
      if (!currentUser) throw new Error("User not authenticated")

      const token = await currentUser.getIdToken()
      const formData = new FormData()
      formData.append("file", file)
      formData.append("title", materialData.title)
      formData.append("description", materialData.description || "")
      formData.append("subject", materialData.subject)
      formData.append("price", materialData.price)
      formData.append("author", materialData.author)
      formData.append("tags", materialData.tags || "")
      formData.append("isPremium", materialData.isPremium)
      if (materialData.originalPrice) formData.append("originalPrice", materialData.originalPrice)
      if (materialData.pages) formData.append("pages", materialData.pages)
      if (materialData.thumbnailUrl) formData.append("thumbnailUrl", materialData.thumbnailUrl)
      formData.append("status", materialData.status || "published")

      const response = await fetch(`${API_ENDPOINTS.STUDY_MATERIALS}/upload`, {
        method: "POST",
        headers: { Authorization: `Bearer ${token}` },
        body: formData,
      })

      if (!response.ok) {
        const errorText = await response.text()
        throw new Error(`Upload failed: ${response.status} - ${errorText}`)
      }

      const savedMaterial = await response.json()
      setMaterials((p) => [savedMaterial, ...p])
      setShowUploadModal(false)
      setUploading(false)
      alert("Material uploaded successfully!")
    } catch (error: any) {
      console.error("Upload failed:", error)
      setUploading(false)
      alert(`Upload failed: ${error.message}`)
    }
  }

  const handleDelete = async (id: string) => {
  const confirmed = confirm("Delete this material permanently?")
  if (!confirmed) return
  try {
    const response = await apiUtils.delete(`${API_ENDPOINTS.STUDY_MATERIALS}/${id}`)
    if (response.ok) {
      setMaterials((prev) => prev.filter((m) => m.id !== id))
      alert("Material deleted successfully!")
    } else {
      const errorText = await response.text()
      throw new Error(`Delete failed: ${response.status} - ${errorText}`)
    }
  } catch (err) {
    console.error(err)
    alert("Failed to delete material")
  }
}

  const getPreviewUrl = useCallback((material: Material): string | null => {
    // Get the file path
    let fileUrl = material.filePath ||
      (material as any).fileUrl ||
      (material as any).url ||
      null

    if (!fileUrl || typeof fileUrl !== "string") return null

    // Only handle PDF files
    if (material.type !== "PDF") return null

    // Handle Cloudinary URLs
    if (fileUrl.includes("cloudinary.com")) {
      // Remove fl_attachment if present
      let previewUrl = fileUrl.replace("/fl_attachment", "")
      
      // Convert to image format for better preview support
      // Replace /raw/upload with /image/upload and add PDF page conversion
      previewUrl = previewUrl.replace("/raw/upload", "/image/upload")
      
      // Add transformations for PDF preview
      if (!previewUrl.includes("/image/upload/")) {
        previewUrl = previewUrl.replace("/upload", "/image/upload")
      }
      
      // Add PDF page conversion and quality settings
      const transformations = "f_jpg,pg_1,q_auto,w_800"
      if (previewUrl.includes("/image/upload/v")) {
        previewUrl = previewUrl.replace("/image/upload/v", `/image/upload/${transformations}/v`)
      } else if (previewUrl.includes("/image/upload/")) {
        previewUrl = previewUrl.replace("/image/upload/", `/image/upload/${transformations}/`)
      }
      
      // Use Google Docs Viewer as a fallback for better PDF viewing
      return `https://docs.google.com/viewer?url=${encodeURIComponent(fileUrl)}&embedded=true`
    }

    // For non-Cloudinary URLs, use Google Docs Viewer
    return `https://docs.google.com/viewer?url=${encodeURIComponent(fileUrl)}&embedded=true`
  }, [])

  const openPreview = useCallback(
    (material: Material) => {
      const url = getPreviewUrl(material)
      if (!url) {
        alert("No PDF preview available for this item.")
        return
      }
      setPreviewUrl(url)
      setPreviewTitle(material.title)
      setIsPreviewOpen(true)
    },
    [getPreviewUrl]
  )

  const closePreview = useCallback(() => {
    setIsPreviewOpen(false)
    setPreviewUrl(null)
    setPreviewTitle(undefined)
  }, [])

  return (
    <div className="space-y-5">
      {/* Header */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center">
        <div>
          <h1 className="text-2xl font-semibold text-gray-900">Study Materials</h1>
          <p className="text-gray-500 mt-1">
            Manage study materials — minimal cards for premium & free content
          </p>
        </div>
        <button
          onClick={() => setShowUploadModal(true)}
          className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors flex items-center gap-2 mt-4 sm:mt-0"
        >
          <Upload className="h-4 w-4" />
          <span>Upload Material</span>
        </button>
      </div>

      {/* Filters + Search */}
      <div className="bg-white p-4 rounded-lg shadow-sm border border-gray-200">
        <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
          <div className="flex items-center gap-2 flex-wrap">
            <Filter className="h-5 w-5 text-gray-600" />
            {[
              "all",
              "published",
              "draft",
              "PDF",
              "Video",
              "Audio",
              "Current Affairs",
              "Polity",
              "Economics",
            ].map((filterOption) => (
              <button
                key={filterOption}
                onClick={() => setFilter(filterOption)}
                className={`px-3 py-1 rounded-md text-sm font-medium transition-colors border ${
                  filter === filterOption
                    ? "bg-blue-600 text-white border-blue-600"
                    : "bg-transparent text-gray-700 border-gray-200 hover:bg-gray-50"
                }`}
              >
                {filterOption}
              </button>
            ))}
          </div>
          <div className="relative">
            <Search className="absolute left-3 top-1/2 -translate-y-1/2 h-5 w-5 text-gray-400" />
            <input
              type="text"
              placeholder="Search materials..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent w-72"
            />
          </div>
        </div>
      </div>

      {/* Cards Grid */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-5">
        {loading &&
          Array.from({ length: 6 }).map((_, i) => (
            <div
              key={i}
              className="animate-pulse bg-white rounded-lg p-3 shadow-sm border border-gray-200"
            >
              <div className="h-40 bg-gray-100 rounded mb-3" />
              <div className="h-4 bg-gray-100 rounded w-3/4 mb-2" />
              <div className="h-3 bg-gray-100 rounded w-1/2 mb-2" />
              <div className="flex justify-between items-center mt-3">
                <div className="h-8 w-20 bg-gray-100 rounded" />
                <div className="h-8 w-20 bg-gray-100 rounded" />
              </div>
            </div>
          ))}

        {!loading && filteredMaterials.length === 0 && (
          <div className="col-span-full text-center text-gray-500 py-12">
            No materials found
          </div>
        )}

        {!loading &&
          filteredMaterials.map((material) => (
            <article
              key={material.id}
              className="bg-white rounded-lg p-3 shadow-sm border border-gray-200 flex flex-col hover:shadow-md transition-shadow relative overflow-hidden"
            >
              {/* Thumbnail with Hover Preview */}
              <div className="relative rounded-md bg-gray-50 overflow-hidden mb-3 group">
                <button
                  type="button"
                  onClick={() => openPreview(material)}
                  className="absolute inset-0 z-10"
                  aria-label="Open preview"
                />
                <div className="h-44 w-full flex items-center justify-center">
                  {material.thumbnailUrl ? (
                    <img
                      src={material.thumbnailUrl || "/placeholder.svg"}
                      alt={material.title}
                      className="object-cover h-full w-full"
                    />
                  ) : (
                    <div className="flex flex-col items-center justify-center text-gray-400">
                      {getTypeIcon(material.type)}
                      <p className="text-xs mt-2">{material.type}</p>
                    </div>
                  )}
                </div>

                {/* Hover overlay */}
                <div className="pointer-events-none absolute inset-0 bg-black/0 group-hover:bg-black/40 transition-colors flex items-center justify-center">
                  <div className="opacity-0 group-hover:opacity-100 transition-opacity text-white text-sm font-medium px-3 py-1.5 rounded-md flex items-center gap-2 bg-white/10 backdrop-blur">
                    <Eye className="h-4 w-4" />
                    Preview
                  </div>
                </div>
              </div>

              {/* Content */}
              <div className="mb-2">
                <h3 className="text-base font-medium text-gray-900 line-clamp-2">
                  {material.title}
                </h3>
                <p className="text-sm text-gray-500 mt-1 line-clamp-2">
                  {material.description}
                </p>
              </div>

              <div className="flex items-center justify-between mt-2">
                <div className="flex items-center gap-3 text-sm">
                  <div className="text-gray-600">By {material.author || "—"}</div>
                  <div className="text-gray-500">{material.fileSize || "—"}</div>
                </div>
                <div className="text-right">
                  <div className="text-lg font-semibold text-green-600">₹{material.price}</div>
                  {/* Hide "0 downloads" */}
                  {material.downloads && material.downloads > 0 ? (
                    <div className="text-xs text-gray-500">{material.downloads} downloads</div>
                  ) : null}
                </div>
              </div>

              <div className="flex items-center justify-between mt-3">
                <div>{getStatusBadge(material.status)}</div>
                <div className="flex items-center gap-1.5">
                  <button
                    title="View"
                    onClick={() => openPreview(material)}
                    className="p-2 text-blue-600 hover:bg-blue-50 rounded-md transition-colors"
                  >
                    <Eye className="h-4 w-4" />
                  </button>
                  <button
                    title="Edit"
                    className="p-2 text-green-600 hover:bg-green-50 rounded-md transition-colors"
                  >
                    <Edit className="h-4 w-4" />
                  </button>
                  <button
                    title="Delete"
                    onClick={() => handleDelete(material.id)}
                    className="p-2 text-red-600 hover:bg-red-50 rounded-md transition-colors"
                  >
                    <Trash2 className="h-4 w-4" />
                  </button>
                </div>
              </div>
            </article>
          ))}
      </div>

      {/* Upload Modal */}
      {showUploadModal && (
        <UploadMaterialModal
          isOpen={showUploadModal}
          onClose={() => setShowUploadModal(false)}
          onUpload={handleUpload}
          uploading={uploading}
        />
      )}

      {/* Preview Modal */}
      <PreviewModal
        isOpen={isPreviewOpen}
        url={previewUrl}
        title={previewTitle}
        onClose={closePreview}
      />
    </div>
  )
}
