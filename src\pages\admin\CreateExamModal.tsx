import React from "react"
import { X } from 'lucide-react'

interface ExamFormData {
   title: string
   description: string
   subject: string
   category: string
   duration: string
   totalQuestions: string
   totalMarks: string
   passingMarks: string
   difficulty: string
   status: string
   startDate: string
   startTime: string
   instructions: string
   isPremium: string
   basePrice: string
   offerPrice: string
}

interface CreateExamModalProps {
   isOpen: boolean
   onClose: () => void
   examFormData: ExamFormData
   onFormChange: (
      e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>
   ) => void
   onCreateExam: (e: React.FormEvent, publish: boolean) => void
}

const inputClass =
   "w-full rounded-md border border-gray-200 bg-white px-3 py-2 text-sm shadow-sm transition focus:outline-none focus:ring-2 focus:ring-gray-900/10 focus:border-gray-400"
const selectClass = inputClass
const textareaClass = inputClass + " resize-y"

const sectionTitleClass = "text-sm font-medium text-gray-900"
const sectionCardClass = "rounded-lg border border-gray-100 bg-white p-4"

const CreateExamModal: React.FC<CreateExamModalProps> = ({
   isOpen,
   onClose,
   examFormData,
   onFormChange,
   onCreateExam,
}) => {
   if (!isOpen) return null

   const showSavings =
      examFormData.basePrice &&
      examFormData.offerPrice &&
      Number(examFormData.basePrice) > Number(examFormData.offerPrice)

   const savings =
      Number(examFormData.basePrice || 0) - Number(examFormData.offerPrice || 0)

   const discountPercent =
      examFormData.basePrice && Number(examFormData.basePrice) > 0
         ? Math.round((savings / Number(examFormData.basePrice)) * 100)
         : 0

   return (
      <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/50 p-3 backdrop-blur-sm">
         <div className="w-full max-w-2xl overflow-hidden rounded-xl bg-white shadow-2xl ring-1 ring-black/5">
            {/* Header */}
            <div className="flex items-center justify-between border-b px-4 py-3">
               <div className="min-w-0">
                  <h3 className="truncate text-lg font-semibold text-gray-900">Create New Exam</h3>
                  <p className="mt-0.5 line-clamp-1 text-xs text-gray-500">
                     Setup details, scheduling, and pricing in a compact form
                  </p>
               </div>
               <button
                  onClick={onClose}
                  aria-label="Close"
                  className="rounded-md p-2 text-gray-500 transition hover:bg-gray-100 hover:text-gray-700"
               >
                  <X className="h-5 w-5" />
               </button>
            </div>

            {/* Content */}
            <div className="max-h-[80vh] overflow-y-auto px-4 py-4">
               <form className="space-y-4" onSubmit={(e) => onCreateExam(e, true)}>
                  {/* Basic Information */}
                  <section className={sectionCardClass}>
                     <h4 className={sectionTitleClass}>Basic Information</h4>
                     <div className="mt-3 grid grid-cols-1 gap-3 md:grid-cols-2">
                        <div className="md:col-span-2">
                           <label className="mb-1 block text-xs font-medium text-gray-600 pl-0.5">
                              Exam Title <span className="text-red-500">*</span>
                           </label>
                           <input
                              type="text"
                              name="title"
                              value={examFormData.title}
                              onChange={onFormChange}
                              className={inputClass}
                              placeholder="Enter a descriptive exam title"
                           />
                        </div>

                        <div>
                           <label className="mb-1 block text-xs font-medium text-gray-600 pl-0.5">
                              Category <span className="text-red-500">*</span>
                           </label>
                           <select
                              name="category"
                              value={examFormData.category}
                              onChange={onFormChange}
                              className={selectClass}
                           >
                              <option value="">Select category</option>
                              <option value="Prelims">Prelims</option>
                              <option value="Mains">Mains</option>
                              <option value="Mock Test">Mock Test</option>
                           </select>
                        </div>

                        <div>
                           <label className="mb-1 block text-xs font-medium text-gray-600 pl-0.5">
                              Subject <span className="text-red-500">*</span>
                           </label>
                           <input
                              type="text"
                              name="subject"
                              value={examFormData.subject}
                              onChange={onFormChange}
                              className={inputClass}
                              placeholder="e.g., General Studies, Mathematics"
                           />
                        </div>

                        <div className="md:col-span-2">
                           <label className="mb-1 block text-xs font-medium text-gray-600 pl-0.5">
                              Description
                           </label>
                           <textarea
                              name="description"
                              value={examFormData.description}
                              onChange={onFormChange}
                              rows={3}
                              className={textareaClass}
                              placeholder="Provide a brief description..."
                           />
                        </div>
                     </div>
                  </section>

                  {/* Exam Configuration */}
                  <section className={sectionCardClass}>
                     <h4 className={sectionTitleClass}>Exam Configuration</h4>
                     <div className="mt-3 grid grid-cols-1 gap-3 md:grid-cols-3">
                        <div>
                           <label className="mb-1 block text-xs font-medium text-gray-600 pl-0.5">
                              Duration (minutes) <span className="text-red-500">*</span>
                           </label>
                           <input
                              type="number"
                              name="duration"
                              value={examFormData.duration}
                              onChange={onFormChange}
                              className={inputClass}
                              placeholder="120"
                              min={1}
                           />
                        </div>

                        <div>
                           <label className="mb-1 block text-xs font-medium text-gray-600 pl-0.5">
                              Total Marks <span className="text-red-500">*</span>
                           </label>
                           <input
                              type="number"
                              name="totalMarks"
                              value={examFormData.totalMarks}
                              onChange={onFormChange}
                              className={inputClass}
                              placeholder="200"
                              min={1}
                           />
                        </div>

                        <div>
                           <label className="mb-1 block text-xs font-medium text-gray-600 pl-0.5">
                              Passing Marks <span className="text-red-500">*</span>
                           </label>
                           <input
                              type="number"
                              name="passingMarks"
                              value={examFormData.passingMarks}
                              onChange={onFormChange}
                              className={inputClass}
                              placeholder="66"
                              min={1}
                           />
                        </div>

                        <div>
                           <label className="mb-1 block text-xs font-medium text-gray-600 pl-0.5">
                              Difficulty Level
                           </label>
                           <select
                              name="difficulty"
                              value={examFormData.difficulty}
                              onChange={onFormChange}
                              className={selectClass}
                           >
                              <option value="Easy">Easy</option>
                              <option value="Medium">Medium</option>
                              <option value="Hard">Hard</option>
                           </select>
                        </div>

                        <div>
                           <label className="mb-1 block text-xs font-medium text-gray-600 pl-0.5">
                              Total Questions
                           </label>
                           <input
                              type="number"
                              name="totalQuestions"
                              value={examFormData.totalQuestions}
                              onChange={onFormChange}
                              className="w-full cursor-not-allowed rounded-md border border-gray-200 bg-gray-50 px-3 py-2 text-sm text-gray-500 shadow-sm"
                              placeholder="Auto-calculated"
                              readOnly
                              disabled
                              title="Automatically calculated from added questions"
                           />
                        </div>

                        <div>
                           <label className="mb-1 block text-xs font-medium text-gray-600 pl-0.5">
                              Exam Type
                           </label>
                           <select
                              name="isPremium"
                              value={examFormData.isPremium}
                              onChange={onFormChange}
                              className={selectClass}
                           >
                              <option value="Free">Free</option>
                              {/* <option value="Premium">Premium</option> */}
                           </select>
                        </div>
                     </div>
                  </section>

                  {/* Premium Pricing */}
                  {examFormData.isPremium === "Premium" && (
                     <section className={sectionCardClass}>
                        <h4 className={sectionTitleClass}>Premium Pricing</h4>
                        <div className="mt-3 grid grid-cols-1 gap-3 md:grid-cols-2">
                           <div>
                              <label className="mb-1 block text-xs font-medium text-gray-600 pl-0.5">
                                 Base Price (₹) <span className="text-red-500">*</span>
                              </label>
                              <input
                                 type="number"
                                 name="basePrice"
                                 value={examFormData.basePrice}
                                 onChange={onFormChange}
                                 className={inputClass}
                                 placeholder="299"
                                 min={0}
                              />
                           </div>
                           <div>
                              <label className="mb-1 block text-xs font-medium text-gray-600 pl-0.5">
                                 Offer Price (₹) <span className="text-red-500">*</span>
                              </label>
                              <input
                                 type="number"
                                 name="offerPrice"
                                 value={examFormData.offerPrice}
                                 onChange={onFormChange}
                                 className={inputClass}
                                 placeholder="199"
                                 min={0}
                              />
                           </div>
                        </div>

                        {showSavings && (
                           <div className="mt-3 rounded-md border border-emerald-100 bg-emerald-50 px-3 py-2 text-sm text-emerald-800">
                              {'💰'} Students will save ₹{savings} ({discountPercent}% discount)
                           </div>
                        )}
                     </section>
                  )}

                  {/* Schedule & Instructions */}
                  <section className={sectionCardClass}>
                     <h4 className={sectionTitleClass}>Schedule & Instructions</h4>
                     <div className="mt-3 grid grid-cols-1 gap-3 md:grid-cols-2">
                        <div>
                           <label className="mb-1 block text-xs font-medium text-gray-600 pl-0.5">
                              Start Date <span className="text-red-500">*</span>
                           </label>
                           <input
                              type="date"
                              name="startDate"
                              value={examFormData.startDate}
                              onChange={onFormChange}
                              className={inputClass}
                           />
                        </div>
                        <div>
                           <label className="mb-1 block text-xs font-medium text-gray-600 pl-0.5">
                              Start Time <span className="text-red-500">*</span>
                           </label>
                           <input
                              type="time"
                              name="startTime"
                              value={examFormData.startTime}
                              onChange={onFormChange}
                              className={inputClass}
                           />
                        </div>
                     </div>

                     <div className="mt-3">
                        <label className="mb-1 block text-xs font-medium text-gray-600 pl-0.5">
                           Exam Instructions
                        </label>
                        <textarea
                           name="instructions"
                           value={examFormData.instructions}
                           onChange={onFormChange}
                           rows={4}
                           className={textareaClass}
                           placeholder="Enter detailed instructions for students..."
                        />
                     </div>
                  </section>

                  {/* Actions */}
                  <div className="flex flex-col-reverse gap-2 border-t pt-3 sm:flex-row sm:justify-end">
                     <button
                        type="button"
                        onClick={onClose}
                        className="rounded-md px-4 py-2 text-sm font-medium text-gray-600 transition hover:bg-gray-100"
                     >
                        Cancel
                     </button>

                     <button
                        type="button"
                        onClick={(e) => onCreateExam(e as unknown as React.FormEvent, false)}
                        className="rounded-md border border-gray-200 bg-white px-4 py-2 text-sm font-medium text-gray-800 shadow-sm transition hover:border-gray-300 hover:bg-gray-50"
                     >
                        Save as Draft
                     </button>

                     <button
                        type="submit"
                        className="rounded-md bg-gray-900 px-4 py-2 text-sm font-medium text-white shadow-sm transition hover:bg-black"
                     >
                        Publish Exam
                     </button>
                  </div>
               </form>
            </div>
         </div>
      </div>
   )
}

export default CreateExamModal