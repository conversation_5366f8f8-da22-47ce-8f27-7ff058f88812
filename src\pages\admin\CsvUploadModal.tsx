"use client"
import type React from "react"
import { useState, use<PERSON><PERSON>back, useMemo } from "react"
import {
  Upload,
  Download,
  Edit2,
  Trash2,
  Check,
  X,
  AlertCircle,
  CheckCircle,
  Eye,
  EyeOff,
  Save,
  AlertTriangle,
  ArrowLeft,
} from "lucide-react"
import { toast } from "react-toastify"
import <PERSON> from "papaparse"
import { API_ENDPOINTS, apiUtils } from "../../config/api"

// Types (keep existing types)
interface Question {
  id?: string
  question: string
  options: string[]
  correctAnswer: string
  explanation: string
  subject: string
  topic: string
  category: string
  difficulty: string
  marks: number
  negativeMarks: number
  questionType: string
  tags: string[]
  imageUrl?: string
  status: string
  examId?: string
  createdAt?: string
  updatedAt?: string
}

interface CsvError {
  rowNumber: number
  columnName: string
  errorMessage: string
  cellValue: string
}

interface CsvValidationResult {
  validQuestions: Question[]
  errors: CsvError[]
  headers: string[]
}

interface ParsedQuestion extends Question {
  rowIndex: number
  hasErrors: boolean
  errors: CsvError[]
  originalData: Record<string, string>
}

interface CsvUploadModalProps {
  examId: string
  onClose: () => void
  onQuestionsAdded: (questions: Question[]) => void
}

// Constants
const EXPECTED_HEADERS = [
  "question",
  "option1",
  "option2",
  "option3",
  "option4",
  "correctAnswer",
  "explanation",
  "subject",
  "topic",
  "category",
  "difficulty",
  "marks",
  "negativeMarks",
  "questionType",
  "tags",
  "imageUrl",
]

const DIFFICULTY_OPTIONS = ["Easy", "Medium", "Hard"]
const QUESTION_TYPE_OPTIONS = ["mcq", "true_false", "numerical"]
const CATEGORY_OPTIONS = ["Prelims", "Mains", "Mock Test"]

const CsvUploadModal: React.FC<CsvUploadModalProps> = ({ examId, onClose, onQuestionsAdded }) => {
  // State
  const [csvFile, setCsvFile] = useState<File | null>(null)
  const [parsedQuestions, setParsedQuestions] = useState<ParsedQuestion[]>([])
  const [csvErrors, setCsvErrors] = useState<CsvError[]>([])
  const [isUploading, setIsUploading] = useState(false)
  const [isProcessing, setIsProcessing] = useState(false)
  const [currentStep, setCurrentStep] = useState<"upload" | "review" | "success">("upload")
  const [editingIndex, setEditingIndex] = useState<number | null>(null)
  const [showDetails, setShowDetails] = useState(false)
  const [selectedQuestions, setSelectedQuestions] = useState<Set<number>>(new Set())
  const [searchTerm, setSearchTerm] = useState("")
  const [filterType, setFilterType] = useState<"all" | "valid" | "invalid">("all")

  // Memoized values
  const validQuestions = useMemo(() => parsedQuestions.filter((q) => !q.hasErrors), [parsedQuestions])
  const invalidQuestions = useMemo(() => parsedQuestions.filter((q) => q.hasErrors), [parsedQuestions])
  const filteredQuestions = useMemo(() => {
    let filtered = parsedQuestions
    if (searchTerm) {
      filtered = filtered.filter(
        (q) =>
          q.question.toLowerCase().includes(searchTerm.toLowerCase()) ||
          q.subject.toLowerCase().includes(searchTerm.toLowerCase()) ||
          q.topic.toLowerCase().includes(searchTerm.toLowerCase()),
      )
    }
    switch (filterType) {
      case "valid":
        filtered = filtered.filter((q) => !q.hasErrors)
        break
      case "invalid":
        filtered = filtered.filter((q) => q.hasErrors)
        break
    }
    return filtered
  }, [parsedQuestions, searchTerm, filterType])

  // Generate sample CSV
  const generateSampleCsv = useCallback(() => {
    const sampleData = [
      {
        question: "What is the capital of India?",
        option1: "New Delhi",
        option2: "Mumbai",
        option3: "Kolkata",
        option4: "Chennai",
        correctAnswer: "New Delhi",
        explanation: "New Delhi is the capital city of India.",
        subject: "General Studies",
        topic: "Geography",
        category: "Prelims",
        difficulty: "Easy",
        marks: "2",
        negativeMarks: "0.66",
        questionType: "mcq",
        tags: "geography,capital,india",
        imageUrl: "",
      },
    ]
    return Papa.unparse(sampleData, { header: true, columns: EXPECTED_HEADERS })
  }, [])

  // Download sample CSV
  const handleDownloadSample = useCallback(() => {
    const csvContent = generateSampleCsv()
    const blob = new Blob([csvContent], { type: "text/csv;charset=utf-8;" })
    const url = URL.createObjectURL(blob)
    const link = document.createElement("a")
    link.setAttribute("href", url)
    link.setAttribute("download", "sample_questions.csv")
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    URL.revokeObjectURL(url)
    toast.success("Sample CSV downloaded!")
  }, [generateSampleCsv])

  // Parse CSV file
  const parseCsvFile = useCallback(
    async (file: File) => {
      setIsProcessing(true)
      try {
        Papa.parse(file, {
          header: true,
          skipEmptyLines: true,
          complete: async (result) => {
            const headers = result.meta.fields || []
            // Check headers first
            const missingHeaders = EXPECTED_HEADERS.filter((h) => !headers.includes(h))
            if (missingHeaders.length > 0) {
              setCsvErrors([
                {
                  rowNumber: 0,
                  columnName: "HEADERS",
                  errorMessage: `Missing headers: ${missingHeaders.join(", ")}`,
                  cellValue: headers.join(", "),
                },
              ])
              setParsedQuestions([])
              setIsProcessing(false)
              toast.error(`Missing headers: ${missingHeaders.join(", ")}`)
              return
            }

            // Parse questions
            const questions: ParsedQuestion[] = result.data.map((row: any, index: number) => ({
              question: row.question || "",
              options: [row.option1 || "", row.option2 || "", row.option3 || "", row.option4 || ""],
              correctAnswer: row.correctAnswer || "",
              explanation: row.explanation || "",
              subject: row.subject || "",
              topic: row.topic || "",
              category: row.category || "",
              difficulty: row.difficulty || "Medium",
              marks: Number.parseInt(row.marks) || 0,
              negativeMarks: Number.parseFloat(row.negativeMarks) || 0,
              questionType: row.questionType || "mcq",
              tags: row.tags ? row.tags.split(",").map((t: string) => t.trim()) : [],
              imageUrl: row.imageUrl || undefined,
              status: "active",
              examId,
              rowIndex: index,
              hasErrors: false,
              errors: [],
              originalData: row,
            }))

            setParsedQuestions(questions)
            setCsvErrors([])
            setIsProcessing(false)
            if (questions.length > 0) {
              setCurrentStep("review")
              toast.success(`Loaded ${questions.length} questions`)
            } else {
              toast.error("No questions found in CSV")
            }
          },
          error: (error) => {
            setIsProcessing(false)
            toast.error("Error parsing CSV file")
            console.error("CSV parse error:", error)
          },
        })
      } catch (error) {
        setIsProcessing(false)
        console.error("CSV validation error:", error)
        toast.error("Failed to process CSV file")
      }
    },
    [examId],
  )

  // Handle file selection
  const handleFileChange = useCallback(
    (e: React.ChangeEvent<HTMLInputElement>) => {
      const file = e.target.files?.[0]
      if (!file) {
        setCsvFile(null)
        setParsedQuestions([])
        setCsvErrors([])
        return
      }
      if (!file.name.endsWith(".csv")) {
        toast.error("Please select a CSV file")
        setCsvFile(null)
        return
      }
      setCsvFile(file)
      parseCsvFile(file)
    },
    [parseCsvFile],
  )

  // Update question
  const updateQuestion = useCallback((index: number, field: keyof ParsedQuestion, value: any) => {
    setParsedQuestions((prev) => {
      const updated = [...prev]
      updated[index] = { ...updated[index], [field]: value }
      // Basic validation
      const errors: CsvError[] = []
      const q = updated[index]
      if (!q.question.trim()) {
        errors.push({
          rowNumber: index + 2,
          columnName: "question",
          errorMessage: "Question required",
          cellValue: q.question,
        })
      }
      if (!q.correctAnswer.trim()) {
        errors.push({
          rowNumber: index + 2,
          columnName: "correctAnswer",
          errorMessage: "Correct answer required",
          cellValue: q.correctAnswer,
        })
      }
      updated[index].errors = errors
      updated[index].hasErrors = errors.length > 0
      return updated
    })
  }, [])

  // Remove questions
  const removeQuestions = useCallback((indices: number[]) => {
    setParsedQuestions((prev) => prev.filter((_, i) => !indices.includes(i)))
    setSelectedQuestions(new Set())
    toast.success(`Removed ${indices.length} question(s)`)
  }, [])

  // Toggle selection
  const toggleQuestionSelection = useCallback((index: number) => {
    setSelectedQuestions((prev) => {
      const newSet = new Set(prev)
      if (newSet.has(index)) {
        newSet.delete(index)
      } else {
        newSet.add(index)
      }
      return newSet
    })
  }, [])

  // In CsvUploadModal.tsx

const handleUpload = useCallback(async () => {
  if (!csvFile || validQuestions.length === 0) {
    toast.error("Please select a valid CSV file with questions")
    return
  }

  setIsUploading(true)
  try {
    const formData = new FormData()
    formData.append("file", csvFile)

    console.log("Uploading CSV to:", `${API_ENDPOINTS.QUESTIONS}/upload/${examId}`)
    console.log("FormData contains file:", csvFile.name)

    const response = await apiUtils.postMultipart(`${API_ENDPOINTS.QUESTIONS}/upload/${examId}`, formData)

    if (response.ok) {
      const uploadedQuestions = await response.json()
      console.log("Uploaded questions:", uploadedQuestions)
      onQuestionsAdded(uploadedQuestions)
      setCurrentStep("success")
      toast.success(`Uploaded ${uploadedQuestions.length} questions!`)
      setTimeout(() => onClose(), 2000)
    } else {
      const errorText = await response.text()
      console.error("Upload error:", {
        status: response.status,
        statusText: response.statusText,
        body: errorText,
      })
      toast.error(`Upload failed (Status: ${response.status}). Please check the file format.`)
    }
  } catch (error) {
    console.error("Upload error:", error)
    toast.error("Upload failed. Please try again.")
  } finally {
    setIsUploading(false)
  }
}, [validQuestions, examId, csvFile, onQuestionsAdded, onClose])

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg w-full max-w-6xl h-[85vh] flex flex-col shadow-xl">
        {/* Simple Header */}
        <div className="flex justify-between items-center p-4 border-b">
          <h2 className="text-xl font-semibold">CSV Question Import</h2>
          <button onClick={onClose} className="p-2 hover:bg-gray-100 rounded">
            <X className="h-5 w-5" />
          </button>
        </div>

        {/* Simple Steps */}
        <div className="px-4 py-3 border-b bg-gray-50">
          <div className="flex items-center justify-center space-x-8">
            {[
              { step: "upload", label: "Upload CSV" },
              { step: "review", label: "Review & Edit" },
              { step: "success", label: "Finish" },
            ].map(({ step, label }, index) => (
              <div key={step} className="flex items-center">
                <div
                  className={`flex items-center space-x-2 ${currentStep === step ? "text-blue-600" : "text-gray-400"}`}
                >
                  <div
                    className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${
                      currentStep === step ? "bg-blue-600 text-white" : "bg-gray-200"
                    }`}
                  >
                    {index + 1}
                  </div>
                  <span className="font-medium">{label}</span>
                </div>
                {index < 2 && <div className="w-12 h-px bg-gray-300 mx-4"></div>}
              </div>
            ))}
          </div>
        </div>

        {/* Content */}
        <div className="flex-1 overflow-hidden">
          {currentStep === "upload" && (
            <div className="p-6 space-y-6 h-full overflow-y-auto">
              {/* Download Sample */}
              <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                <div className="flex justify-between items-center">
                  <div>
                    <h4 className="font-semibold text-green-800">Download Sample CSV</h4>
                    <p className="text-green-700 text-sm">Get the correct format with sample data</p>
                  </div>
                  <button
                    onClick={handleDownloadSample}
                    className="flex items-center space-x-2 px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700"
                  >
                    <Download className="h-4 w-4" />
                    <span>Download</span>
                  </button>
                </div>
              </div>

              {/* Upload Area */}
              <div className="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center">
                {isProcessing ? (
                  <div className="space-y-4">
                    <div className="animate-spin rounded-full h-12 w-12 border-4 border-blue-600 border-t-transparent mx-auto"></div>
                    <p className="text-lg font-medium">Processing CSV...</p>
                  </div>
                ) : (
                  <div className="space-y-4">
                    <Upload className="h-12 w-12 text-gray-400 mx-auto" />
                    <div>
                      <p className="text-lg font-medium text-gray-700">Upload CSV File</p>
                      <p className="text-gray-500">Choose a CSV file with question data</p>
                    </div>
                    <input
                      type="file"
                      accept=".csv"
                      onChange={handleFileChange}
                      className="block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded file:border-0 file:text-sm file:font-medium file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100 mx-auto max-w-xs"
                    />
                  </div>
                )}
              </div>

              {/* Required Headers */}
              <div className="bg-gray-50 rounded-lg p-4">
                <h4 className="font-semibold text-gray-800 mb-3">Required Headers</h4>
                <div className="grid grid-cols-3 md:grid-cols-4 gap-2 text-xs">
                  {EXPECTED_HEADERS.map((header) => (
                    <div key={header} className="bg-white px-2 py-1 rounded border font-mono">
                      {header}
                    </div>
                  ))}
                </div>
              </div>

              {/* Errors */}
              {csvErrors.length > 0 && (
                <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                  <div className="flex items-start">
                    <AlertCircle className="h-5 w-5 text-red-500 mt-0.5 mr-3" />
                    <div>
                      <h4 className="font-semibold text-red-800">CSV Errors</h4>
                      {csvErrors.map((error, i) => (
                        <p key={i} className="text-red-700 text-sm mt-1">
                          {error.columnName}: {error.errorMessage}
                        </p>
                      ))}
                    </div>
                  </div>
                </div>
              )}
            </div>
          )}

          {currentStep === "review" && (
            <div className="h-full flex flex-col">
              {/* Review Header */}
              <div className="p-4 border-b bg-gray-50">
                <div className="flex justify-between items-center">
                  <div>
                    <h4 className="font-semibold">Review Questions</h4>
                    <p className="text-sm text-gray-600">Edit questions before uploading</p>
                  </div>
                  <div className="flex space-x-3">
                    <span className="px-3 py-1 bg-green-100 text-green-800 rounded text-sm">
                      Valid: {validQuestions.length}
                    </span>
                    {invalidQuestions.length > 0 && (
                      <span className="px-3 py-1 bg-red-100 text-red-800 rounded text-sm">
                        Invalid: {invalidQuestions.length}
                      </span>
                    )}
                  </div>
                </div>

                {/* Filters */}
                <div className="flex justify-between items-center mt-4">
                  <div className="flex space-x-3">
                    <input
                      type="text"
                      placeholder="Search..."
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      className="px-3 py-2 border rounded w-48"
                    />
                    <select
                      value={filterType}
                      onChange={(e) => setFilterType(e.target.value as any)}
                      className="px-3 py-2 border rounded"
                    >
                      <option value="all">All</option>
                      <option value="valid">Valid</option>
                      <option value="invalid">Invalid</option>
                    </select>
                  </div>
                  <div className="flex space-x-2">
                    <button
                      onClick={() => setShowDetails(!showDetails)}
                      className="px-3 py-2 text-gray-600 hover:bg-gray-100 rounded"
                    >
                      {showDetails ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                    </button>
                    {selectedQuestions.size > 0 && (
                      <button
                        onClick={() => removeQuestions(Array.from(selectedQuestions))}
                        className="px-3 py-2 text-red-600 hover:bg-red-50 rounded"
                      >
                        Remove ({selectedQuestions.size})
                      </button>
                    )}
                  </div>
                </div>
              </div>

              {/* Questions List */}
              <div className="flex-1 overflow-y-auto p-4">
                <div className="space-y-4">
                  {filteredQuestions.map((question, index) => (
                    <QuestionCard
                      key={question.rowIndex}
                      question={question}
                      index={question.rowIndex}
                      isEditing={editingIndex === question.rowIndex}
                      showDetails={showDetails}
                      isSelected={selectedQuestions.has(question.rowIndex)}
                      onEdit={() => setEditingIndex(editingIndex === question.rowIndex ? null : question.rowIndex)}
                      onUpdate={(field, value) => updateQuestion(question.rowIndex, field, value)}
                      onRemove={() => removeQuestions([question.rowIndex])}
                      onToggleSelect={() => toggleQuestionSelection(question.rowIndex)}
                    />
                  ))}
                </div>
              </div>

              {/* Footer */}
              <div className="p-4 border-t bg-gray-50">
                <div className="flex justify-between">
                  <button
                    onClick={() => setCurrentStep("upload")}
                    className="flex items-center space-x-2 px-4 py-2 border rounded hover:bg-gray-50"
                  >
                    <ArrowLeft className="h-4 w-4" />
                    <span>Back</span>
                  </button>
                  <button
                    onClick={handleUpload}
                    disabled={validQuestions.length === 0 || isUploading}
                    className="flex items-center space-x-2 px-6 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 disabled:bg-gray-400"
                  >
                    {isUploading ? (
                      <>
                        <div className="animate-spin rounded-full h-4 w-4 border-2 border-white border-t-transparent"></div>
                        <span>Uploading...</span>
                      </>
                    ) : (
                      <>
                        <Upload className="h-4 w-4" />
                        <span>Upload {validQuestions.length} Questions</span>
                      </>
                    )}
                  </button>
                </div>
              </div>
            </div>
          )}

          {currentStep === "success" && (
            <div className="flex flex-col items-center justify-center h-full space-y-6">
              <CheckCircle className="h-16 w-16 text-green-500" />
              <div className="text-center">
                <h4 className="text-2xl font-bold text-gray-900 mb-2">Upload Complete!</h4>
                <p className="text-gray-600">Successfully uploaded {validQuestions.length} questions</p>
              </div>
              <button onClick={onClose} className="px-6 py-2 bg-blue-600 text-white rounded hover:bg-blue-700">
                Close
              </button>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}

// Compact Question Card
interface QuestionCardProps {
  question: ParsedQuestion
  index: number
  isEditing: boolean
  showDetails: boolean
  isSelected: boolean
  onEdit: () => void
  onUpdate: (field: keyof ParsedQuestion, value: any) => void
  onRemove: () => void
  onToggleSelect: () => void
}

const QuestionCard: React.FC<QuestionCardProps> = ({
  question,
  index,
  isEditing,
  showDetails,
  isSelected,
  onEdit,
  onUpdate,
  onRemove,
  onToggleSelect,
}) => {
  return (
    <div
      className={`border rounded-lg p-4 ${question.hasErrors ? "border-red-200 bg-red-50" : "border-green-200 bg-green-50"} ${isSelected ? "ring-2 ring-blue-500" : ""}`}
    >
      <div className="flex items-center justify-between mb-3">
        <div className="flex items-center space-x-3">
          <input type="checkbox" checked={isSelected} onChange={onToggleSelect} className="w-4 h-4" />
          <span className="font-medium">Row {index + 2}</span>
          {question.hasErrors ? (
            <span className="px-2 py-1 bg-red-100 text-red-800 rounded text-xs">{question.errors.length} errors</span>
          ) : (
            <span className="px-2 py-1 bg-green-100 text-green-800 rounded text-xs">Valid</span>
          )}
        </div>
        <div className="flex space-x-2">
          <button onClick={onEdit} className="p-1 text-blue-600 hover:bg-blue-100 rounded">
            {isEditing ? <Save className="h-4 w-4" /> : <Edit2 className="h-4 w-4" />}
          </button>
          <button onClick={onRemove} className="p-1 text-red-600 hover:bg-red-100 rounded">
            <Trash2 className="h-4 w-4" />
          </button>
        </div>
      </div>

      {/* Errors */}
      {question.hasErrors && (
        <div className="mb-3 p-3 bg-red-100 border border-red-200 rounded">
          <div className="flex items-start">
            <AlertTriangle className="h-4 w-4 text-red-500 mt-0.5 mr-2" />
            <div className="text-sm">
              {question.errors.map((error, i) => (
                <p key={i} className="text-red-700">
                  <strong>{error.columnName}:</strong> {error.errorMessage}
                </p>
              ))}
            </div>
          </div>
        </div>
      )}

      {/* Content */}
      {isEditing ? (
        <EditQuestionForm question={question} onUpdate={onUpdate} />
      ) : (
        <ViewQuestionContent question={question} showDetails={showDetails} />
      )}
    </div>
  )
}

// Compact Edit Form
const EditQuestionForm: React.FC<{
  question: ParsedQuestion
  onUpdate: (field: keyof ParsedQuestion, value: any) => void
}> = ({ question, onUpdate }) => {
  return (
    <div className="space-y-4">
      <div>
        <label className="block text-sm font-medium mb-1">Question</label>
        <textarea
          value={question.question}
          onChange={(e) => onUpdate("question", e.target.value)}
          rows={2}
          className="w-full px-3 py-2 border rounded focus:ring-2 focus:ring-blue-500"
        />
      </div>
      <div className="grid grid-cols-2 gap-3">
        {question.options.map((option, i) => (
          <input
            key={i}
            value={option}
            onChange={(e) => {
              const newOptions = [...question.options]
              newOptions[i] = e.target.value
              onUpdate("options", newOptions)
            }}
            placeholder={`Option ${i + 1}`}
            className="px-3 py-2 border rounded focus:ring-2 focus:ring-blue-500"
          />
        ))}
      </div>
      <div className="grid grid-cols-2 gap-3">
        <div>
          <label className="block text-sm font-medium mb-1">Correct Answer</label>
          <input
            value={question.correctAnswer}
            onChange={(e) => onUpdate("correctAnswer", e.target.value)}
            className="w-full px-3 py-2 border rounded focus:ring-2 focus:ring-blue-500"
          />
        </div>
        <div>
          <label className="block text-sm font-medium mb-1">Difficulty</label>
          <select
            value={question.difficulty}
            onChange={(e) => onUpdate("difficulty", e.target.value)}
            className="w-full px-3 py-2 border rounded focus:ring-2 focus:ring-blue-500"
          >
            {DIFFICULTY_OPTIONS.map((option) => (
              <option key={option} value={option}>
                {option}
              </option>
            ))}
          </select>
        </div>
      </div>
      <div>
        <label className="block text-sm font-medium mb-1">Explanation</label>
        <textarea
          value={question.explanation}
          onChange={(e) => onUpdate("explanation", e.target.value)}
          rows={2}
          className="w-full px-3 py-2 border rounded focus:ring-2 focus:ring-blue-500"
        />
      </div>
      <div className="grid grid-cols-3 gap-3">
        <input
          value={question.subject}
          onChange={(e) => onUpdate("subject", e.target.value)}
          placeholder="Subject"
          className="px-3 py-2 border rounded focus:ring-2 focus:ring-blue-500"
        />
        <input
          value={question.topic}
          onChange={(e) => onUpdate("topic", e.target.value)}
          placeholder="Topic"
          className="px-3 py-2 border rounded focus:ring-2 focus:ring-blue-500"
        />
        <select
          value={question.category}
          onChange={(e) => onUpdate("category", e.target.value)}
          className="px-3 py-2 border rounded focus:ring-2 focus:ring-blue-500"
        >
          <option value="">Category</option>
          {CATEGORY_OPTIONS.map((option) => (
            <option key={option} value={option}>
              {option}
            </option>
          ))}
        </select>
      </div>
    </div>
  )
}

// Compact View
const ViewQuestionContent: React.FC<{ question: ParsedQuestion; showDetails: boolean }> = ({
  question,
  showDetails,
}) => {
  return (
    <div className="space-y-3">
      <div>
        <p className="font-medium text-gray-900">{question.question}</p>
      </div>
      <div className="grid grid-cols-2 gap-2">
        {question.options.map((option, i) => (
          <div
            key={i}
            className={`p-2 rounded text-sm ${
              option === question.correctAnswer
                ? "bg-green-100 border border-green-300 text-green-800"
                : "bg-white border text-gray-700"
            }`}
          >
            <span className="font-medium">{String.fromCharCode(65 + i)}.</span> {option}
            {option === question.correctAnswer && <Check className="inline h-3 w-3 ml-1" />}
          </div>
        ))}
      </div>
      {showDetails && (
        <div className="grid grid-cols-3 gap-2 text-xs text-gray-600">
          <div>Subject: {question.subject}</div>
          <div>Topic: {question.topic}</div>
          <div>Difficulty: {question.difficulty}</div>
        </div>
      )}
    </div>
  )
}

export default CsvUploadModal
