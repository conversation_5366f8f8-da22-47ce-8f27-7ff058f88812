"use client"

import type React from "react"
import { useState, useEffect } from "react"
import { Clock, CheckCircle, XCircle, RotateCcw, LogIn } from "lucide-react"
import QuestionText from "../utils/QuestionText";


interface Question {
  id: string
  question: string
  options: string[]
  correctAnswer: string
  explanation: string
  subject: string
}

interface QuizResult {
  score: number
  totalQuestions: number
  answers: { [key: string]: number }
  timeSpent: number
}

interface PublicQuizProps {
  questions: Question[]
  onQuizSubmit: (result: { dailyQuizId: string; answers: { [key: string]: number } }) => void
  onLoginPrompt: () => void
  isLoggedIn: boolean
  onBackToCalendar: () => void // Add callback to go back
}

const PublicQuiz: React.FC<PublicQuizProps> = ({
  questions,
  onQuizSubmit,
  onLoginPrompt,
  isLoggedIn,
  onBackToCalendar,
}) => {
  const [currentQuestion, setCurrentQuestion] = useState(0)
  const [selectedAnswer, setSelectedAnswer] = useState<number | null>(null)
  const [answers, setAnswers] = useState<{ [key: string]: number }>({})
  const [timeLeft, setTimeLeft] = useState(30 * 60) // 30 minutes
  const [quizCompleted, setQuizCompleted] = useState(false)
  const [result, setResult] = useState<QuizResult | null>(null)

  // Auto-start quiz when component mounts
  useEffect(() => {
    if (questions && questions.length > 0) {
      // Quiz starts immediately, no need for start screen
      setCurrentQuestion(0)
      setAnswers({})
      setTimeLeft(30 * 60)
      setQuizCompleted(false)
      setResult(null)
    }
  }, [questions])

  useEffect(() => {
    let interval: NodeJS.Timeout
    if (!quizCompleted && timeLeft > 0 && questions.length > 0) {
      interval = setInterval(() => {
        setTimeLeft((prev) => prev - 1)
      }, 1000)
    } else if (timeLeft === 0 && !quizCompleted) {
      handleQuizSubmit()
    }
    return () => clearInterval(interval)
  }, [quizCompleted, timeLeft, questions.length])

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60)
    const secs = seconds % 60
    return `${mins}:${secs.toString().padStart(2, "0")}`
  }

  const handleAnswerSelect = (answerIndex: number) => {
    setSelectedAnswer(answerIndex)
  }

  const handleNextQuestion = () => {
    if (selectedAnswer !== null) {
      const newAnswers = { ...answers, [questions[currentQuestion].id]: selectedAnswer }
      setAnswers(newAnswers)
      setSelectedAnswer(null)
      if (currentQuestion < questions.length - 1) {
        setCurrentQuestion(currentQuestion + 1)
      } else {
        handleQuizSubmit(newAnswers)
      }
    }
  }

  const handleQuizSubmit = (finalAnswers = answers) => {
    // Calculate score locally for immediate display
    const score = questions.reduce((acc, question) => {
      const userAnswerIndex = finalAnswers[question.id]
      const correctAnswerIndex = question.options.findIndex((option) => option === question.correctAnswer)
      return acc + (userAnswerIndex === correctAnswerIndex ? 1 : 0)
    }, 0)

    const result: QuizResult = {
      score,
      totalQuestions: questions.length,
      answers: finalAnswers,
      timeSpent: 30 * 60 - timeLeft,
    }

    setResult(result)
    setQuizCompleted(true)

    // If user is logged in, call the submission callback
    if (isLoggedIn) {
      const dailyQuizId = new Date().toISOString().split("T")[0]
      onQuizSubmit({
        dailyQuizId: dailyQuizId,
        answers: finalAnswers,
      })
    }
  }

  const handleRetakeQuiz = () => {
    setCurrentQuestion(0)
    setSelectedAnswer(null)
    setAnswers({})
    setTimeLeft(30 * 60)
    setQuizCompleted(false)
    setResult(null)
  }

  // Show loading state if no questions provided
  if (!questions || questions.length === 0) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  // Show results screen
  if (quizCompleted && result) {
    return (
      <div className="min-h-screen bg-gray-50 py-8">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="max-w-2xl mx-auto">
            <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-8">
              <div className="text-center mb-8">
                <div
                  className={`p-4 rounded-full w-16 h-16 mx-auto mb-6 ${
                    result.score >= questions.length * 0.8
                      ? "bg-green-100"
                      : result.score >= questions.length * 0.6
                        ? "bg-yellow-100"
                        : "bg-red-100"
                  }`}
                >
                  {result.score >= questions.length * 0.8 ? (
                    <CheckCircle className="h-8 w-8 text-green-600 mx-auto mt-1" />
                  ) : (
                    <XCircle className="h-8 w-8 text-red-600 mx-auto mt-1" />
                  )}
                </div>
                <h2 className="text-2xl font-bold text-gray-900 mb-4">Quiz Completed!</h2>
                <div className="text-3xl font-bold text-blue-600 mb-2">
                  {result.score}/{result.totalQuestions}
                </div>
                <p className="text-gray-600">You scored {Math.round((result.score / result.totalQuestions) * 100)}%</p>
              </div>

              <div className="space-y-6">
                {questions.map((question, index) => {
                  const userAnswerIndex = result.answers[question.id]
                  const correctAnswerIndex = question.options.findIndex((option) => option === question.correctAnswer)
                  const isCorrect = userAnswerIndex === correctAnswerIndex

                  return (
                    <div key={question.id} className="border border-gray-200 rounded-lg p-6">
                      <div className="flex items-start space-x-3 mb-4">
                        <div className={`p-1 rounded-full ${isCorrect ? "bg-green-100" : "bg-red-100"}`}>
                          {isCorrect ? (
                            <CheckCircle className="h-5 w-5 text-green-600" />
                          ) : (
                            <XCircle className="h-5 w-5 text-red-600" />
                          )}
                        </div>
                        <div className="flex-1">
                          <h3 className="font-medium text-gray-900 mb-2">
                            {index + 1}. {question.question}
                          </h3>
                          <div className="space-y-2">
                            {question.options.map((option, optionIndex) => (
                              <div
                                key={optionIndex}
                                className={`p-2 rounded text-sm ${
                                  optionIndex === correctAnswerIndex
                                    ? "bg-green-50 text-green-800 border border-green-200"
                                    : optionIndex === userAnswerIndex && !isCorrect
                                      ? "bg-red-50 text-red-800 border border-red-200"
                                      : "bg-gray-50 text-gray-700"
                                }`}
                              >
                                {option}
                              </div>
                            ))}
                          </div>
                          <div className="mt-3 p-3 bg-blue-50 rounded-lg">
                            <p className="text-sm text-blue-800">
                              <strong>Explanation:</strong> {question.explanation}
                            </p>
                          </div>
                        </div>
                      </div>
                    </div>
                  )
                })}
              </div>

              <div className="mt-8 text-center space-y-4">
                <div className="flex justify-center space-x-4">
                  <button
                    onClick={onBackToCalendar}
                    className="bg-gray-600 text-white px-6 py-3 rounded-lg font-medium hover:bg-gray-700 transition-all"
                  >
                    Back to Calendar
                  </button>
                  <button
                    onClick={handleRetakeQuiz}
                    className="bg-gradient-to-r from-blue-600 to-indigo-600 text-white px-6 py-3 rounded-lg font-medium hover:from-blue-700 hover:to-indigo-700 transition-all inline-flex items-center space-x-2"
                  >
                    <RotateCcw className="h-4 w-4" />
                    <span>Retake Quiz</span>
                  </button>
                </div>

                {!isLoggedIn && (
                  <div className="p-4 bg-green-50 rounded-lg border border-green-200">
                    <p className="text-sm text-green-800 mb-3">
                      Great job! Want to track your progress over time and access more features?
                    </p>
                    <button
                      onClick={onLoginPrompt}
                      className="bg-green-600 text-white px-4 py-2 rounded-lg text-sm font-medium hover:bg-green-700 transition-colors inline-flex items-center space-x-2"
                    >
                      <LogIn className="h-4 w-4" />
                      <span>Create Account to Save Progress</span>
                    </button>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
    )
  }

  // Main quiz interface - CLEAN, NO NAVIGATION
  const currentQ = questions[currentQuestion]
  const progress = ((currentQuestion + 1) / questions.length) * 100

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="max-w-2xl mx-auto">
          <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-8">
            {/* Header */}
            <div className="flex justify-between items-center mb-6">
              <div>
                <h2 className="text-xl font-bold text-gray-900">
                  Question {currentQuestion + 1} of {questions.length}
                </h2>
                <p className="text-sm text-gray-600">Subject: {currentQ.subject}</p>
              </div>
              <div className="flex items-center space-x-2 bg-red-50 text-red-700 px-3 py-2 rounded-lg">
                <Clock className="h-4 w-4" />
                <span className="font-medium">{formatTime(timeLeft)}</span>
              </div>
            </div>

            {/* Progress Bar */}
            <div className="mb-8">
              <div className="bg-gray-200 rounded-full h-2">
                <div
                  className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                  style={{ width: `${progress}%` }}
                />
              </div>
            </div>

            {/* Question */}
            <div className="mb-8">
               {/* Question Text - Better spacing for long questions */}
                          <div className="mb-8 border-l-4 border-blue-500 pl-6">
                            <QuestionText
                              text={currentQ.question}
                              paragraphClassName="text-lg text-gray-900 leading-relaxed font-medium"
                              addLineMarkers={true}            // default true
                              markerType="bullet"              // "bullet" | "dash" | "star" | "none"
                            />
                          </div>
              <div className="space-y-3">
                {currentQ.options.map((option, index) => (
                  <button
                    key={index}
                    onClick={() => handleAnswerSelect(index)}
                    className={`w-full text-left p-4 rounded-lg border-2 transition-all ${
                      selectedAnswer === index
                        ? "border-blue-500 bg-blue-50 text-blue-900"
                        : "border-gray-200 hover:border-gray-300 hover:bg-gray-50"
                    }`}
                  >
                    <div className="flex items-center">
                      <div
                        className={`w-4 h-4 rounded-full border-2 mr-3 ${
                          selectedAnswer === index ? "border-blue-500 bg-blue-500" : "border-gray-300"
                        }`}
                      >
                        {selectedAnswer === index && <div className="w-2 h-2 bg-white rounded-full mx-auto mt-0.5" />}
                      </div>
                      <span>{option}</span>
                    </div>
                  </button>
                ))}
              </div>
            </div>

            {/* Next Button */}
            <div className="flex justify-end">
              <button
                onClick={handleNextQuestion}
                disabled={selectedAnswer === null}
                className="bg-gradient-to-r from-blue-600 to-indigo-600 text-white px-6 py-3 rounded-lg font-medium disabled:opacity-50 disabled:cursor-not-allowed hover:from-blue-700 hover:to-indigo-700 transition-all"
              >
                {currentQuestion === questions.length - 1 ? "Submit Quiz" : "Next Question"}
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default PublicQuiz
