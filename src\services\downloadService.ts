import { API_ENDPOINTS, apiUtils } from '../config/api';

export interface DownloadConfig {
  maxDownloadsPerMaterial: number;
  downloadLinkExpiryMinutes: number;
  allowPrint: boolean;
  watermarkEnabled: boolean;
}

export interface SecureDownloadLink {
  url: string;
  token: string;
  expiresAt: string;
  downloadId: string;
}

export interface DownloadAttempt {
  id: string;
  userId: string;
  materialId: string;
  downloadId: string;
  attemptedAt: string;
  status: 'initiated' | 'in_progress' | 'completed' | 'failed' | 'expired';
  ipAddress?: string;
  userAgent?: string;
  progress?: number;
}

export class DownloadService {
  private config: DownloadConfig = {
    maxDownloadsPerMaterial: 5,
    downloadLinkExpiryMinutes: 30,
    allowPrint: false,
    watermarkEnabled: true
  };

  async generateSecureDownloadLink(
    materialId: string,
    userId: string,
    userInfo: any
  ): Promise<SecureDownloadLink> {
    // Check download limits from server
    const remainingDownloads = await this.getRemainingDownloads(materialId, userId);
    if (remainingDownloads <= 0) {
      throw new Error('Download limit exceeded for this material (5 downloads maximum)');
    }

    const downloadId = `dl_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    const token = this.generateSecureToken(materialId, userId, downloadId);
    const expiresAt = new Date(Date.now() + this.config.downloadLinkExpiryMinutes * 60 * 1000).toISOString();

    const secureLink: SecureDownloadLink = {
      url: `${API_ENDPOINTS.SECURE_DOWNLOAD}/${downloadId}`,
      token,
      expiresAt,
      downloadId
    };

    // Store download attempt on server
    await this.recordDownloadAttempt({
      id: downloadId,
      userId,
      materialId,
      downloadId,
      attemptedAt: new Date().toISOString(),
      status: 'initiated',
      ipAddress: await this.getClientIP(),
      userAgent: navigator.userAgent
    });

    return secureLink;
  }

  private generateSecureToken(materialId: string, userId: string, downloadId: string): string {
    const payload = {
      materialId,
      userId,
      downloadId,
      timestamp: Date.now(),
      watermark: this.config.watermarkEnabled,
      printRestricted: !this.config.allowPrint
    };

    return btoa(JSON.stringify(payload));
  }

  async getRemainingDownloads(materialId: string, userId: string): Promise<number> {
    try {
      const response = await apiUtils.get(`${API_ENDPOINTS.DOWNLOAD_LIMITS}/${userId}/${materialId}`);
      if (response.ok) {
        const data = await response.json();
        return data.remainingDownloads;
      } else {
        return 5;
      }
    } catch (error) {
      return 5;
    }
  }

  private getDownloadAttempts(materialId: string, userId: string): DownloadAttempt[] {
    const key = `download_attempts_${userId}_${materialId}`;
    const stored = localStorage.getItem(key);

    if (!stored) {
      console.log(`No download attempts found in localStorage for key: ${key}`);
      return [];
    }

    try {
      const attempts = JSON.parse(stored);
      console.log(`Found ${attempts.length} download attempts in localStorage:`, attempts);
      return attempts;
    } catch (error) {
      console.warn('Failed to parse download attempts from localStorage:', error);
      return [];
    }
  }

  private async recordDownloadAttempt(attempt: DownloadAttempt): Promise<void> {
    try {
      // Save to server
      await fetch(API_ENDPOINTS.DOWNLOAD_ATTEMPTS, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(attempt)
      });
    } catch (error) {
      console.warn('Failed to save to server, using localStorage');
    }

    // Also save to localStorage as backup
    const key = `download_attempts_${attempt.userId}_${attempt.materialId}`;
    const attempts = this.getDownloadAttempts(attempt.materialId, attempt.userId);
    attempts.unshift(attempt);
    localStorage.setItem(key, JSON.stringify(attempts.slice(0, 20)));
  }

  async updateDownloadProgress(downloadId: string, progress: number, status: string): Promise<void> {
  console.log('Updating download progress:', { downloadId, progress, status });

  try {
    // Update on server
    const token = localStorage.getItem('user_token'); // Assuming token is stored in localStorage
    await fetch(API_ENDPOINTS.DOWNLOAD_PROGRESS, {
      method: 'POST',
      headers: { 
        'Content-Type': 'application/json',
        'Authorization': token ? `Bearer ${token}` : ''
      },
      body: JSON.stringify({ downloadId, progress, status })
    });
  } catch (error) {
    console.warn('Failed to update progress on server:', error);
  }

  // Update progress in localStorage
  const allKeys = Object.keys(localStorage).filter(key => key.startsWith('download_attempts_'));

  for (const key of allKeys) {
    const attempts = JSON.parse(localStorage.getItem(key) || '[]');
    const attemptIndex = attempts.findIndex((a: DownloadAttempt) => a.downloadId === downloadId);

    if (attemptIndex !== -1) {
      attempts[attemptIndex] = {
        ...attempts[attemptIndex],
        progress,
        status: status as any
      };
      localStorage.setItem(key, JSON.stringify(attempts));

      console.log('Updated download attempt:', attempts[attemptIndex]);
      break;
    }
  }
}

  async markDownloadCompleted(downloadId: string): Promise<void> {
    // First check if server is reachable
    try {
      const healthCheck = await fetch(API_ENDPOINTS.STUDY_MATERIALS);
      if (!healthCheck.ok) {
        throw new Error('Server not reachable');
      }
    } catch (error) {
      await this.updateDownloadProgress(downloadId, 100, 'completed');
      return;
    }

    try {
      const response = await fetch(API_ENDPOINTS.DOWNLOAD_COMPLETE, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        },
        body: JSON.stringify({ downloadId })
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`Server responded with ${response.status}: ${errorText}`);
      }

      const result = await response.json();

    } catch (error) {
      // Continue with localStorage update only
    }

    await this.updateDownloadProgress(downloadId, 100, 'completed');
  }

  private async getClientIP(): Promise<string> {
    try {
      const response = await fetch('https://api.ipify.org?format=json');
      const data = await response.json();
      return data.ip;
    } catch {
      return 'unknown';
    }
  }

  getDownloadHistory(userId: string): DownloadAttempt[] {
    const allKeys = Object.keys(localStorage).filter(key =>
      key.startsWith('download_attempts_') && key.includes(`_${userId}_`)
    );

    const allAttempts: DownloadAttempt[] = [];
    for (const key of allKeys) {
      const attempts = JSON.parse(localStorage.getItem(key) || '[]');
      allAttempts.push(...attempts);
    }

    return allAttempts.sort((a, b) =>
      new Date(b.attemptedAt).getTime() - new Date(a.attemptedAt).getTime()
    );
  }
}










