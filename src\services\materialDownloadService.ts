import { API_ENDPOINTS, apiUtils } from '../config/api';

export class MaterialDownloadService {
  async downloadMaterial(materialId: string, userId: string) {
    try {
      const headers = await apiUtils.getAuthHeaders();
      const materialResponse = await apiUtils.get(`${API_ENDPOINTS.STUDY_MATERIALS}/${materialId}`);
      if (!materialResponse.ok) {
        throw new Error(`Material not found: ${materialResponse.statusText}`);
      }
      const material = await materialResponse.json();
      const downloadResponse = await fetch(`${API_ENDPOINTS.STUDY_MATERIALS}/${materialId}/download`, {
        headers
      });
      if (downloadResponse.ok) {
        const blob = await downloadResponse.blob();
        const url = window.URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        link.download = material.originalName || `${material.title}.pdf`;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        window.URL.revokeObjectURL(url);
        return {
          success: true,
          downloadUrl: url,
          fileName: material.originalName || `${material.title}.pdf`
        };
      } else {
        const errorText = await downloadResponse.text();
        throw new Error(`Download failed: ${errorText}`);
      }
    } catch (error) {
      console.error('Download failed:', error);
      return {
        success: false,
        error: error.message || 'Unknown error occurred'
      };
    }
  }
}

export const materialDownloadService = new MaterialDownloadService();