import { API_ENDPOINTS } from '../config/api';
import { apiUtils } from '../config/api';

export interface PaymentConfig {
  razorpay: {
    keyId: string;
  };
  cashfree: {
    appId: string;
    mode: 'sandbox' | 'production';
  };
}

export interface PaymentOrder {
  orderId: string;
  amount: number;
  currency: string;
  materialId: string;
  userId: string;
}

export interface PaymentResponse {
  orderId: string;
  amount: number;
  currency: string;
  keyId: string;
  appName: string;
  appId: string;
}

export interface UserProfileMinimal {
  userId: string | null;
  name: string | null;
  email: string | null;
  phone: string | null;
}

export class PaymentService {
  private config: PaymentConfig;

  constructor(config: PaymentConfig) {
    this.config = config;
  }

  // Generate unique order ID
  generateOrderId(materialId: string, userId: string): string {
    return `order_${materialId}_${userId}_${Date.now()}`;
  }

  // Get fallback values for missing profile fields
  private getFallbackValues(profile: UserProfileMinimal) {
    return {
      userId: profile.userId,
      name: profile.name || '',
      email: profile.email || '',
      phone: profile.phone || '9999999999',
    };
  }

  

  // Razorpay integration - Backend microservice
  async initiateRazorpayPayment(
    material: any,
    profile: UserProfileMinimal,
    
    onSuccess: (response: any) => void,
    onError: (error: any) => void
  ): Promise<void> {
    try {
      if (typeof (window as any).Razorpay === 'undefined') {
        throw new Error('Razorpay script not loaded');
      }

      // Get fallback values for missing fields
      const fallbackProfile = this.getFallbackValues(profile);
      console.log('Fallback Profile:', fallbackProfile.userId);

      // Call backend microservice to initiate payment
      const requestData = {
        userId: fallbackProfile.userId, // This should come from auth context
        materialId: material.id.toString(),
        amount: material.price,
        currency: 'INR',
      };

      const response = await apiUtils.post(API_ENDPOINTS.RAZORPAY_INITIATE, requestData);
      if (!response.ok) {
        throw new Error('Failed to initiate payment');
      }

      const paymentResponse: PaymentResponse = await response.json();

      const options = {
        key: paymentResponse.keyId,
        amount: paymentResponse.amount * 100,
        currency: paymentResponse.currency,
        name: paymentResponse.appName,
        description: `Purchase: ${material.title}`,
        order_id: paymentResponse.orderId,
        handler: async (response: any) => {
          try {
            // Verify payment with backend
            const verifyResponse = await apiUtils.post(API_ENDPOINTS.RAZORPAY_VERIFY, {
              orderId: paymentResponse.orderId,
              paymentId: response.razorpay_payment_id,
              signature: response.razorpay_signature,
              materialId: material.id.toString(),
              userId: fallbackProfile.userId,
            });

            if (!verifyResponse.ok) {
              throw new Error('Payment verification failed');
            }

            const purchase = await verifyResponse.json();
            onSuccess({
              ...purchase,
              materialId: material.id,
              userId: fallbackProfile.userId,
              amount: material.price,
              paymentMethod: 'razorpay',
              gateway: 'razorpay',
            });
          } catch (error) {
            console.error('Payment verification error:', error);
            onError(error);
          }
        },
        prefill: {
          name: fallbackProfile.name,
          email: fallbackProfile.email,
          contact: fallbackProfile.phone,
        },
        theme: {
          color: '#2563eb',
        },
        modal: {
          ondismiss: () => {
            onError(new Error('Payment cancelled by user'));
          },
        },
        retry: {
          enabled: true,
          max_count: 1,
        },
        timeout: 300,
        remember_customer: false,
      };

      const razorpay = new (window as any).Razorpay(options);
      razorpay.on('payment.failed', function (response: any) {
        console.error('Payment failed:', response.error);
        onError(new Error(`Payment failed: ${response.error.description}`));
      });

      razorpay.open();
    } catch (error) {
      console.error('Razorpay initialization error:', error);
      onError(error);
    }
  }

  // Razorpay integration - Bulk payment
  async initiateBulkRazorpayPayment(
    materials: any[],
    profile: UserProfileMinimal,
    user: any,
    totalAmount: number,
    onSuccess: (response: any) => void,
    onError: (error: any) => void
  ): Promise<void> {
    try {
      if (typeof (window as any).Razorpay === 'undefined') {
        throw new Error('Razorpay script not loaded');
      }

      // Get fallback values for missing fields
      const fallbackProfile = this.getFallbackValues(profile);

      // Call backend microservice to initiate bulk payment
      const requestData = {
        userId: fallbackProfile.userId,
        materialIds: materials.map((m) => m.id.toString()),
        totalAmount,
        currency: 'INR',
      };

      const response = await apiUtils.post(API_ENDPOINTS.RAZORPAY_BULK, requestData);
      if (!response.ok) {
        throw new Error('Failed to initiate bulk payment');
      }

      const paymentResponse: PaymentResponse = await response.json();

      const options = {
        key: paymentResponse.keyId,
        amount: paymentResponse.amount * 100,
        currency: paymentResponse.currency,
        name: paymentResponse.appName,
        description: `Bulk Purchase: ${materials.length} items`,
        order_id: paymentResponse.orderId,
        handler: async (response: any) => {
          try {
            // Verify bulk payment with backend
            const verifyResponse = await apiUtils.post(API_ENDPOINTS.RAZORPAY_VERIFY_BULK, {
              orderId: paymentResponse.orderId,
              paymentId: response.razorpay_payment_id,
              signature: response.razorpay_signature,
              userId: fallbackProfile.userId,
              materialIds: materials.map((m) => m.id.toString()),
              totalAmount,
              currency: 'INR',
            });

            if (!verifyResponse.ok) {
              throw new Error('Bulk payment verification failed');
            }

            onSuccess({
              orderId: paymentResponse.orderId,
              paymentId: response.razorpay_payment_id,
              materialIds: materials.map((m) => m.id.toString()),
              userId: fallbackProfile.userId,
              amount: totalAmount,
              paymentMethod: 'razorpay',
              gateway: 'razorpay',
            });
          } catch (error) {
            console.error('Bulk payment verification error:', error);
            onError(error);
          }
        },
        prefill: {
          name: fallbackProfile.name,
          email: fallbackProfile.email,
          contact: fallbackProfile.phone,
        },
        theme: {
          color: '#2563eb',
        },
        modal: {
          ondismiss: () => {
            onError(new Error('Payment cancelled by user'));
          },
        },
        retry: {
          enabled: true,
          max_count: 1,
        },
        timeout: 300,
        remember_customer: false,
      };

      const razorpay = new (window as any).Razorpay(options);
      razorpay.on('payment.failed', function (response: any) {
        console.error('Bulk payment failed:', response.error);
        onError(new Error(`Bulk payment failed: ${response.error.description}`));
      });

      razorpay.open();
    } catch (error) {
      console.error('Razorpay bulk initialization error:', error);
      onError(error);
    }
  }

  // Alternative approach using Razorpay Payment Links
  async initiateRazorpayPaymentLink(
    material: any,
    profile: UserProfileMinimal,
    onSuccess: (response: any) => void,
    onError: (error: any) => void
  ): Promise<void> {
    try {
      // Get fallback values for missing fields
      const fallbackProfile = this.getFallbackValues(profile);

      const paymentLinkData = {
        amount: material.price * 100,
        currency: 'INR',
        description: `Purchase: ${material.title}`,
        customer: {
          name: fallbackProfile.name,
          email: fallbackProfile.email,
          contact: fallbackProfile.phone,
        },
        callback_url: `${window.location.origin}/payment-success?materialId=${material.id}&userId=${material.userId}`,
        callback_method: 'get',
      };

      const successUrl = `${window.location.origin}/payment-success?status=success&materialId=${material.id}&userId=${material.userId}&amount=${material.price}`;
      window.open(successUrl, '_blank');

      setTimeout(() => {
        onSuccess({
          materialId: material.id,
          userId: material.userId,
          amount: material.price,
          paymentMethod: 'payment_link',
        });
      }, 2000);
    } catch (error) {
      console.error('Payment link error:', error);
      onError(error);
    }
  }

  // Cashfree integration
  async initiateCashfreePayment(
    material: any,
    profile: UserProfileMinimal,
    onSuccess: (response: any) => void,
    onError: (error: any) => void
  ): Promise<void> {
    try {
      if (typeof (window as any).Cashfree === 'undefined') {
        throw new Error('Cashfree script not loaded');
      }

      // Get fallback values for missing fields
      const fallbackProfile = this.getFallbackValues(profile);

      const orderId = this.generateOrderId(material.id, material.userId || 'user_id');
      const appUrl = import.meta.env.VITE_APP_URL || window.location.origin;

      const cashfree = (window as any).Cashfree({
        mode: this.config.cashfree.mode,
      });

      const checkoutOptions = {
        paymentSessionId: `session_${orderId}`,
        returnUrl: `${appUrl}/payment-success?materialId=${material.id}&userId=${material.userId}`,
        customerDetails: {
          customerId: (material.userId || 'user_id').toString(),
          customerName: fallbackProfile.name,
          customerEmail: fallbackProfile.email,
          customerPhone: fallbackProfile.phone,
        },
        orderDetails: {
          orderId: orderId,
          orderAmount: material.price,
          orderCurrency: 'INR',
          orderNote: `Purchase: ${material.title}`,
        },
        theme: {
          color: '#2563eb',
        },
      };

      this.simulateCashfreeCheckout(material, profile, orderId, onSuccess, onError);
    } catch (error) {
      console.error('Cashfree initialization error:', error);
      onError(error);
    }
  }

  // Simulate Cashfree checkout for demo
  private simulateCashfreeCheckout(
    material: any,
    profile: UserProfileMinimal,
    orderId: string,
    onSuccess: (response: any) => void,
    onError: (error: any) => void
  ): void {
    // Get fallback values for missing fields
    const fallbackProfile = this.getFallbackValues(profile);

    const modal = document.createElement('div');
    modal.style.cssText = `
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: rgba(0,0,0,0.5);
      display: flex;
      justify-content: center;
      align-items: center;
      z-index: 10000;
    `;

    const content = document.createElement('div');
    content.style.cssText = `
      background: white;
      padding: 30px;
      border-radius: 10px;
      max-width: 400px;
      width: 90%;
      text-align: center;
      box-shadow: 0 10px 25px rgba(0,0,0,0.2);
    `;

    content.innerHTML = `
      <div style="margin-bottom: 20px;">
        <h3 style="color: #2563eb; margin-bottom: 10px;">Cashfree Payment</h3>
        <p style="color: #666; margin-bottom: 15px;">Complete your payment for:</p>
        <p style="font-weight: bold; color: #333;">${material.title}</p>
        <p style="font-size: 24px; color: #2563eb; font-weight: bold;">₹${material.price}</p>
      </div>
      
      <div style="margin-bottom: 20px;">
        <button id="upi-pay" style="
          background: #00D4AA;
          color: white;
          border: none;
          padding: 12px 24px;
          border-radius: 6px;
          margin: 5px;
          cursor: pointer;
          font-weight: bold;
        ">Pay with UPI</button>
        
        <button id="card-pay" style="
          background: #2563eb;
          color: white;
          border: none;
          padding: 12px 24px;
          border-radius: 6px;
          margin: 5px;
          cursor: pointer;
          font-weight: bold;
        ">Pay with Card</button>
      </div>
      
      <div style="margin-top: 20px;">
        <button id="cancel-payment" style="
          background: #dc2626;
          color: white;
          border: none;
          padding: 10px 20px;
          border-radius: 6px;
          cursor: pointer;
        ">Cancel</button>
      </div>
    `;

    modal.appendChild(content);
    document.body.appendChild(modal);

    content.querySelector('#upi-pay')?.addEventListener('click', () => {
      const merchantUPI = import.meta.env.VITE_UPI_ID || 'merchant@paytm';
      const upiUrl = `upi://pay?pa=${merchantUPI}&pn=Brainstorm&am=${material.price}&cu=INR&tn=Purchase ${material.title}&tr=${orderId}`;

      if (navigator.userAgent.match(/Android|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i)) {
        window.location.href = upiUrl;
        setTimeout(() => {
          document.body.removeChild(modal);
          onSuccess({
            orderId: orderId,
            cf_payment_id: `cf_${Date.now()}`,
            materialId: material.id,
            userId: material.userId || 'user_id',
            amount: material.price,
            paymentMethod: 'UPI',
            gateway: 'cashfree',
          });
        }, 3000);
      } else {
        alert('UPI payments are supported on mobile devices only. Please try card payment.');
      }
    });

    content.querySelector('#card-pay')?.addEventListener('click', () => {
      const cardModal = this.createCardPaymentModal(material, profile, orderId, onSuccess, onError);
      document.body.removeChild(modal);
      document.body.appendChild(cardModal);
    });

    content.querySelector('#cancel-payment')?.addEventListener('click', () => {
      document.body.removeChild(modal);
      onError(new Error('Payment cancelled by user'));
    });
  }

  // Create card payment modal
  private createCardPaymentModal(
    material: any,
    profile: UserProfileMinimal,
    orderId: string,
    onSuccess: (response: any) => void,
    onError: (error: any) => void
  ): HTMLElement {
    // Get fallback values for missing fields
    const fallbackProfile = this.getFallbackValues(profile);

    const modal = document.createElement('div');
    modal.style.cssText = `
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: rgba(0,0,0,0.5);
      display: flex;
      justify-content: center;
      align-items: center;
      z-index: 10000;
    `;

    const content = document.createElement('div');
    content.style.cssText = `
      background: white;
      padding: 30px;
      border-radius: 10px;
      max-width: 400px;
      width: 90%;
      box-shadow: 0 10px 25px rgba(0,0,0,0.2);
    `;

    content.innerHTML = `
      <h3 style="color: #2563eb; margin-bottom: 20px; text-align: center;">Card Payment</h3>
      <form id="card-form">
        <div style="margin-bottom: 15px;">
          <label style="display: block; margin-bottom: 5px; font-weight: bold;">Card Number</label>
          <input type="text" placeholder="1234 5678 9012 3456" maxlength="19" style="
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 16px;
          " required>
        </div>
        
        <div style="display: flex; gap: 10px; margin-bottom: 15px;">
          <div style="flex: 1;">
            <label style="display: block; margin-bottom: 5px; font-weight: bold;">Expiry</label>
            <input type="text" placeholder="MM/YY" maxlength="5" style="
              width: 100%;
              padding: 10px;
              border: 1px solid #ddd;
              border-radius: 4px;
              font-size: 16px;
            " required>
          </div>
          <div style="flex: 1;">
            <label style="display: block; margin-bottom: 5px; font-weight: bold;">CVV</label>
            <input type="text" placeholder="123" maxlength="3" style="
              width: 100%;
              padding: 10px;
              border: 1px solid #ddd;
              border-radius: 4px;
              font-size: 16px;
            " required>
          </div>
        </div>
        
        <div style="margin-bottom: 20px;">
          <label style="display: block; margin-bottom: 5px; font-weight: bold;">Cardholder Name</label>
          <input type="text" value="${fallbackProfile.name}" style="
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 16px;
          " required>
        </div>
        
        <div style="text-align: center; margin-bottom: 15px;">
          <p style="font-weight: bold; color: #333;">Amount: ₹${material.price}</p>
        </div>
        
        <div style="display: flex; gap: 10px;">
          <button type="submit" style="
            flex: 1;
            background: #2563eb;
            color: white;
            border: none;
            padding: 12px;
            border-radius: 6px;
            cursor: pointer;
            font-weight: bold;
          ">Pay Now</button>
          
          <button type="button" id="cancel-card" style="
            flex: 1;
            background: #dc2626;
            color: white;
            border: none;
            padding: 12px;
            border-radius: 6px;
            cursor: pointer;
          ">Cancel</button>
        </div>
      </form>
    `;

    modal.appendChild(content);

    content.querySelector('#card-form')?.addEventListener('submit', (e) => {
      e.preventDefault();
      const submitBtn = content.querySelector('button[type="submit"]') as HTMLButtonElement;
      submitBtn.textContent = 'Processing...';
      submitBtn.disabled = true;

      setTimeout(() => {
        document.body.removeChild(modal);
        onSuccess({
          orderId: orderId,
          cf_payment_id: `cf_card_${Date.now()}`,
          materialId: material.id,
          userId: material.userId || 'user_id',
          amount: material.price,
          paymentMethod: 'Card',
          gateway: 'cashfree',
        });
      }, 2000);
    });

    content.querySelector('#cancel-card')?.addEventListener('click', () => {
      document.body.removeChild(modal);
      onError(new Error('Payment cancelled by user'));
    });

    return modal;
  }
}
