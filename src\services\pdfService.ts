import { API_CONFIG } from '../config/api';

export interface PDFDocument {
  id: string;
  user_id: number;
  original_file_name: string;
  cloudinary_public_id: string;
  secure_url: string;
  file_size: number;
  status: string;
  content_type: string;
  upload_duration_ms: number;
  created_at: string;
  updated_at: string;
}

export class PDFService {
  private baseUrl = API_CONFIG.PDF_SERVICE_URL;

  async uploadPDF(file: File, userId: string, category: string = 'study-material'): Promise<PDFDocument> {
    const formData = new FormData();
    formData.append('file', file);
    formData.append('userId', userId);
    formData.append('category', category);

    const response = await fetch(`${this.baseUrl}/api/v1/pdf/upload`, {
      method: 'POST',
      body: formData
    });

    if (!response.ok) {
      const error = await response.text();
      throw new Error(`Upload failed: ${error}`);
    }

    const result = await response.json();
    return result.transaction;
  }

  async getUserPDFs(userId: string): Promise<PDFDocument[]> {
    const response = await fetch(`${this.baseUrl}/api/v1/pdf/user/${userId}/all`);

    if (!response.ok) {
      throw new Error('Failed to fetch PDFs');
    }

    const result = await response.json();
    return result.pdfs;
  }

  async getUserPDFsPaginated(userId: string, page: number = 0, size: number = 10): Promise<PDFDocument[]> {
    const response = await fetch(`${this.baseUrl}/api/v1/pdf/user/${userId}?page=${page}&size=${size}`);

    if (!response.ok) {
      throw new Error('Failed to fetch PDFs');
    }

    const result = await response.json();
    return result.pdfs;
  }
}

export const pdfService = new PDFService();
