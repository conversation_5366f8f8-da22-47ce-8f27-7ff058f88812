import { API_CONFIG, API_ENDPOINTS, apiUtils } from '../config/api';

export interface StudyMaterial {
  id: string;
  title: string;
  description: string;
  type: string;
  subject: string;
  price: number;
  originalPrice?: number;
  rating?: number;
  reviews?: number;
  pages?: number;
  author: string;
  isPremium: number;
  thumbnailUrl?: string;
  status: string;
  downloads: number;
  createdAt: string;
  fileSize: string;
  filePath: string;
  originalName: string;
}

export interface UploadResponse {
  id: string;
  title: string;
  description: string;
  type: string;
  subject: string;
  price: number;
  originalPrice?: number;
  rating?: number;
  reviews?: number;
  pages?: number;
  author: string;
  isPremium: number;
  thumbnailUrl?: string;
  status: string;
  downloads: number;
  createdAt: string;
  fileSize: string;
  filePath: string;
  originalName: string;
  createdAtAsDate: number;
}

export class StudyMaterialService {
  async uploadStudyMaterial(
    file: File, 
    materialData: {
      title: string;
      description: string;
      subject: string;
      price: string;
      author: string;
      tags: string;
      isPremium: string;
    },
    authToken: string
  ): Promise<UploadResponse> {
    const formData = new FormData();
    
    // Add file
    formData.append('file', file);
    
    // Add all material data
    formData.append('title', materialData.title);
    formData.append('description', materialData.description);
    formData.append('subject', materialData.subject);
    formData.append('price', materialData.price);
    formData.append('author', materialData.author);
    formData.append('tags', materialData.tags);
    formData.append('isPremium', materialData.isPremium);

    try {
      const response = await fetch(`${API_CONFIG.API_BASE_URL}/studyMaterials/upload`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${authToken}`,
          // Don't set Content-Type for FormData, let browser set it with boundary
        },
        body: formData,
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`Upload failed: ${response.status} - ${errorText}`);
      }

      const result = await response.json();
      console.log('Study material upload result:', result);
      return result;
    } catch (error) {
      console.error('Study material upload error:', error);
      throw error;
    }
  }

  async getAllStudyMaterials(): Promise<StudyMaterial[]> {
    try {
      const response = await apiUtils.get(API_ENDPOINTS.STUDY_MATERIALS);
      if (!response.ok) {
        throw new Error('Failed to fetch study materials');
      }
      return await response.json();
    } catch (error) {
      console.error('Error fetching study materials:', error);
      throw error;
    }
  }

  async getStudyMaterialById(id: string): Promise<StudyMaterial> {
    try {
      const response = await apiUtils.get(`${API_ENDPOINTS.STUDY_MATERIALS}/${id}`);
      if (!response.ok) {
        throw new Error('Failed to fetch study material');
      }
      return await response.json();
    } catch (error) {
      console.error('Error fetching study material:', error);
      throw error;
    }
  }

  async deleteStudyMaterial(id: string): Promise<void> {
    try {
      const response = await apiUtils.delete(`${API_ENDPOINTS.STUDY_MATERIALS}/${id}`);
      if (!response.ok) {
        throw new Error('Failed to delete study material');
      }
    } catch (error) {
      console.error('Error deleting study material:', error);
      throw error;
    }
  }
}

export const studyMaterialService = new StudyMaterialService();
