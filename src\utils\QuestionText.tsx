import React from "react";

type MarkerType = "bullet" | "dash" | "star" | "none";

type Props = {
  text: string;
  wrapperClassName?: string; // wrapper styling
  paragraphClassName?: string; // default text styling
  addLineMarkers?: boolean; // default true
  markerType?: MarkerType; // which marker symbol
};

export default function QuestionText({
  text,
  wrapperClassName = "",
  paragraphClassName = "text-lg text-gray-900 leading-relaxed font-medium",
  addLineMarkers = true,
  markerType = "bullet",
}: Props) {
  const nodes = renderNodesFromText(text ?? "", paragraphClassName, addLineMarkers, markerType);
  return <div className={wrapperClassName}>{nodes}</div>;
}

/* -------------------------
   Core rendering pipeline
   ------------------------- */
function renderNodesFromText(
  text: string,
  pClass: string,
  addLineMarkers: boolean,
  markerType: MarkerType
): React.ReactNode[] {
  // normalize literal "\n" and CRLF, trim outer whitespace
  const normalized = text.replace(/\\n/g, "\n").replace(/\r\n/g, "\n").trim();

  if (!normalized) return [];

  // split into "paragraph blocks" by 2+ newlines
  const paragraphs = normalized.split(/\n{2,}/);

  const result: React.ReactNode[] = [];
  let key = 0;

  for (const para of paragraphs) {
    const parts = parseParagraph(para, pClass, addLineMarkers, markerType, key);
    parts.forEach((part) => {
      result.push(
        React.isValidElement(part)
          ? React.cloneElement(part, { key: key++ })
          : <React.Fragment key={key++}>{part}</React.Fragment>
      );
    });
  }

  return result;
}

/**
 * parseParagraph:
 * Handles:
 * - Explicit list markers (-, *, +, •, 1., 1))
 * - Inline numbered lists
 * - Multi-line short lines (with markers if enabled)
 * - Fallback plain text
 */
function parseParagraph(
  para: string,
  pClass: string,
  addLineMarkers: boolean,
  markerType: MarkerType,
  baseKey: number
): React.ReactNode[] {
  const nodes: React.ReactNode[] = [];
  const lines = para
    .split("\n")
    .map((l) => l.trim())
    .filter(Boolean);

  if (lines.length === 0) return [];

  // explicit list markers
  const listLineRegex = /^\s*(?:[-*+•]|\d+[.)])\s+/;

  // If any line starts like list → parse as list
  if (lines.some((l) => listLineRegex.test(l))) {
    let i = 0;
    while (i < lines.length) {
      if (listLineRegex.test(lines[i])) {
        const items: string[] = [];
        const firstLine = lines[i];
        const ordered = /^\s*\d+[.)]\s+/.test(firstLine);

        while (i < lines.length && listLineRegex.test(lines[i])) {
          const text = lines[i].replace(/^\s*(?:[-*+•]|\d+[.)])\s+/, "").trim();
          if (text) items.push(text);
          i++;
        }

        nodes.push(
          ordered ? (
            <ol key={`${baseKey}-ol-${i}`} className="list-decimal ml-6">
              {items.map((it, idx) => (
                <li className="mb-2" key={idx}>
                  <span className={pClass}>{it}</span>
                </li>
              ))}
            </ol>
          ) : (
            <ul key={`${baseKey}-ul-${i}`} className="list-disc ml-6">
              {items.map((it, idx) => (
                <li className="mb-2" key={idx}>
                  <span className={pClass}>{it}</span>
                </li>
              ))}
            </ul>
          )
        );
      } else {
        // non-list segment
        const segLines: string[] = [];
        while (i < lines.length && !listLineRegex.test(lines[i])) {
          segLines.push(lines[i]);
          i++;
        }
        const segText = segLines.join(" ").trim();
        if (segText) {
          nodes.push(...renderParagraphText(segText, pClass, addLineMarkers, markerType, baseKey + i));
        }
      }
    }
    return nodes;
  }

  // No explicit block-list markers → try inline numbered list
  return renderParagraphText(para, pClass, addLineMarkers, markerType, baseKey);
}

/**
 * renderParagraphText:
 * Handles inline numbering, multi-line small lines, or single paragraph.
 */
function renderParagraphText(
  text: string,
  pClass: string,
  addLineMarkers: boolean,
  markerType: MarkerType,
  key: number
): React.ReactNode[] {
  const nodes: React.ReactNode[] = [];

  const inlineSplit = splitInlineNumbered(text);
  if (inlineSplit.items.length > 0) {
    if (inlineSplit.before) nodes.push(<p key={`${key}-before`} className={pClass}>{inlineSplit.before}</p>);
    nodes.push(
      <ol key={`${key}-inline-ol`} className="list-decimal ml-6">
        {inlineSplit.items.map((it, idx) => (
          <li className="mb-2" key={idx}>
            <span className={pClass}>{it}</span>
          </li>
        ))}
      </ol>
    );
    return nodes;
  }

  // If multiple short lines → add markers if enabled
  const finalLines = text.split("\n").map((l) => l.trim()).filter(Boolean);

  if (finalLines.length === 1) {
    nodes.push(<p key={`${key}-p`} className={pClass}>{finalLines[0]}</p>);
  } else {
    if (addLineMarkers) {
      const symbol = getMarkerSymbol(markerType);
      nodes.push(
        <ul key={`${key}-ul`} className="list-none ml-0">
          {finalLines.map((it, idx) => (
            <li className="flex items-start gap-3 mb-2" key={idx}>
              <span className="mt-1 text-gray-400">{symbol}</span>
              <span className={pClass}>{it}</span>
            </li>
          ))}
        </ul>
      );
    } else {
      finalLines.forEach((ln, idx) =>
        nodes.push(
          <p key={`${key}-line-${idx}`} className={pClass}>
            {ln}
          </p>
        )
      );
    }
  }

  return nodes;
}

/**
 * splitInlineNumbered:
 * Detects inline "1. text 2. text" patterns inside a single paragraph
 */
function splitInlineNumbered(para: string): { before: string | null; items: string[] } {
  const regex = /(\d+)[.)](?!\d)/g;
  const matches = [...para.matchAll(regex)];
  if (matches.length === 0) return { before: para, items: [] };

  const realMatches = matches.filter((m) => {
    const idx = m.index!;
    if (idx === 0) return true;
    const prev = para[idx - 1];
    return /[\s:;(\[\{>\-]/.test(prev);
  });

  if (realMatches.length === 0) return { before: para, items: [] };

  const items: string[] = [];
  const firstIndex = realMatches[0].index!;
  const before = para.slice(0, firstIndex).trim() || null;

  for (let i = 0; i < realMatches.length; i++) {
    const match = realMatches[i];
    const nextMatch = realMatches[i + 1];
    let start = match.index! + match[0].length;
    while (para[start] === " ") start++;
    const end = nextMatch ? nextMatch.index! : para.length;
    const item = para.slice(start, end).trim();
    if (item) items.push(item);
  }

  return { before, items };
}

/** marker symbols */
function getMarkerSymbol(type: MarkerType): string {
  switch (type) {
    case "bullet":
      return "•";
    case "dash":
      return "—";
    case "star":
      return "✱";
    case "none":
      return "";
    default:
      return "•";
  }
}
